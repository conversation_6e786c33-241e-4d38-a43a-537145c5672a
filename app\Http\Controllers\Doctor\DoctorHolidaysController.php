<?php

namespace App\Http\Controllers\Doctor;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Services\DoctorService\IDoctorService;
use App\Http\Requests\Doctor\AddHolidayRequest;
use App\Http\Requests\Doctor\DeleteHolidayRequest;
use App\Models\Holiday;

class DoctorHolidaysController extends Controller
{

    protected $doctorService;
    public function __construct(IDoctorService $doctorService)
    {
        $this->doctorService = $doctorService;
    }
    public function index()
    {
        $holidays = Holiday::where('user_id','=',Auth::id())->get();
        return view('pages.user-accounts.doctor.holidays.index', compact('holidays'));
    }
    public function store_holiday(AddHolidayRequest $req)
    {
        $this->doctorService->addHoliday(
            $req->validated()
        );

        return redirect()->back()->with([
            'success' => trans("alerts.success.your_holiday_have_been_saved_successfully")
        ]);
    }
    public function destory_holiday(DeleteHolidayRequest $req)
    {
        $this->doctorService->deleteHoliday($req->id);

        return response()->json([
            'success' => trans("alerts.success.holiday_deleted")
        ]);
    }
}

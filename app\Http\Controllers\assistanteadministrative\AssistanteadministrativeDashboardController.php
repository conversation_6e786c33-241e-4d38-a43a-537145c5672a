<?php

namespace App\Http\Controllers\assistanteadministrative;

use App\Http\Controllers\Controller;
use App\Services\AppointmentService\IAppointmentService;
use App\Services\DoctorService\IDoctorService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class AssistanteadministrativeDashboardController extends Controller
{
    protected $appointmentService;
    protected $doctorService;

    public function __construct(IAppointmentService $appointmentService, IDoctorService $doctorService)
    {
        $this->appointmentService = $appointmentService;
        $this->doctorService = $doctorService;
    }

    public function index()
    {
        $doctorId = Auth::user()->assistante->doctor->id ?? null;
        if ($doctorId) {
            session(['selected_doctor_id' => $doctorId]);
        }

        $appointmentRequests = $this->appointmentService->getDoctorAppointmentRequests($doctorId);
        $doctorStats = $this->doctorService->getStats($doctorId);

        return view(
            'pages.user-accounts.assistante-administrative.dashboard',
            compact('appointmentRequests', 'doctorId') + $doctorStats
        );
    }

    public function indexWithDoctor($doctorId)
    {
        session(['selected_doctor_id' => $doctorId]);

        $appointmentRequests = $this->appointmentService->getDoctorAppointmentRequests($doctorId);
        $doctorStats = $this->doctorService->getStats($doctorId);

        return view(
            'pages.user-accounts.assistante-administrative.dashboard',
            compact('appointmentRequests', 'doctorId') + $doctorStats
        );
    }

    public function indexWithClinic($clinicid)
    {
        session(['selected_clinic_id' => $clinicid]);
        $appointmentRequests = $this->appointmentService->GetClinicAppointements($clinicid);
      
        Log::info('appt req', ['appointmentRequests' => $appointmentRequests]);
            // dd($appointmentRequests);
        return view(
            'pages.user-accounts.assistante-administrative.dashboard',
            compact('clinicid', 'appointmentRequests')
        );
    }

    public function appointmentsWithDoctor($doctorId)
    {
        session(['selected_doctor_id' => $doctorId]);

        $confirmedAppointments = $this->appointmentService->getDoctorConfirmedAppointments($doctorId);
        
        return view('pages.user-accounts.assistante-administrative.appointments', $confirmedAppointments);
    }
}

<?php

namespace App\Http\Controllers\Common;

use App\Http\Controllers\Controller;
use App\Http\Requests\Review\CreateReplyRequest;
use App\Http\Requests\Review\CreateReviewRequest;
use App\Services\ReviewService\IReviewService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ReviewsController extends Controller
{
    protected $reviewService;

    public function __construct(IReviewService $reviewService)
    {
        $this->reviewService = $reviewService;
    }

    public function index($doctorId = null)
    {
        
         $reviews = $this->reviewService->all();
        if (auth()->user()->role == 'doctor') {
            $reviews = $this->reviewService->all();
            return view('pages.user-accounts.doctor.reviews', compact('reviews'));
        } elseif (auth()->user()->role == 'assistante') {
            $doctorId = auth()->user()->assistante->doctor->id ?? $doctorId;
            $reviews = $this->reviewService->getByDoctor($doctorId);
            return view('pages.user-accounts.assistante-administrative.reviews', compact('reviews'));
        }
    //    dd($reviews);
        return view('pages.admin.reviews.index', compact('reviews'));
    }

    public function store(CreateReviewRequest $req)
    {
        $this->reviewService->create($req->validated());
        return redirect()->back()->with(
            'success',
            trans("alerts.success.your_review_has_been_added_successfully")
        )->withFragment('reviews');
    }
    public function reply(CreateReplyRequest $req)
    {
        $this->reviewService->reply($req->reply, $req->review_id);
        return redirect()->back()->with(
            'success',
            trans("alerts.success.your_reply_has_been_added_successfully")
        );
    }

    public function load_more($id, Request $req)
    {
        $limit = 3;
        $reviews = $this->reviewService->loadMore($id, $limit);
        $reviewsLeft = $reviews->total() - ($reviews->currentPage() * $reviews->perPage());
        $reviewsLeft = $reviewsLeft <= 0 ? 0 : $reviewsLeft;
        $view = view(
            'pages.user-accounts.doctor.profile.sections.reviews.sections.load-more',
            compact('reviews', 'reviewsLeft')
        )->render();

        return $view;
    }
    public function destroy($id)
    {
        $this->reviewService->delete($id);

        return redirect()->back()->with([
            'success' => trans("alerts.success.review_deleted_successfully")
        ]);
    }
}

<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class BlockSpecificMedecinQueries
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $path = $request->path();
        if ($path === 'medecin') {
            $blockedParams = ['speciality', 'subspeciality', 'city', 'district'];
            foreach ($blockedParams as $param) {
                if ($request->has($param)) {
                    return redirect('/medecin');
                }
            }
        }
        return $next($request);
    }
}

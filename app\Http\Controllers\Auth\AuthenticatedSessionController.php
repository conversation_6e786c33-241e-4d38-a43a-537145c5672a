<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\View\View;
use Artesaos\SEOTools\Facades\SEOMeta;
use Artesaos\SEOTools\Facades\OpenGraph;
use Artesaos\SEOTools\Facades\JsonLd;

// use SEOMeta;
// use OpenGraph;
// use JsonLd;

class AuthenticatedSessionController extends Controller
{
    /**
     * Display the login view.
     */
    public function create(): View
    {
// Set SEO meta for login page
SEOMeta::setTitle('Se Connecter à Mon Compte | DocExpress');
SEOMeta::setDescription('Accédez à votre compte en toute sécurité pour gérer vos rendez-vous et services.');
SEOMeta::addKeyword([
    'se connecter',
    'connexion DocExpress',
    'compte DocExpress',
    'accéder à mon compte',
    'connexion patient DocExpress'
]);
SEOMeta::setCanonical(url()->current());

// OpenGraph metadata
OpenGraph::setTitle('Se Connecter à Mon Compte | DocExpress');
OpenGraph::setDescription('Accédez à votre compte en toute sécurité pour gérer vos rendez-vous et services.');
OpenGraph::setUrl(url()->current());

// JSON-LD metadata
JsonLd::setTitle('Se Connecter à Mon Compte | DocExpress');
JsonLd::setDescription('Accédez à votre compte en toute sécurité pour gérer votre compte et accéder à nos services.');
JsonLd::setUrl(url()->current());

        return view('pages.auth.login');
    }

    /**
     * Handle an incoming authentication request.
     */
    public function store(LoginRequest $request): RedirectResponse
    {
        $request->authenticate();
        $request->session()->regenerate();

        // Get intended URL if exists
        $intended = session()->pull('url.intended');

        if ($intended && str_contains($intended, '/centre/')) {
            return redirect()->to($intended);
        }

        // Default role-based redirects
        if (Auth::user()->role == "patient") {
            return redirect()->intended('patient/dashboard');
        }

        if (Auth::user()->role == "doctor") {
            if (Auth::user()->doctor->offre == 'basic')
                return redirect()->intended('/doctor/settings');
            else
                return redirect()->intended('doctor/dashboard');
        }
        if (Auth::user()->role == "pharmacy") {
            return redirect()->intended('pharmacy/dashboard');
        }
        if (Auth::user()->role == "assistante") {
            if (Auth::user()->assistante->type == 'accuille_clinique' || Auth::user()->assistante->type == 'service_clinique')
            return redirect()->intended('/assistante/clinics');
        else
        return redirect()->intended('/assistante/doctors');
        }
        if (Auth::user()->role == "centre_medical") {
            return redirect()->intended('centre-medical/dashboard');
        }
        if (Auth::user()->role == "admin") {
            return redirect()->intended('admin');
        }
    }

    /**
     * Destroy an authenticated session.
     */
    public function destroy(Request $request): RedirectResponse
    {
        $locale =  Session::get('locale');

        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        Session::put('locale', $locale);

        return redirect('/');
    }
}

<?php

namespace App\Http\Controllers\Doctor;

use App\Http\Controllers\Controller;
use App\Http\Requests\MedicalCertificate\CreateMedicalCertificateRequest;
use App\Services\MedicalCertificateService\IMedicalCertificateService;
use App\Models\MedicalCertificate;
use Illuminate\Support\Facades\Storage;
use Barryvdh\DomPDF\Facade\Pdf;

class MedicalCertificateController extends Controller
{

    protected $medicalCertificateService;

    public function __construct(
        IMedicalCertificateService $medicalCertificateService
    ) {
        $this->medicalCertificateService = $medicalCertificateService;
    }
    // public function store(CreateMedicalCertificateRequest $request)
    // {
    //     $data = $request->validated();
    //     if (empty($data))
    //         return;
    //     $document = $this->medicalCertificateService->create($data);
    //     return $document->stream('document.pdf');
    // }
    public function store(CreateMedicalCertificateRequest $request)
{
    $data = $request->validated();

    // Make sure patient_id is set
    if (!isset($data['patient_id']) || empty($data['patient_id'])) {
        $data['patient_id'] = $request->patient_id ?? auth()->user()->patient->id;
    }

    // If a dependent is selected, map it to the correct column name
    if (isset($data['dependent_id'])) {
        $data['depeBndent_id'] = $data['dependent_id'];
        unset($data['dependent_id']); // remove wrong key
    }

    // create record + PDF
    $result = $this->medicalCertificateService->create($data);
    $medicalCertificate = $result['model'];
    $pdf = $result['pdf'];

    $fileName = "medicalCertificate-" . time() . ".pdf";
    $filePath = "medical_certificates/" . $fileName;

    Storage::disk('public')->put($filePath, $pdf->output());

    $medicalCertificate->update([
        'file_path' => $filePath
    ]);

    return $pdf->download($fileName);
}



    public function downloadMedicalCertificate($id)
    {
        $medicalCertificate = $this->medicalCertificateService->get($id);
        $patient = $this->medicalCertificateService->get($medicalCertificate->patient_id);

        $pdf = Pdf::loadView(
            'pages.user-accounts.doctor.components.template-docs.prescription-template',
            [
                'patient' => $patient, 'address' => $medicalCertificate->doctor->address,
                'prescription' => $medicalCertificate,
            ]
        )->setPaper('a5');
        return $pdf->download("medicalCertificate-" . time() . ".pdf");
    }
    public function download($id)
    {
        $medicalCertificate = MedicalCertificate::findOrFail($id);

        if (empty($medicalCertificate->file_path) || !Storage::disk('public')->exists($medicalCertificate->file_path)) {
            abort(404, 'File not found.');
        }

        return Storage::disk('public')->download($medicalCertificate->file_path);
    }


}

<?php

namespace App\Http\Controllers\Admin;



use App\Models\Doctor;
use App\Models\Payment;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Services\DoctorService\IDoctorService;
use App\Repositories\SpecialityRepositoryInterface;

class PaymentController extends Controller
{
    protected $doctorService;
    protected $specialityRepository;

    public function __construct(IDoctorService $doctorService, SpecialityRepositoryInterface $specialityRepository)
    {
        $this->doctorService = $doctorService;
        $this->specialityRepository = $specialityRepository;
    }

    public function index()
    {
        $doctors = $this->doctorService->all();

        return view('pages.admin.payment.index', compact('doctors'));
    }

    public function profile($id)
    {
        $payments = Payment::where('doctor_id', $id)->get();
        $doctor = Doctor::find($id);
        return view('pages.admin.payment.table-payment', compact('payments', 'doctor'));
    }
    public function edit(Request $req)
    {

        $payment = Payment::find($req->id);
        return response()->json($payment);

    }

    public function update(Request $req)
    {

        $req->validate([
            'status' => 'required|in:paid,no paid',
        ]);

        $payment = Payment::find($req->id);

        $payment->status = $req->status;
        $payment->save();

        return redirect()->back()->with('success', 'Payment status updated successfully!');
    }


}

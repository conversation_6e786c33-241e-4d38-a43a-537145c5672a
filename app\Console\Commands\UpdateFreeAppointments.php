<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\UpdateFreeAppointmentCache;

class UpdateFreeAppointments extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'appointments:update-free-cache';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update the cache for free appointment slots for all doctors';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Dispatching free appointment cache update job...');
        
        UpdateFreeAppointmentCache::dispatch();
        
        $this->info('Free appointment cache update job dispatched successfully!');
        
        return 0;
    }
}

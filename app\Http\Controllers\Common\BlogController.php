<?php

namespace App\Http\Controllers\Common;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class BlogController extends Controller
{
    public function index()
    {
        // You can add logic here to fetch blog posts
        return view('pages.blog.index');
    }
    public function speciality()
   {
    $specialities = \App\Models\Speciality::with('subSpecialities')->get();
    return view('pages.blog.all-speciality', compact('specialities'));
   }
    public function AboutUs()
    {
        return view('pages.blog.AboutUs');
    }
}

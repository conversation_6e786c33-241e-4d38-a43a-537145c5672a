<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class UserOffreMiddleware
{
    public function handle(Request $request, Closure $next, ...$allowedProfiles)
    {
        $userProfile = $this->getUserProfile($request);

        if (in_array($userProfile, $allowedProfiles)) {
            return $next($request);
        }
        return abort(401, 'Non autorisé');
    }
    private function getUserProfile(Request $request)
    {
        if (!Auth::check()) {
            return null;
        }
        $user = Auth::user();
        if ($user->role == "doctor" && $user->doctor) {
            return $user->doctor->offre;
        }
        if ($user->role == "assistante") {
            return $this->getAssistanteProfile($request, $user);
        }
        return null;
    }
    private function getAssistanteProfile(Request $request, $user)
    {
        $selectedDoctorId = $request->route('doctor_id');
        $doctor = $user->assistante->doctors()->find($selectedDoctorId);
        if (!$doctor) {
            return null;
        }
        return $doctor->offre;
    }
}

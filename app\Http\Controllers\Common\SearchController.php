<?php

namespace App\Http\Controllers\Common;

use App\Http\Controllers\Controller;
use App\Models\Doctor;
use App\Models\Speciality;
use App\Models\SubSpeciality;
use App\Models\Pharmacy;
use App\Models\CentreMedical;
use Illuminate\Http\Request;
use Artesaos\SEOTools\Facades\SEOMeta;
use Artesaos\SEOTools\Facades\OpenGraph;
use Artesaos\SEOTools\Facades\JsonLd;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class SearchController extends Controller
{    public function index(Request $request)
{
    if ($request->has('speciality')) {
        $params = $request->all();
        $params['subspeciality'] = $params['speciality'];
        unset($params['speciality']);
        return redirect()->route('search.index', $params, 301);
    }

    // Check if any search parameters are provided, then redirect to SEO URL
    $subspeciality = $request->input('subspeciality');
    $city = $request->input('city');
    $district = $request->input('district');
    $gender = $request->input('gender');
    $firstName = $request->input('first_name');
    $lastName = $request->input('last_name');
    $clinic = $request->input('clinic');    // If searching by name only, use query parameters with French names
    if ($firstName || $lastName) {
        $queryParams = [];

        if ($firstName) {
            $queryParams['prenom'] = $this->sanitizeUrlParam($firstName);
        }

        if ($lastName) {
            $queryParams['nom'] = $this->sanitizeUrlParam($lastName);
        }

        $queryString = http_build_query($queryParams);
        return redirect('/medecin?' . $queryString);
    }    // For location/specialty/gender/clinic searches, use path-based SEO URLs
    if ($subspeciality || $city || $district || $gender || $clinic) {
        // Build SEO URL with flexible structure
        $urlParts = ['medecin'];

        // Only add parameters that have values, in order of priority
        if ($subspeciality) {
            // Find the subspeciality name and replace spaces/special chars with hyphens
            $subSpec = SubSpeciality::find($subspeciality);
            if ($subSpec) {
                $urlParts[] = $this->sanitizeUrlParam($subSpec->name);
            }
        }

        if ($city) {
            $urlParts[] = $this->sanitizeUrlParam($city);
        }

        if ($district) {
            $urlParts[] = $this->sanitizeUrlParam($district);
        }

        if ($gender) {
            $urlParts[] = $gender;
        }

        $seoUrl = '/' . implode('/', $urlParts);
        return redirect($seoUrl);
    }    return $this->seoSearch();
}    /**
     * Handle /medecin with query parameters (for name searches and combined filters)
     */
    public function medecinSearch(Request $request)
    {
        // Map French parameter names to English ones for the search
        if ($request->has('prenom')) {
            $request->merge(['first_name' => $request->input('prenom')]);
        }

        if ($request->has('nom')) {
            $request->merge(['last_name' => $request->input('nom')]);
        }

        if ($request->has('specialite')) {
            $request->merge(['subspeciality' => $request->input('specialite')]);
        }

        if ($request->has('ville')) {
            $request->merge(['city' => $request->input('ville')]);
        }

        if ($request->has('quartier')) {
            $request->merge(['district' => $request->input('quartier')]);
        }

        if ($request->has('sexe')) {
            $request->merge(['gender' => $request->input('sexe')]);
        }

        if ($request->has('clinique')) {
            $request->merge(['clinic' => $request->input('clinique')]);
        }

        return $this->seoSearch();
    }

    /**
     * Sanitize URL parameter by replacing spaces and special characters with hyphens
     */
    private function sanitizeUrlParam($param)
    {
        // For city names, we need to preserve existing hyphens and only replace spaces
        // First normalize the string to handle accented characters
        $normalized = $this->normalizeString($param);
        
        // Convert to lowercase first
        $result = strtolower($normalized);
        
        // Handle accented characters
        $result = str_replace(['é', 'è', 'ê', 'ë'], 'e', $result);
        $result = str_replace(['à', 'â', 'ä', 'á'], 'a', $result);
        $result = str_replace(['ù', 'û', 'ü', 'ú'], 'u', $result);
        $result = str_replace(['î', 'ï', 'í'], 'i', $result);
        $result = str_replace(['ô', 'ö', 'ó'], 'o', $result);
        $result = str_replace('ç', 'c', $result);
        $result = str_replace('ñ', 'n', $result);
        
        // Replace spaces with hyphens, but preserve existing hyphens
        $result = preg_replace('/\s+/', '-', $result);
        
        // Remove any characters that aren't letters, numbers, or hyphens
        $result = preg_replace('/[^a-z0-9\-]/', '', $result);
        
        // Clean up multiple consecutive hyphens
        $result = preg_replace('/-+/', '-', $result);
        
        // Remove leading/trailing hyphens
        return trim($result, '-');
    }

    /**
     * Decode URL parameter by converting it back to the original form
     */
    private function decodeUrlParam($param)
    {
        if (!$param || $param === '_') {
            return null;
        }

        // URL decode the parameter first to handle encoded characters like %C3%AF
        $decoded = urldecode($param);
        
        // For city names with hyphens, we need to check if this matches a known city
        // before converting hyphens to spaces
        $cityNames = $this->getAllCityNames();
        
        // Strategy 1: Direct exact match (case-insensitive)
        foreach ($cityNames as $cityName) {
            if (strtolower($cityName) === strtolower($decoded)) {
                return $cityName;
            }
        }
        
        // Strategy 2: Check if the URL param matches the sanitized version of a city
        foreach ($cityNames as $cityName) {
            if (strtolower($this->sanitizeUrlParam($cityName)) === strtolower($decoded)) {
                return $cityName;
            }
        }
        
        // Strategy 3: Try accent-insensitive matching
        $decodedNormalized = $this->createAccentInsensitiveString($decoded);
        foreach ($cityNames as $cityName) {
            $cityNormalized = $this->createAccentInsensitiveString($cityName);
            if ($cityNormalized === $decodedNormalized) {
                return $cityName;
            }
        }
        
        // Strategy 4: Try with hyphens converted to spaces
        $withSpaces = str_replace('-', ' ', $decoded);
        foreach ($cityNames as $cityName) {
            if (strtolower($cityName) === strtolower($withSpaces)) {
                return $cityName;
            }
        }
        
        // Strategy 5: Try accent-insensitive matching with spaces
        $withSpacesNormalized = $this->createAccentInsensitiveString($withSpaces);
        foreach ($cityNames as $cityName) {
            $cityNormalized = $this->createAccentInsensitiveString($cityName);
            if ($cityNormalized === $withSpacesNormalized) {
                return $cityName;
            }
        }
        
        // Strategy 6: Try with spaces converted to hyphens (for cities that originally have hyphens)
        $withHyphens = str_replace(' ', '-', $decoded);
        foreach ($cityNames as $cityName) {
            if (strtolower($cityName) === strtolower($withHyphens)) {
                return $cityName;
            }
        }
        
        // Strategy 7: Try accent-insensitive matching with hyphens
        $withHyphensNormalized = $this->createAccentInsensitiveString($withHyphens);
        foreach ($cityNames as $cityName) {
            $cityNormalized = $this->createAccentInsensitiveString($cityName);
            if ($cityNormalized === $withHyphensNormalized) {
                return $cityName;
            }
        }
        
        // Strategy 8: For complex cases like "agadir-ida-ou-tanane" -> "agadir-ida ou tanane"
        // Try replacing multiple hyphens with space-hyphen-space pattern
        $complexPattern = preg_replace('/-+/', ' ', $decoded); // Replace hyphens with spaces
        $complexPattern = preg_replace('/\s+/', ' ', $complexPattern); // Clean up multiple spaces
        foreach ($cityNames as $cityName) {
            if (strtolower($cityName) === strtolower($complexPattern)) {
                return $cityName;
            }
        }
        
        // Strategy 9: Try accent-insensitive matching for complex patterns
        $complexPatternNormalized = $this->createAccentInsensitiveString($complexPattern);
        foreach ($cityNames as $cityName) {
            $cityNormalized = $this->createAccentInsensitiveString($cityName);
            if ($cityNormalized === $complexPatternNormalized) {
                return $cityName;
            }
        }
        
        // If still no match, return the version with spaces (most likely to be correct)
        return trim($withSpaces);
    }
    
    /**
     * Get all possible city names from JSON files and translations
     */
    private function getAllCityNames()
    {
        static $cityNames = null;
        
        if ($cityNames === null) {
            $cityNames = [];
            
            // Get cities from French JSON file
            $frenchCitiesPath = public_path('assets/js/cities-districts.json');
            if (file_exists($frenchCitiesPath)) {
                $frenchCities = json_decode(file_get_contents($frenchCitiesPath), true);
                if ($frenchCities) {
                    $cityNames = array_merge($cityNames, array_keys($frenchCities));
                }
            }
            
            // Get cities from Arabic JSON file
            $arabicCitiesPath = public_path('assets/js/cities-districtsAR.json');
            if (file_exists($arabicCitiesPath)) {
                $arabicCities = json_decode(file_get_contents($arabicCitiesPath), true);
                if ($arabicCities) {
                    $cityNames = array_merge($cityNames, array_keys($arabicCities));
                }
            }
            
            // Get cities from translation files
            try {
                $cityTranslations = __('villes', [], 'ar');
                if (is_array($cityTranslations)) {
                    $cityNames = array_merge($cityNames, array_keys($cityTranslations));
                    $cityNames = array_merge($cityNames, array_values($cityTranslations));
                }
            } catch (\Exception $e) {
                // Translation file might not exist
            }
            
            // Remove duplicates and empty values
            $cityNames = array_unique(array_filter($cityNames));
        }
        
        return $cityNames;
    }
    
    /**
     * Normalize string by handling accented characters properly
     */
    private function normalizeString($string)
    {
        // Don't remove accents, just normalize the string
        return trim($string);
    }
    
    /**
     * Create accent-insensitive comparison string
     */
    private function createAccentInsensitiveString($string)
    {
        // Convert to lowercase and remove accents for comparison
        $normalized = strtolower($string);
        $normalized = str_replace(['é', 'è', 'ê', 'ë'], 'e', $normalized);
        $normalized = str_replace(['à', 'â', 'ä', 'á'], 'a', $normalized);
        $normalized = str_replace(['ù', 'û', 'ü', 'ú'], 'u', $normalized);
        $normalized = str_replace(['î', 'ï', 'í'], 'i', $normalized);
        $normalized = str_replace(['ô', 'ö', 'ó'], 'o', $normalized);
        $normalized = str_replace(['ç'], 'c', $normalized);
        $normalized = str_replace(['ñ'], 'n', $normalized);
        return $normalized;
    }

    /**
     * Parse flexible URL structure and determine parameter types
     */
    private function parseFlexibleUrl($segments)
    {
        $result = [
            'subspeciality' => null,
            'city' => null,
            'district' => null,
            'gender' => null
        ];

        foreach ($segments as $segment) {
            // Check if it's a gender first (these don't have hyphens or special chars)
            if (in_array($segment, ['male', 'female'])) {
                $result['gender'] = $segment;
                continue;
            }

            // Decode the segment to handle accented characters and convert hyphens to spaces
            $cleanSegment = $this->decodeUrlParam($segment);

            if (!$cleanSegment) {
                continue;
            }            // Check if it's a subspeciality by searching with the cleaned segment
            $subSpec = null;

            // First check if segment matches French translation URLs
            $allSubspecialities = SubSpeciality::all();
            foreach ($allSubspecialities as $subspeciality) {
                // Get the English key for this subspeciality name
                $englishKey = $subspeciality->name;

                // If current locale is Arabic, find the English key by reverse lookup
                if (app()->getLocale() === 'ar') {
                    $allSubspecialitiesAr = __('subspecialities', [], 'ar');
                    $englishKey = array_search($subspeciality->name, $allSubspecialitiesAr) ?: $subspeciality->name;
                }

                // Get French translation using the English key
                $frenchName = __('subspecialities.' . $englishKey, [], 'fr');

                // If translation not found, use the original name
                if (strpos($frenchName, 'subspecialities.') === 0) {
                    $frenchName = $subspeciality->name;
                }

                // Always convert French name to lowercase for consistency
                $frenchName = strtolower($frenchName);

                // Create URL-safe version from French name (same logic as in Blade templates)
                $urlName = $frenchName; // Already lowercase
                $urlName = str_replace(['é', 'è', 'ê', 'ë'], 'e', $urlName);
                $urlName = str_replace(['à', 'â', 'ä', 'á'], 'a', $urlName);
                $urlName = str_replace(['ù', 'û', 'ü', 'ú'], 'u', $urlName);
                $urlName = str_replace(['î', 'ï', 'í'], 'i', $urlName);
                $urlName = str_replace(['ô', 'ö', 'ó'], 'o', $urlName);
                $urlName = str_replace('ç', 'c', $urlName);
                $urlName = str_replace('ñ', 'n', $urlName);
                $urlName = str_replace([' ', '/', '(', ')', "'", '.', '&', ','], ['-', '-', '', '', '', '', 'et', ''], $urlName);
                $urlName = preg_replace('/[^a-z0-9\-]/', '', $urlName);
                $urlName = preg_replace('/-+/', '-', $urlName);
                $urlName = trim($urlName, '-');

                // Check if this URL segment matches this subspecialty's URL name
                if ($segment === $urlName) {
                    $subSpec = $subspeciality;
                   
                    break;
                }

                // Handle related subspecialities for dental searches
                if ($segment === 'dentiste') {
                    // Check if this subspeciality is dental-related
                    $dentalKeywords = ['dentiste', 'dental', 'chirurgien dentiste', 'dentofacial', 'orthodontiste', 'parodontiste', 'prosthodontiste', 'endodontiste'];
                    $isDentalRelated = false;

                    foreach ($dentalKeywords as $keyword) {
                        if (stripos($frenchName, $keyword) !== false || stripos($subspeciality->name, $keyword) !== false) {
                            $isDentalRelated = true;
                            break;
                        }
                    }

                    if ($isDentalRelated) {
                        $subSpec = $subspeciality;
                      
                        // Don't break here - we want to collect all dental subspecialities
                        // But for URL parsing, we'll take the first match
                        if (!isset($result['subspeciality'])) {
                            break;
                        }
                    }
                }
            }

            // If not found, try direct match with original name (Arabic)
            if (!$subSpec) {
                $subSpec = SubSpeciality::where('name', $cleanSegment)->first();
            }

            // If not found, try to find by slug match with original name
            if (!$subSpec) {
                $allSubspecialities = SubSpeciality::all();
                foreach ($allSubspecialities as $subspeciality) {
                    if (Str::slug($subspeciality->name) === $segment) {
                        $subSpec = $subspeciality;
                        break;
                    }
                }
            }

             if (!$subSpec) {
                $allSubspecialities = SubSpeciality::all();
                foreach ($allSubspecialities as $subspeciality) {
                    $arabicTranslation = __('subspecialities.' . $subspeciality->name, [], 'ar');
                    if (strtolower($arabicTranslation) === strtolower($cleanSegment)) {
                        $subSpec = $subspeciality;
                        break;
                    }
                    // Also check slugged Arabic translation
                    if (Str::slug($arabicTranslation) === $segment) {
                        $subSpec = $subspeciality;
                        break;
                    }
                }
            }
            if ($subSpec && !$result['subspeciality']) {
                $result['subspeciality'] = $subSpec->id;
               
                continue;
            }

            Log::info('No subspeciality match, treating as location', ['segment' => $segment, 'cleanSegment' => $cleanSegment]);

            // Otherwise treat as location (city first, then district)
            if (!$result['city']) {
                $result['city'] = $cleanSegment; // Use decoded segment for city
            } elseif (!$result['district']) {
                $result['district'] = $cleanSegment; // Use decoded segment for district
            }
        }

        return $result;
    }

    public function seoSearch($param1 = null, $param2 = null, $param3 = null, $param4 = null)
{
    // Collect all non-null parameters
    $segments = array_filter([$param1, $param2, $param3, $param4], function($value) {
        return $value !== null;
    });

    // Parse the flexible URL structure
    $params = $this->parseFlexibleUrl($segments);
     if ($params['subspeciality']) {
        $subSpec = SubSpeciality::find($params['subspeciality']);
        if ($subSpec) {
            $currentSubspecialitySegment = null;
            // Find which segment was the subspeciality
            foreach ($segments as $segment) {
                $cleanSegment = $this->decodeUrlParam($segment);
                $arabicTranslation = __('subspecialities.' . $subSpec->name, [], 'ar');
                if (strtolower($arabicTranslation) === strtolower($cleanSegment)) {
                    // This URL contains Arabic subspeciality, redirect to French
                    $urlParts = ['medecin'];
                    $frenchName = __('subspecialities.' . $subSpec->name, [], 'fr');
                    $urlParts[] = $this->sanitizeUrlParam($frenchName);

                    // Add other parameters
                    if ($params['city']) {
                        $urlParts[] = $this->sanitizeUrlParam($params['city']);
                    }
                    if ($params['district']) {
                        $urlParts[] = $this->sanitizeUrlParam($params['district']);
                    }
                    if ($params['gender']) {
                        $urlParts[] = $params['gender'];
                    }

                    $frenchUrl = '/' . implode('/', $urlParts);
                    return redirect($frenchUrl, 301);
                }
            }
        }
    }    // Debug logging
       // Set the request parameters to match the old search functionality
    request()->merge($params);

    // Also check for French query parameters and merge them
    if (request()->has('prenom')) {
        request()->merge(['first_name' => request()->input('prenom')]);
    }

    if (request()->has('nom')) {
        request()->merge(['last_name' => request()->input('nom')]);
    }

    // Build SEO meta based on parsed parameters
    $title = 'Recherche de Médecins';
    $description = 'Trouvez les meilleurs médecins près de chez vous grâce à DocExpress.';

    if ($params['subspeciality']) {
        $subSpec = SubSpeciality::find($params['subspeciality']);
        if ($subSpec) {
            $title = 'Médecins ' . $subSpec->name;
            $description = 'Trouvez des médecins spécialisés en ' . $subSpec->name;
        }
    }

    if ($params['city']) {
        $title .= ' à ' . $params['city'];
        $description .= ' à ' . $params['city'];
    }

    if ($params['district']) {
        $title .= ' - ' . $params['district'];
        $description .= ' dans le quartier ' . $params['district'];
    }

    $title .= ' | DocExpress';

    SEOMeta::setTitle($title);
    SEOMeta::setDescription($description);
    SEOMeta::addKeyword([
        'recherche médecins',
        'trouver un médecin',
        'réserver rendez-vous médical',
        'médecins Casablanca',
        'soins médicaux en ligne'
    ]);
    SEOMeta::setCanonical(request()->fullUrl());
    OpenGraph::setTitle($title);
    OpenGraph::setDescription($description);
    OpenGraph::setUrl(request()->fullUrl());
    JsonLd::setTitle($title);
    JsonLd::setDescription($description);

    $doctors = Doctor::search();
    $clinics = CentreMedical::all();
    $subspecialities = SubSpeciality::all();
    $translatedDoctors = $doctors->map(function ($doctor) {
        $doctor->translated_speciality = __('subspecialities.' . $doctor->subspeciality_name, [], 'fr');
        return $doctor;
    });

    return view('pages.search.index', compact('doctors', 'translatedDoctors', 'clinics', 'subspecialities'));
}


    public function SearchPharmacy()
    {
        // Set SEO meta for pharmacy search
        SEOMeta::setTitle('Trouver un pharmacien | DocExpress');
        SEOMeta::setDescription('Trouver des pharmacies près de chez vous avec DocExpress. Simplifiez vos besoins en santé.');
        SEOMeta::addKeyword([
            'pharmacies Casablanca',
            'trouver une pharmacie',
            'recherche pharmacie',
            'pharmacie ouverte à proximité',
            'pharmacie au Maroc'
        ]);
        SEOMeta::setCanonical(url()->current());
        OpenGraph::setTitle('Trouver un pharmacien | DocExpress');
        OpenGraph::setDescription('DocExpress vous aide à localiser rapidement des pharmacies dans votre région.');
        OpenGraph::setUrl(url()->current());
        JsonLd::setTitle('Trouver un pharmacien | DocExpress');
        JsonLd::setDescription('Trouvez facilement des pharmacies près de chez vous grâce à DocExpress.');

        $pharmacies = Pharmacy::search();
        $total = $pharmacies->count();
        return view('pages.pharmacies.index', compact('pharmacies', 'total'));
    }

    public function searchClinic(Request $request)
    {
        // Set SEO meta for clinic/office search
        SEOMeta::setTitle('Recherche de Cliniques et Cabinets | DocExpress');
        SEOMeta::setDescription('Trouvez des cliniques et cabinets près de chez vous grâce à DocExpress. Prenez rendez-vous rapidement.');
        SEOMeta::addKeyword([
            'cliniques au Maroc',
            'cabinet médical Casablanca',
            'clinique proche de chez moi',
            'réserver un rendez-vous clinique',
            'services de santé Maroc'
        ]);
        SEOMeta::setCanonical(url()->current());
        OpenGraph::setTitle('Recherche de Cliniques et Cabinets | DocExpress');
        OpenGraph::setDescription('Recherchez des cliniques et cabinets autour de vous avec DocExpress.');
        OpenGraph::setUrl(url()->current());
        JsonLd::setDescription('Découvrez les cliniques et cabinets disponibles près de chez vous grâce à DocExpress.');
        JsonLd::setTitle('Recherche de Cliniques et Cabinets | DocExpress');
        $filters = $request->only('name');
        $centres = CentreMedical::clinique($filters);
        $total = $centres->count();
        return view('pages.clinic.index', compact('total', 'centres'));
    }

  public function searchDoctorsAroundMe(Request $request)
    {
        $request->validate([
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
        ]);
        SEOMeta::setTitle('Médecins Près de Moi | DocExpress');
        SEOMeta::setDescription('Trouvez des médecins qualifiés autour de votre emplacement actuel avec DocExpress.');
        SEOMeta::addKeyword([
            'médecins autour de moi',
            'médecins disponibles',
            'trouver un médecin près de chez moi',
            'réservation de soins médicaux',
            'médecins proches Maroc'
        ]);
        SEOMeta::setCanonical(url()->current());
        OpenGraph::setTitle('Médecins Près de Moi | DocExpress');
        OpenGraph::setDescription('Recherchez des médecins autour de vous rapidement et facilement avec DocExpress.');
        OpenGraph::setUrl(url()->current());
        JsonLd::setTitle('Médecins Près de Moi | DocExpress');
        JsonLd::setDescription('Trouvez rapidement des médecins près de chez vous grâce à notre outil de recherche.');
        $userLatitude = $request->input('latitude');
        $userLongitude = $request->input('longitude');
        $doctors = Doctor::findNearbyDoctors($userLatitude, $userLongitude)->map(function ($doctor) {
            $doctor = $doctor['doctor'];
            $doctor->translated_speciality = __('specialities.' . $doctor->speciality->name, [], 'fr');
            return $doctor;
        });
        $specialities = Speciality::all();
        $subSpecialities = SubSpeciality::all();
        $clinics = CentreMedical::all();
        return view('pages.doctors.nearby', [
            'doctors' => $doctors,
            'specialities' => $specialities,
            'subspecialities' => $subSpecialities,
            'error' => $doctors->isEmpty() ? 'Aucun médecin trouvé à proximité.' : null,
            'latitude' => $userLatitude,
            'longitude' => $userLongitude,
            'clinics'=> $clinics,

        ]);
    }

    public function debugUrl($param1 = null, $param2 = null, $param3 = null, $param4 = null)
    {
        // Collect all non-null parameters
        $segments = array_filter([$param1, $param2, $param3, $param4], function($value) {
            return $value !== null;
        });

        $decoded = [];
        foreach ($segments as $segment) {
            $decoded[] = [
                'original' => $segment,
                'decoded' => $this->decodeUrlParam($segment)
            ];
        }

        // Parse the flexible URL structure
        $params = $this->parseFlexibleUrl($segments);

        return response()->json([
            'segments' => $segments,
            'decoded' => $decoded,
            'parsed_params' => $params,
            'request_merged' => request()->all()
        ]);
    }

    /**
     * Handle search with query parameters (for name and clinic searches)
     */
    public function searchWithParams(Request $request)
    {
        // Map French parameter names to English ones used in the backend
        $parameterMap = [
            'prenom' => 'first_name',
            'nom' => 'last_name',
            'clinique' => 'clinic',
            'specialite' => 'subspeciality',
            'ville' => 'city',
            'quartier' => 'district',
            'genre' => 'gender'
        ];

        $mappedParams = [];

        foreach ($parameterMap as $frenchParam => $englishParam) {
            if ($request->has($frenchParam)) {
                $value = $request->input($frenchParam);
                // Decode URL-safe parameter back to original form
                $mappedParams[$englishParam] = $this->decodeUrlParam($value);
            }
        }

        // Handle subspeciality special case - if we have specialite, find the ID
        if (isset($mappedParams['subspeciality'])) {
            $subSpec = SubSpeciality::where('name', 'LIKE', '%' . $mappedParams['subspeciality'] . '%')->first();
            if ($subSpec) {
                $mappedParams['subspeciality'] = $subSpec->id;
            }
        }

        // Set the request parameters to match the old search functionality
        request()->merge($mappedParams);

        // Build SEO meta based on parsed parameters
        $title = 'Recherche de Médecins';
        $description = 'Trouvez les meilleurs médecins près de chez vous grâce à DocExpress.';

        if (isset($mappedParams['first_name']) || isset($mappedParams['last_name'])) {
            $name = trim(($mappedParams['first_name'] ?? '') . ' ' . ($mappedParams['last_name'] ?? ''));
            $title = 'Docteur ' . $name . ' | DocExpress';
            $description = 'Consultez le profil du Dr ' . $name . ' et prenez rendez-vous en ligne.';
        }

        if (isset($mappedParams['clinic'])) {
            $title = 'Médecins - ' . $mappedParams['clinic'] . ' | DocExpress';
            $description = 'Trouvez tous les médecins de ' . $mappedParams['clinic'] . '.';
        }

        SEOMeta::setTitle($title);
        SEOMeta::setDescription($description);
        SEOMeta::addKeyword([
            'médecin', 'docteur', 'consultation', 'rendez-vous',
            'soins médicaux en ligne'
        ]);
        SEOMeta::setCanonical(request()->fullUrl());

        OpenGraph::setTitle($title);
        OpenGraph::setDescription($description);
        OpenGraph::setUrl(request()->fullUrl());

        JsonLd::setTitle($title);
        JsonLd::setDescription($description);

        $doctors = Doctor::search();
        $clinics = CentreMedical::all();
        $subspecialities = SubSpeciality::all();
        $translatedDoctors = $doctors->map(function ($doctor) {
            return $doctor;
        });

        return view('pages.search.index', compact('doctors', 'translatedDoctors', 'clinics', 'subspecialities'));
    }
}
<?php

namespace App\Http\Controllers\assistanteadministrative;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use App\Services\CentreMedicalService\CentreMedicalService;
use App\Services\DoctorToCentreMedical\DoctorToCentreMedical;
use App\Services\PatientClinique\PatientClinique;
use App\Services\AppointmentService\AppointmentService;
use App\Repositories\AppointmentRepository;
use Illuminate\Support\Facades\App;

class PatientcliniqueController extends Controller
{
    protected $centreMedicalService;
    protected $doctorToCentreMedical;
    protected $patientcliniqueService;
    protected $appointmentService;
    protected $appointmentRepository;

    public function __construct(
        CentreMedicalService $centreMedicalService,
        DoctorToCentreMedical $doctorToCentreMedical,
        PatientClinique  $patientcliniqueService,
        AppointmentService $appointmentService,
        AppointmentRepository $appointmentRepository
    ) {
        $this->centreMedicalService = $centreMedicalService;
        $this->doctorToCentreMedical = $doctorToCentreMedical;
        $this->patientcliniqueService = $patientcliniqueService;
        $this->appointmentService = $appointmentService;
        $this->appointmentRepository = $appointmentRepository; 
    }

    public function getByCentreMedical(Request $request)
    {
        // Fetch the selected clinic ID from the session
        $centreMedicalId = session('selected_clinic_id');
        $patients = $this->patientcliniqueService->getPatientClinique($centreMedicalId);
        $appointments = $this->appointmentService->getPatientsByCentre($centreMedicalId);
        // Return the view with the necessary data
        return view('pages.user-accounts.assistante-administrative.patient-profile.patient-clinic',
            compact('patients', 'appointments'));
    }

    /**
     * Get appointments based on patient ID or clinic ID.
     *
     * @param  int  $patientId
     * @param  int  $patientCliniqueId
     * @param  string  $motif
     * @return \Illuminate\Http\Response
     */
    public function getAppointments($patientId, $motif)
    {
        // Call the repository method to fetch the appointments
        $appointments = $this->appointmentRepository->getAppointmentsByPatientOrClinique($patientId, $motif);


return view('pages.user-accounts.assistante-administrative.patient-profile.appointment-patient', compact('appointments', 'motif'));

}

}

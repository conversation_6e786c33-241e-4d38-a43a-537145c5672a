<?php

namespace App\Http\Controllers\Doctor;

use App\Http\Controllers\Controller;
use App\Services\DentalprocedureService\IDentalprocedureService;
use App\Repositories\PatientRepositoryInterface;
use App\Repositories\AddressRepositoryInterface;
use App\Http\Requests\Dentalprocedure\UpdateDentalProcedureRequest;
use App\Models\Patient;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use PDF; 
class DentalprocedureController extends Controller
{
    protected $DentalprocedureService;
    protected $PatientRepository;
    protected $AddressRepository;

    public function __construct(IDentalprocedureService $DentalprocedureService,PatientRepositoryInterface $PatientRepository,AddressRepositoryInterface $AddressRepository)
    {
        $this->DentalprocedureService = $DentalprocedureService;
        $this->PatientRepository = $PatientRepository;
        $this->AddressRepository = $AddressRepository;
    }
    public function index()
    {
        $dentalprocedures = $this->DentalprocedureService->getAllPaginated();
        return view('pages.user-accounts.doctor.patient-profile.tabs.acts.index', compact('dentalprocedures'));
    }
    public function show($id)
    {
        $dentalprocedure = $this->DentalprocedureService->findById($id);
        if ($dentalprocedure) {
            return response()->json($dentalprocedure);
        }
        return response()->json(['message' => 'Dental procedure not found'], 404);
    }
    public function store(Request $request)
    {
        $data = $request->all();
        $data['doctor_id'] = Auth::user()->doctor->id;
        $data['patient_id'] = $request->input('patient_id'); 
        $dentalprocedure = $this->DentalprocedureService->create($data);
       return redirect()
            ->intended('/doctor/patient/' . $data['patient_id'])
            ->with('success', trans("alerts.success.the_act_dentaire_have_been_added_successfully"));
    }
 
    public function update(UpdateDentalProcedureRequest $request, $id)
    {
        $data = $request->all();
        $dentalprocedure = $this->DentalprocedureService->update($id, $data);
        if ($dentalprocedure) {
            return response()->json($dentalprocedure);
        }
        return response()->json(['message' => 'Dental procedure not found'], 404);
    }
    public function destroy($id)
    {
        $deleted = $this->DentalprocedureService->delete($id);
        if ($deleted) {
            return response()->json(['message' => 'Dental procedure deleted']);
        }
        return response()->json(['message' => 'Dental procedure not found'], 404);
    }
    public function downloadPrescriptionPdf($id,$patient_id)
    {
        $dentalprocedures = $this->DentalprocedureService->findByPrescriptionId($id);
         $patient = $this->PatientRepository->get($patient_id);
         $addresse = $this->AddressRepository                           ->getByuser($patient->user_id)->first();
  
        
        if ($dentalprocedures->isNotEmpty()) {
            $pdf = PDF::loadView('pages.user-accounts.doctor.components.template-docs.dentalprocedure-doc', compact('dentalprocedures', 'patient','addresse'));
            $pdf->setPaper('A4', 'portrait');
            return $pdf->download('prescription_' . $id . '.pdf');
        }
        return redirect()->back()->with('error', 'Prescription not found');
    }
}

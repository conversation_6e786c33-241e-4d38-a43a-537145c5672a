<?php
namespace App\Http\Controllers\Patient;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\MedicalRecord;

class MedicalRecordAccessController extends Controller
{
    public function store(Request $request)
    {
        $request->validate([
            'record_id' => ['required','integer','exists:medical_records,id'],
            'doctor_id' => ['required','integer','exists:doctors,id'],
            'note' => ['nullable','string','max:1000']
        ]);

        $record = MedicalRecord::findOrFail($request->record_id);
        $patientId = Auth::user()->patient->id;

        // Ensure the record belongs to the authenticated patient
        if ((int)$record->patient_id !== (int)$patientId) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        DB::table('medical_record_accesses')->updateOrInsert(
            [
                'medical_record_id' => $record->id,
                'doctor_id' => $request->doctor_id,
            ],
            [
                'granted_by_patient_id' => $patientId,
                'note' => $request->note,
                'expires_at' => null,
                'updated_at' => now(),
                'created_at' => now(),
            ]
        );

        if ($request->wantsJson()) {
            return response()->json(['success' => true]);
        }

        return back()->with('success', 'Accès accordé au médecin.');
    }

    public function destroy(Request $request)
    {
        try {
            // Handle both POST with method override and actual DELETE requests
            $request->validate([
                'record_id' => ['required','integer','exists:medical_records,id'],
                'doctor_id' => ['required','integer','exists:doctors,id']
            ]);

            $record = MedicalRecord::findOrFail($request->record_id);
            $patientId = Auth::user()->patient->id;

            // Debug logging
            Log::info('Revoke access attempt', [
                'record_id' => $record->id,
                'doctor_id' => $request->doctor_id,
                'patient_id' => $patientId,
                'record_patient_id' => $record->patient_id
            ]);

            // Ensure the record belongs to the authenticated patient
            if ((int)$record->patient_id !== (int)$patientId) {
                Log::warning('Unauthorized access attempt', [
                    'record_patient_id' => $record->patient_id,
                    'current_patient_id' => $patientId
                ]);

                if ($request->wantsJson()) {
                    return response()->json(['success' => false, 'message' => 'Non autorisé à modifier cet accès.'], 403);
                }
                return back()->with('error', 'Non autorisé.');
            }

            // Check if access exists before trying to delete
            $existingAccess = DB::table('medical_record_accesses')
                ->where('medical_record_id', $record->id)
                ->where('doctor_id', $request->doctor_id)
                ->where('granted_by_patient_id', $patientId)
                ->first();

            Log::info('Existing access check', [
                'exists' => $existingAccess ? 'yes' : 'no',
                'access_data' => $existingAccess
            ]);

            if (!$existingAccess) {
                if ($request->wantsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Aucun accès trouvé pour ce médecin sur ce dossier.'
                    ]);
                }
                return back()->with('error', 'Aucun accès trouvé à retirer.');
            }

            $deleted = DB::table('medical_record_accesses')
                ->where('medical_record_id', $record->id)
                ->where('doctor_id', $request->doctor_id)
                ->where('granted_by_patient_id', $patientId)
                ->delete();

            Log::info('Delete result', ['deleted_count' => $deleted]);

            // Always return JSON for AJAX requests
            return response()->json([
                'success' => $deleted > 0,
                'message' => $deleted > 0 ? 'Accès retiré avec succès.' : 'Erreur lors de la suppression de l\'accès.'
            ]);

        } catch (\Exception $e) {
            Log::error('Error in destroy method', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Une erreur est survenue: ' . $e->getMessage()
                ], 500);
            }

            return back()->with('error', 'Une erreur est survenue lors de la suppression.');
        }
    }

    public function getAccessList(Request $request)
    {
        $request->validate([
            'record_id' => ['required','integer','exists:medical_records,id']
        ]);

        $record = MedicalRecord::findOrFail($request->record_id);
        $patientId = Auth::user()->patient->id;

        // Ensure the record belongs to the authenticated patient
        if ((int)$record->patient_id !== (int)$patientId) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $accesses = DB::table('medical_record_accesses as mra')
            ->join('doctors as d', 'd.id', '=', 'mra.doctor_id')
            ->join('users as u', 'u.id', '=', 'd.user_id')
            ->leftJoin('specialities as s', 's.id', '=', 'd.speciality_id')
            ->where('mra.medical_record_id', $record->id)
            ->where('mra.granted_by_patient_id', $patientId)
            ->select([
                'mra.id as access_id',
                'mra.doctor_id',
                'mra.note',
                'mra.created_at as granted_at',
                'u.first_name',
                'u.last_name',
                's.name as speciality_name'
            ])
            ->get();

        return response()->json(['accesses' => $accesses]);
    }
}

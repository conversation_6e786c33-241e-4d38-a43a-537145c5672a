<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\AssistanteAdministrative\CreateAssistanteRequest;
use App\Http\Requests\AssistanteAdministrative\UpdateAssistanteRequest;
use App\Repositories\SpecialityRepositoryInterface;
use App\Repositories\DoctorToCentreMedicalRepositoryInterface;
use App\Services\AssistanteService\IAssistanteService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\ModelNotFoundException;


class AssistanteAdController extends Controller
{

    protected $assistanteService;
    protected $specialityRepository;
    protected $doctorToCentreMedicalRepository;

    public function __construct(IAssistanteService $assistanteService, SpecialityRepositoryInterface $specialityRepository, DoctorToCentreMedicalRepositoryInterface $doctorToCentreMedicalRepository)
    {
        $this->assistanteService = $assistanteService;
        $this->specialityRepository = $specialityRepository;
        $this->doctorToCentreMedicalRepository = $doctorToCentreMedicalRepository;
    }

    public function index()
    {
        $assistantes = $this->assistanteService->all()->load('doctors.user','centremedical');
        $specialities = $this->specialityRepository->all(); 
       
        return view('pages.admin.assistante.index', compact('assistantes', 'specialities'));
    }
    public function getAllClinics()
    {
        try {
            $clinics = $this->doctorToCentreMedicalRepository->all();
            return response()->json($clinics);
        } catch (ModelNotFoundException $e) {
            
            return response()->json(['error' => 'error fetching clinic'], 404);
        }
    }
    public function create()
    {
        try {
            $doctors = $this->assistanteService->getAvailableDoctors();
            return response()->json(['doctors' => $doctors]);
        } catch (\Throwable $th) {
            Log::error('Error fetching doctors:', ['exception' => $th]);
            return response()->json(['error' => 'Error fetching doctors'], 500);
        }
    }

    public function store(CreateAssistanteRequest $request)
    {
        try {
          
            $data = $request->validated();
            $data['role'] = 'assistante';
            $data['service'] = $request->input('service'); // Ensure service is included
            $this->assistanteService->create($data);
            
            return response()->json(['success' => true]);
        } catch (\Throwable $th) {
            Log::error('Error storing assistante:', ['exception' => $th]);
            return response()->json(['success' => false, 'message' => trans("alerts.error.something_went_wrong")], 500);
        }
    }

    public function show($id)
    {
        $doctor = $this->assistanteService->get($id);

        if (!$doctor) return abort(404);

        if ($doctor->user->account_status == 0) return view(
            'pages.user-accounts.doctor.profile.profile-blocked',
            ['doctor' => $doctor]
        );

        $can_review = Auth::user() && $doctor->appointments()->where('patient_id', Auth::user()->patient->id)->where('status', 'completed')->exists();
        $already_reviewed = Auth::user() && auth()->user()->patient->reviews()->where('doctor_id', $doctor->id)->exists();

        $holidaysdate = [];
        $holidays = [];
        foreach ($doctor->holidays as $holiday) {
            $holidaysdate[] = $holiday['date_holiday'];
            $holidays[] = $holiday;
        }
        $holidaysdate = array_unique($holidaysdate);
        $holidays = array_unique($holidays);

        return view(
            'pages.user-accounts.doctor.profile.profile',
            ['doctor' => $doctor, 'can_review' => $can_review, 'holidays' => $holidays, 'holidaysdate' => $holidaysdate]
        );
    }

    public function edit(Request $request)
    {
        try {
            $id = $request->input('id');
           
            $assistante = $this->assistanteService->getAssistanteDetails($id);
            if (!$assistante) {
                return response()->json(['error' => 'Assistante not found'], 404);
            }
            $doctors = $this->assistanteService->getAllDoctors()->map(function ($doctor) use ($assistante) {
                $doctor->has_assistant = $doctor->assistantes->isNotEmpty() && !in_array($doctor->id, $assistante['doctor_ids']);
                return $doctor;
            });

            
            return response()->json([
                'assistante' => $assistante,
                'doctors' => $doctors
            ]);
        } catch (\Throwable $th) {
            Log::error('Error fetching assistante data:', ['exception' => $th]);
            return response()->json(['error' => 'Error fetching assistante data'], 500);
        }
    }

    public function update(UpdateAssistanteRequest $request)
    {
        try {
            $validatedData = $request->validated();
            $id = $request->input('id');
            $validatedData['service'] = $request->input('service'); // Ensure service is included
            
           
            if (!isset($validatedData['doctor_id']) || !is_array($validatedData['doctor_id'])) {
                $validatedData['doctor_id'] = [];
            }

            $this->assistanteService->update($validatedData, $id);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 400);
        } catch (\Throwable $th) {
            return response()->json(['error' => trans("alerts.error.something_went_wrong")], 500);
        }

        return response()->json(['success' => trans("alerts.success.doctor_updated_successfully")], 200);
    }

    public function block(Request $request)
    {
        $this->assistanteService->block($request->id);

        return response()->json(false);
    }

    public function unblock(Request $request)
    {
        $this->assistanteService->unblock($request->id);

        return response()->json(true);
    }
}

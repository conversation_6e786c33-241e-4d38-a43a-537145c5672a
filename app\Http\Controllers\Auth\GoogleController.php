<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Laravel\Socialite\Facades\Socialite;
use Illuminate\Support\Facades\Log;

class GoogleController extends Controller
{
    public function redirectToGoogle()
    {
        return Socialite::driver('google')->redirect();
    }

    public function handleGoogleCallback()
    {
        try {
            // Retrieve user from Google
            $googleUser = Socialite::driver('google')->stateless()->user();
    
            // Debugging: Log the Google user data
            Log::info('Google User: ', (array) $googleUser);
    
            // Check if user already exists with this email
            $existingUser = User::where('email', $googleUser->getEmail())->first();
            
            if ($existingUser) {
                // User exists - just update google_id if not already set and login
                if (empty($existingUser->google_id)) {
                    $existingUser->update(['google_id' => $googleUser->getId()]);
                }
                
                Log::info('Existing user found during Google OAuth', [
                    'user_id' => $existingUser->id,
                    'email' => $existingUser->email,
                    'role' => $existingUser->role,
                    'has_doctor' => $existingUser->doctor ? 'yes' : 'no',
                    'has_patient' => $existingUser->patient ? 'yes' : 'no'
                ]);
                
                // Log the user in
                Auth::login($existingUser);
                
                // Redirect based on user role
                return $this->redirectUserBasedOnRole($existingUser);
            } else {
                // New user - create with patient role as default
                $user = User::create([
                    'first_name' => explode(' ', $googleUser->getName())[0] ?? '',
                    'last_name' => explode(' ', $googleUser->getName())[1] ?? '', 
                    'email' => $googleUser->getEmail(),
                    'phone' => 'N/A-' . rand(1000, 9999), 
                    'google_id' => $googleUser->getId(),
                    'password' => bcrypt('default_password'),
                    'role' => 'patient', // Default role for new Google users
                ]);
    
                // Create patient record for new user
                \App\Models\Patient::create([
                    'user_id' => $user->id,
                ]);
                
                Log::info('New user created via Google OAuth', [
                    'user_id' => $user->id,
                    'email' => $user->email,
                    'role' => $user->role
                ]);
                
                // Log the user in
                Auth::login($user);
                
                // If the phone number is auto-generated with 'N/A-', redirect to settings
                if (strpos($user->phone, 'N/A-') === 0) {
                    return redirect()->route('patient-settings')
                        ->with('warning', 'Veuillez mettre à jour votre numéro de téléphone dans vos paramètres pour recevoir des notifications par SMS.');
                }
                
                // Redirect to the dashboard
                return redirect()->intended('/dashboard');
            }
        } catch (\Exception $e) {
            // Log the error for debugging
            Log::error('Google OAuth Error: ' . $e->getMessage());
    
            // Redirect back to login with error message
            return redirect('/login')->with('error', 'Failed to authenticate with Google: ' . $e->getMessage());
        }
    }
    
    /**
     * Redirect user based on their role after Google authentication
     */
    private function redirectUserBasedOnRole($user)
    {
        switch ($user->role) {
            case 'doctor':
                // For doctors, redirect to doctor dashboard
                return redirect()->intended('/doctor/dashboard');
                
            case 'patient':
                // For patients, check if phone needs to be updated
                if (strpos($user->phone, 'N/A-') === 0) {
                    return redirect()->route('patient-settings')
                        ->with('warning', 'Veuillez mettre à jour votre numéro de téléphone dans vos paramètres pour recevoir des notifications par SMS.');
                }
                return redirect()->intended('/patient/dashboard');
                
            case 'admin':
                return redirect()->intended('/admin');
                
            case 'centre_medical':
                return redirect()->intended('/centre-medical/dashboard');
                
            case 'pharmacy':
                return redirect()->intended('/pharmacy/dashboard');
                
            case 'assistante':
                // Assistante needs to select a doctor first
                return redirect()->intended('/assistante');
                
            default:
                return redirect()->intended('/dashboard');
        }
    }
}

<?php

namespace App\Http\Controllers\Doctor;

use App\Http\Controllers\Controller;
use App\Http\Requests\Prescription\CreatePrescriptionRequest;
use App\Services\PatientService\IPatientService;
use App\Services\PrescriptionService\IPrescriptionService;
use Barryvdh\DomPDF\Facade\Pdf;
class PrescriptionController extends Controller
{
    protected $prescriptionService;
    protected $patientService;
    public function __construct(IPrescriptionService $prescriptionService, IPatientService $patientService)
    {
        $this->prescriptionService = $prescriptionService;
        $this->patientService = $patientService;
    }
    public function index($id)
    {
        $patient = $this->patientService->get($id);
        return view(
            'pages.user-accounts.doctor.patient-profile.tabs.prescriptions.add-prescription',
            ['patient' => $patient]
        );
    }
    public function store(CreatePrescriptionRequest $request)
    {
        $this->prescriptionService->create($request->validated());

        return redirect()
            ->intended('/doctor/patient/' . $request->patient_id)
            ->with('success', trans("alerts.success.the_prescriptions_have_been_added_successfully"));
    }
    public function show($patient_id, $id)
    {
        $prescription = $this->prescriptionService->get($id);
        $patient = $this->patientService->get($patient_id);

        if ($prescription->patient_id != $patient_id) {
            return abort(404);
        }

        return view(
            'pages.user-accounts.doctor.patient-profile.tabs.prescriptions.view-prescription',
            ['patient' => $patient, 'prescription' => $prescription]
        );
    }
    public function download_prescription($id)
    {
        $prescription = $this->prescriptionService->get($id);
        $patient = $this->patientService->get($prescription->patient_id);

        $pdf = Pdf::loadView(
            'pages.user-accounts.doctor.components.template-docs.prescription-template',
            [
                'patient' => $patient, 'address' => $prescription->doctor->address,
                'prescription' => $prescription,
            ]
        )->setPaper('a5');
        return $pdf->download("prescription-" . time() . ".pdf");
    }
}

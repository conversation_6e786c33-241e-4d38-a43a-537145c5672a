<?php

namespace App\Http\Controllers\Doctor;

use App\Http\Controllers\Controller;
use App\Services\AppointmentService\IAppointmentService;
use App\Services\DoctorService\IDoctorService;
use Illuminate\Support\Facades\Auth;

class DoctorDashboardController extends Controller
{
    protected $appointmentService;
    protected $doctorService;
    public function __construct(IAppointmentService $appointmentService, IDoctorService $doctorService)
    {
        $this->appointmentService = $appointmentService;
        $this->doctorService = $doctorService;
    }
    public function index()
    {
        $appointmentRequests = $this->appointmentService->getDoctorAppointmentRequests(Auth::user()->doctor->id);
        $doctorStats = $this->doctorService->getStats(Auth::user()->doctor->id);
        return view(
            'pages.user-accounts.doctor.dashboard',
            compact('appointmentRequests'),
            $doctorStats
        );
    }
}

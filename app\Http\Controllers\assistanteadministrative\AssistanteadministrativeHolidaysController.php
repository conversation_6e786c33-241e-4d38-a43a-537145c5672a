<?php

namespace App\Http\Controllers\assistanteadministrative;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Services\DoctorService\IDoctorService;
use App\Http\Requests\Doctor\AddHolidayRequest;
use App\Http\Requests\Doctor\DeleteHolidayRequest;
use App\Models\Holiday;
use App\Models\Doctor;
use Illuminate\Support\Facades\Log;

class AssistanteadministrativeHolidaysController extends Controller
{
    protected $doctorService;

    public function __construct(IDoctorService $doctorService)
    {
        $this->doctorService = $doctorService;
    }

    public function index($doctorId = null)
    {
        $doctor = Doctor::find($doctorId);
        if (!$doctor || !$doctor->user) {
            abort(404, 'Doctor or user not found');
        }

        $user = $doctor->user->id;
        $holidays = Holiday::where('user_id', '=', $user)->get();
        return view('pages.user-accounts.assistante-administrative.holidays.index', compact('holidays', 'doctorId', 'user'));
    }

    public function store_holiday(AddHolidayRequest $req)
    {
        $doctorId = $req->input('doctor_id');
        $doctor = Doctor::find($doctorId);
        if (!$doctor || !$doctor->user) {
            return redirect()->back()->withErrors(['error' => 'Doctor or user not found']);
        }

        $user = $doctor->user;

        $this->doctorService->addHoliday(
            array_merge($req->validated(), ['user_id' => $user->id])
        );

        return redirect()->back()->with([
            'success' => trans("alerts.success.your_holiday_have_been_saved_successfully")
        ]);
    }

    public function destroy_holiday(DeleteHolidayRequest $req)
    {
        $holiday = Holiday::find($req->id);
        if ($holiday) {
            $holiday->delete();
        }

        return response()->json([
            'success' => trans("alerts.success.holiday_deleted")
        ]);
    }
}

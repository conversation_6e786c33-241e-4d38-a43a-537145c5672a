<?php

namespace App\Http\Controllers\Doctor;

use App\Http\Controllers\Controller;
use App\Http\Requests\Doctor\AddEducationRequest;
use App\Http\Requests\Doctor\AddExperienceRequest;
use App\Http\Requests\Doctor\DeleteClinicImageRequest;
use App\Http\Requests\Doctor\DeleteEducationRequest;
use App\Http\Requests\Doctor\DeleteExperienceRequest;
use App\Http\Requests\Doctor\UpdateClinicAddressRequest;
use App\Http\Requests\Doctor\UpdatePersonalInfoRequest;
use App\Http\Requests\Doctor\UpdateServicesRequest;
use App\Http\Requests\Doctor\UploadClinicImageRequest;
use App\Services\DoctorService\IDoctorService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

class DoctorSettingsController extends Controller
{
    protected $doctorService;
    public function __construct(IDoctorService $doctorService)
    {
        $this->doctorService = $doctorService;
    }
    public function index()
    {
        $doctor = $this->doctorService->get(Auth::user()->doctor->id);
        return view('pages.user-accounts.doctor.settings.settings', compact('doctor'));
    }
    public function updatePersonalInfo(UpdatePersonalInfoRequest $request)
    {
        $data = $request->validated();
        if ($request->hasFile('image')) {
            $image = $request->file('image')->store('doctors_images');
            $data['image_url'] = $image;
        }
        $this->doctorService->updatePersonalInfo(
            $data,
            Auth::id()
        );
        return redirect()->back()->with([
            'success' => trans("alerts.success.your_information_have_been_saved_successfully")
        ]);
    }

    public function updateClinicAddress(UpdateClinicAddressRequest $request)
    {
        $this->doctorService->updateClinicInfo(
            $request->validated(),
            Auth::id()
        );
        session()->flash(
            'success',
            trans("alerts.success.your_clinic_address_has_been_saved")
        );
        return response()->json();
    }
    public function store_clinic_image(UploadClinicImageRequest $request)
    {
        $path = $request->file('file')->store('clinics_images');
        $this->doctorService->addClinicImage([
            'image_url' => $path,
            'id' => $request->address_id
        ]);
        session()->flash(
            'success',
            trans("alerts.success.image_added")
        );
        return response()->json();
    }

    public function delete_clinic_image(DeleteClinicImageRequest $request)
    {
        $this->doctorService->deleteClinicImage($request->id);
        return response()->json([
            'success' => trans("alerts.success.image_deleted")
        ]);
    }

    public function updateServices(UpdateServicesRequest $req)
    {
        $this->doctorService->updateServices(
            $req->validated()['services'] ?? '',null
        );
        return redirect()->back()->with([
            "success" => trans("alerts.success.your_services_have_been_save_successfully")
        ])->withFragment('services-section');
    }

    public function store_education(AddEducationRequest $req)
    {
        $this->doctorService->addEducation(
            $req->validated()
        );

        return redirect()->back()->with([
            'success' => trans("alerts.success.your_education_have_been_saved_successfully")
        ])->withFragment('education-section');
    }

    public function store_experience(AddExperienceRequest $req)
    {
        $this->doctorService->addExperience(
            $req->validated()
        );
        return redirect()->back()->with([
            'success' => trans("alerts.success.your_experience_have_been_saved_successfullyy")
        ])->withFragment('experience-section');
    }
    public function destory_education(DeleteEducationRequest $req)
    {
        $this->doctorService->deleteEducation($req->id);
        return response()->json([
            'success' => trans("alerts.success.education_deleted")
        ]);
    }
    public function destory_experience(DeleteExperienceRequest $req)
    {
        $this->doctorService->deleteExperience($req->id);
        if(Auth::user()->role !='admin'){

                return response()->json([
            'success' => trans("alerts.success.experience_deleted")
        ]);

        }
 Log::info('Admin deleted experience');


    }


    public function saveAll(Request $request)
    {
        $data = $request->all();

        // Update personal info
        if ($request->hasFile('image')) {
            $image = $request->file('image')->store('doctors_images', 'public');
            $data['image_url'] = $image;
        }
        $this->doctorService->updatePersonalInfo($data, Auth::id());

        // Validate postal code is numeric before any DB update
        if (isset($data['postal_code']) && !is_numeric($data['postal_code'])) {
            return redirect()->back()->withErrors(['postal_code' => 'Postal code must be numeric.'])->withInput();
        }

        // Update clinic address
        $clinicData = [
            'office_name' => $data['clinic_name'] ?? '',
            'address' => $data['address'] ?? '',
            'city' => $data['city'] ?? '',
            'district' => $data['district'] ?? '',
            'postal_code' => $data['postal_code'] ?? '',
            'country' => $data['country'] ?? '',
        ];
        $this->doctorService->updateClinicInfo($clinicData, Auth::id());

        // Handle header/footer images
        $addressId = $data['address_id'] ?? null;
        $headerPath = null;
        $footerPath = null;

        if ($request->hasFile('header_image')) {
            $headerPath = $request->file('header_image')->store('address_images', 'public');
        }
        if ($request->hasFile('footer_image')) {
            $footerPath = $request->file('footer_image')->store('address_images', 'public');
        }
        if ($addressId) {
            $this->doctorService->saveHeaderFooterImages($addressId, $headerPath, $footerPath);
        }

        // Handle clinic images
        if ($request->hasFile('clinic_images')) {
            foreach ($request->file('clinic_images') as $file) {
                $path = $file->store('clinics_images', 'public');
                $this->doctorService->addClinicImage([
                    'image_url' => $path,
                    'id' => $addressId,
                ]);
            }
        }

        // Update services
        $this->doctorService->updateServices($data['services'] ?? '', null);

        // Update education
        if (isset($data['degree'])) {
            foreach ($data['degree'] as $index => $degree) {
                $educationData = [
                    'degree' => $degree,
                    'user_id' => Auth::id(),
                ];
                $this->doctorService->addEducation($educationData);
            }
        }

        // Update experience
        if (isset($data['hospital_name'])) {
            foreach ($data['hospital_name'] as $index => $hospital_name) {
                $experienceData = [
                    'hospital_name' => $hospital_name,
                    'user_id' => Auth::id(),
                ];
                $this->doctorService->addExperience($experienceData);
            }
        }

        return redirect()->back()->with([
            'success' => trans("alerts.success.added_successfully")
        ]);
    }
}

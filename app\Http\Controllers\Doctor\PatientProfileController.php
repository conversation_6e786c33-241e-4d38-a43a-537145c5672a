<?php

namespace App\Http\Controllers\Doctor;

use App\Http\Controllers\Controller;
use App\Services\AppointmentService\IAppointmentService;
use App\Services\DoctorService\IDoctorService;
use App\Services\MedicalCertificateService\IMedicalCertificateService;
use App\Services\PatientService\IPatientService;
use App\Services\PrescriptionService\IPrescriptionService;
use App\Services\DentalprocedureService\IDentalprocedureService;
use App\Models\PreConsultationForm;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class PatientProfileController extends Controller
{
    protected $appointmentService;
    protected $doctorService;
    protected $patientService;
    protected $prescriptionService;
    protected $medicalCertificateService;
    protected $dentalprocedureService;

    public function __construct(
        IAppointmentService $appointmentService,
        IDoctorService $doctorService,
        IPatientService $patientService,
        IPrescriptionService $prescriptionService,
        IMedicalCertificateService $medicalCertificateService,
        IDentalprocedureService $dentalprocedureService
    ) {
        $this->appointmentService = $appointmentService;
        $this->doctorService = $doctorService;
        $this->patientService = $patientService;
        $this->prescriptionService = $prescriptionService;
        $this->medicalCertificateService = $medicalCertificateService;
        $this->dentalprocedureService = $dentalprocedureService;
    }
    public function index($id)
    {
        $patient = $this->patientService->get($id);
        $medical_certificates = $this->medicalCertificateService->getAll($id);
        $prescriptions = $this->prescriptionService->getPatientPrescriptions($id);
        $appointments = $this->appointmentService->getDoctorPatientAppointments(Auth::user()->doctor->id, $id);
        $dentalprocedures = $this->dentalprocedureService->getAll();

        // Get pre-consultation forms for this patient
        $appointmentIds = $appointments->pluck('id')->toArray();
        $preConsultationForms = PreConsultationForm::whereIn('appointment_id', $appointmentIds)
            ->orderBy('created_at', 'desc')
            ->get();

        // Current doctor ID
        $doctorId = Auth::user()->doctor->id;

        // Get shared ordonnances
        $sharedOrdonnances = DB::table('medical_record_accesses as mra')
            ->join('medical_records as mr', 'mr.id', '=', 'mra.medical_record_id')
            ->where('mra.doctor_id', $doctorId)
            ->where('mr.patient_id', $id)
            ->where('mr.document_type', 'ordonnance')
            ->select('mr.*', 'mra.created_at as shared_at', 'mra.note as access_note')
            ->orderByDesc('mra.created_at')
            ->get();

        // Get shared examens and imageries (both)
        $sharedExamens = DB::table('medical_record_accesses as mra')
            ->join('medical_records as mr', 'mr.id', '=', 'mra.medical_record_id')
            ->where('mra.doctor_id', $doctorId)
            ->where('mr.patient_id', $id)
            ->whereIn('mr.document_type', ['examen', 'imagerie']) // include both types
            ->select('mr.*', 'mra.created_at as shared_at', 'mra.note as access_note')
            ->orderByDesc('mra.created_at')
            ->get();


        // Optional: separate examens and imageries for Blade
        $examens = $sharedExamens->where('document_type', 'examen');
        $imageries = $sharedExamens->where('document_type', 'imagerie');

        return view('pages.user-accounts.doctor.patient-profile.index', compact(
            'patient',
            'appointments',
            'medical_certificates',
            'prescriptions',
            'dentalprocedures',
            'preConsultationForms',
            'sharedOrdonnances',
            'examens',
            'imageries'
        ));
    }

}

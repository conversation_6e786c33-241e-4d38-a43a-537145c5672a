<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\SMSService;
use App\Models\Appointment;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class SendAppointmentReminders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'appointments:send-reminders 
                            {type? : Reminder type (24h or 48h, default: both)}
                            {--appointment= : Process a specific appointment ID}
                            {--force : Force sending reminders even if outside time window}
                            {--date= : Find appointments on this date (YYYY-MM-DD)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send SMS reminders for upcoming appointments at 24h and 48h marks';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting appointment reminder check');
        
        $reminderType = $this->argument('type');
        $appointmentId = $this->option('appointment');
        $force = $this->option('force');
        $date = $this->option('date');
        
        try {
            $smsService = new SMSService();
            
            if ($date) {
                return $this->handleDateSearch($date, $smsService, $reminderType, $force);
            }
            
            if ($appointmentId) {
                return $this->handleSingleAppointment($appointmentId, $smsService, $reminderType, $force);
            }
            
            // Process normal reminders
            $stats = $smsService->sendAppointmentReminders($reminderType, null, $force);
            $this->displayReminderStats($stats, $reminderType);
            
            return 0;
        } catch (\Exception $e) {
            $this->error('Error: ' . $e->getMessage());
            Log::error('Error in reminder command: ' . $e->getMessage());
            return 1;
        }
    }
    
    /**
     * Handle processing a single appointment
     */
    private function handleSingleAppointment($appointmentId, $smsService, $reminderType, $force)
    {
        try {
            $appointment = Appointment::with(['doctor.user', 'patient.user', 'clinic', 'patient_clinique'])
                ->findOrFail($appointmentId);
            
            $this->displayAppointmentDetails($appointment);
            
            // Ask for confirmation if not forced
            if (!$force && !$this->confirm('Send reminder for this appointment?', true)) {
                $this->info('Operation canceled.');
                return 0;
            }
            
            $stats = $smsService->sendAppointmentReminders($reminderType, $appointmentId, $force);
            
            $this->info("Reminder processing complete for appointment #$appointmentId:");
            $this->info("Sent to patient: " . ($stats['sent_to_patients'] > 0 ? 'Yes' : 'No'));
            $this->info("Sent to doctor: " . ($stats['sent_to_doctors'] > 0 ? 'Yes' : 'No'));
            
            return 0;
        } catch (\Exception $e) {
            $this->error("Error processing appointment #$appointmentId: " . $e->getMessage());
            return 1;
        }
    }
    
    /**
     * Display appointment details
     */
    private function displayAppointmentDetails($appointment)
    {
        $this->info("Appointment details:");
        $this->info("Date: " . Carbon::parse($appointment->date)->format('Y-m-d H:i'));
        
        if ($appointment->patient && $appointment->patient->user) {
            $this->info("Patient: " . $appointment->patient->user->first_name . ' ' . $appointment->patient->user->last_name);
        } elseif ($appointment->patient_clinique) {
            $this->info("Patient: " . $appointment->patient_clinique->first_name . ' ' . $appointment->patient_clinique->last_name);
        }
        
        if ($appointment->doctor && $appointment->doctor->user) {
            $this->info("Doctor: " . $appointment->doctor->user->first_name . ' ' . $appointment->doctor->user->last_name);
        }
        
        $this->info("Status: " . $appointment->status);
    }
    
    /**
     * Display reminder statistics
     */
    private function displayReminderStats($stats, $reminderType)
    {
        $this->info('Appointment reminder check completed');
        
        if ($reminderType === null && isset($stats['types'])) {
            $this->info('24h reminders: ' . $stats['types']['24h']['processed'] . ' processed, ' . 
                       $stats['types']['24h']['sent_to_patients'] . ' to patients, ' . 
                       $stats['types']['24h']['sent_to_doctors'] . ' to doctors');
            
            $this->info('48h reminders: ' . $stats['types']['48h']['processed'] . ' processed, ' . 
                       $stats['types']['48h']['sent_to_patients'] . ' to patients, ' . 
                       $stats['types']['48h']['sent_to_doctors'] . ' to doctors');
        } else {
            $this->info("$reminderType reminders: {$stats['processed']} processed, " . 
                       "{$stats['sent_to_patients']} to patients, {$stats['sent_to_doctors']} to doctors");
        }
        
        if ($stats['errors'] > 0) {
            $this->warn("Encountered {$stats['errors']} errors");
        }
    }
    
    /**
     * Handle appointment search by date
     */
    protected function handleDateSearch($date, $smsService, $reminderType, $force)
    {
        try {
            $searchDate = Carbon::parse($date);
            $this->info("Searching for appointments on: " . $searchDate->format('Y-m-d'));
            
            $startOfDay = $searchDate->copy()->startOfDay();
            $endOfDay = $searchDate->copy()->endOfDay();
            
            $appointments = Appointment::whereBetween('date', [$startOfDay, $endOfDay])
                ->with(['doctor.user', 'patient.user', 'clinic', 'patient_clinique'])
                ->get();
                
            if ($appointments->isEmpty()) {
                $this->warn("No appointments found on " . $searchDate->format('Y-m-d'));
                return 0;
            }
            
            $this->info("Found " . $appointments->count() . " appointments on " . $searchDate->format('Y-m-d'));
            
            // Display the appointments
            $rows = [];
            foreach ($appointments as $appointment) {
                $patientName = 'Unknown';
                if ($appointment->patient && $appointment->patient->user) {
                    $patientName = $appointment->patient->user->first_name . ' ' . $appointment->patient->user->last_name;
                } elseif ($appointment->patient_clinique) {
                    $patientName = $appointment->patient_clinique->first_name . ' ' . $appointment->patient_clinique->last_name;
                }
                
                $doctorName = 'Unknown';
                if ($appointment->doctor && $appointment->doctor->user) {
                    $doctorName = $appointment->doctor->user->first_name . ' ' . $appointment->doctor->user->last_name;
                }
                
                $rows[] = [
                    $appointment->id,
                    Carbon::parse($appointment->date)->format('H:i'),
                    $patientName,
                    $doctorName,
                    $appointment->status,
                    $appointment->type
                ];
            }
            
            $this->table(['ID', 'Time', 'Patient', 'Doctor', 'Status', 'Type'], $rows);
            
            // Ask which appointment to test
            if ($this->confirm('Do you want to send test reminders for any of these appointments?', true)) {
                $appointmentId = $this->ask('Enter the appointment ID to test');
                
                if (!$appointmentId) {
                    $this->info('No ID provided. Operation canceled.');
                    return 0;
                }
                
                // Reuse the same logic for a specific appointment
                $stats = $smsService->sendAppointmentReminders($reminderType, $appointmentId, $force);
                
                $this->info("Reminder processing complete for appointment #$appointmentId:");
                $this->info("Sent to patient: " . ($stats['sent_to_patients'] > 0 ? 'Yes' : 'No'));
                $this->info("Sent to doctor: " . ($stats['sent_to_doctors'] > 0 ? 'Yes' : 'No'));
            }
            
            return 0;
        } catch (\Exception $e) {
            $this->error('Error processing date search: ' . $e->getMessage());
            return 1;
        }
    }
}

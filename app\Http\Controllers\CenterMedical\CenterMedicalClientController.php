<?php

namespace App\Http\Controllers\CenterMedical;

use App\Http\Controllers\Controller;
use App\Services\CentreMedicalService\CentreMedicalService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Repositories\AddressRepository;
use App\Repositories\SpecialityRepositoryInterface;
use App\Services\DoctorToCentreMedical\DoctorToCentreMedical;
class CenterMedicalClientController  extends Controller
{
    protected $centreMedicalService;
    protected $doctorToCentreMedical;
    protected $addressRepository;
    protected $specialityRepository;
    public function __construct(
        CentreMedicalService $centreMedicalService,
        DoctorToCentreMedical $doctorToCentreMedical,
        AddressRepository $addressRepository,
        SpecialityRepositoryInterface $specialityRepository,
    ) {
        $this->centreMedicalService = $centreMedicalService;
        $this->doctorToCentreMedical = $doctorToCentreMedical;
        $this->addressRepository = $addressRepository;
        $this->specialityRepository = $specialityRepository;
    }
    public function getDoctors($city, $name)
    {
        // Convert URL parameters back to proper format
        $decodedCity = str_replace('-', ' ', $city);
        $decodedName = str_replace('-', ' ', $name);
        
        // Find medical center by city and name
        $medicalCenter = $this->centreMedicalService->getByNameAndCity($decodedName, $decodedCity);
        
        if (!$medicalCenter) {
            abort(404, 'Medical center not found');
        }
        
        $doctors = $this->doctorToCentreMedical->getDoctorsByCentre($medicalCenter->id);
        $translatedDoctors = $doctors->map(function ($doctor) {
        $doctor->translated_speciality = __('specialities.' . $doctor->name, [], 'fr');
            $doctor->url_district = strtolower(trim(preg_replace('/[^a-zA-Z0-9]+/', '-', $doctor->district)));
            $doctor->url_name = strtolower(trim(preg_replace('/[^a-zA-Z0-9]+/', '-', $doctor->first_name . '-' . $doctor->last_name)));
            return $doctor;
        });
        return view('pages.clinic.show', compact('medicalCenter', 'doctors'));
    }
    public function booking($city, $name)
{
    // Convert URL parameters back to proper format
    $decodedCity = str_replace('-', ' ', $city);
    $decodedName = str_replace('-', ' ', $name);
    
    // Find medical center by city and name
    $medicalCenter = $this->centreMedicalService->getByNameAndCity($decodedName, $decodedCity);
    
    if (!$medicalCenter) {
        abort(404, 'Medical center not found');
    }
    
    $doctors = $this->doctorToCentreMedical->getDoctorsByCentre($medicalCenter->id);
    $clinicSpecialities = $doctors->pluck('name')
        ->filter()
        ->unique()
        ->values();
    $specialities = $this->specialityRepository->all()
        ->whereIn('name', $clinicSpecialities);
    return view('pages.clinic.cliniqueStep', compact('medicalCenter', 'specialities'));
}
public function getDoctorsBySpeciality(Request $request, $city, $name)
{
    try {
        // Convert URL parameters back to proper format
        $decodedCity = str_replace('-', ' ', $city);
        $decodedName = str_replace('-', ' ', $name);
        
        // Find medical center by city and name
        $medicalCenter = $this->centreMedicalService->getByNameAndCity($decodedName, $decodedCity);
        
        if (!$medicalCenter) {
            return response()->json(['error' => 'Medical center not found'], 404);
        }
        
        $speciality = $request->query('speciality');
        $doctors = $this->doctorToCentreMedical->getDoctorsByCentre($medicalCenter->id)
            ->filter(function ($doctor) use ($speciality) {
                return $doctor->name === $speciality;
            })
            ->values();
        return response()->json($doctors);
    } catch (\Exception $e) {
        Log::error('Error getting doctors:', [
            'error' => $e->getMessage(),
            'centre_name' => $name,
            'centre_city' => $city,
            'speciality' => $speciality ?? 'unknown'
        ]);
        return response()->json([
            'error' => 'Error fetching doctors'
        ], 500);
    }
}
}

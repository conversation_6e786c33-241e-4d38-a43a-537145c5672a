<?php

namespace App\Http\Controllers\assistanteadministrative;

use App\Http\Controllers\Controller;
use App\Services\DoctorService\IDoctorService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

class AssistanteadministrativePatientsController extends Controller
{
    protected $doctorService;


    public function __construct(IDoctorService $doctorService)
    {
        $this->doctorService = $doctorService;
    }

    public function index(Request $request, $doctor_id)
    {
        $doctor = $this->doctorService->get($doctor_id);
        if (!$doctor) {
            return abort(404, 'Doctor not found');
        }

        $my_patients = $doctor->patients;

        return view('pages.user-accounts.assistante-administrative.patients', ['patients' => $my_patients, 'doctor_id' => $doctor_id]);
    }
}

<?php

namespace App\Http\Controllers\Auth;

use App\Models\User;
use App\Notifications\RappelNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\RedirectResponse;
use App\Http\Controllers\Controller;

class RappelMedecinController extends Controller
{

public function sendRappel(Request $request): RedirectResponse
{
    $request->validate([
        'email' => ['required', 'email'],
    ]);

    try {
        // Get doctor details with proper name fields
        $doctor = User::select('users.*')
                     ->where('email', $request->email)
                     ->first();

        if (!$doctor) {
            return back()->with('error', 'Médecin non trouvé.');
        }

        // Debug logging
        Log::info('Doctor details:', [
            'email' => $doctor->email,
            'first_name' => $doctor->first_name,
            'last_name' => $doctor->last_name,
            'full_object' => $doctor->toArray()
        ]);

        // Construct full name
        $fullName = $doctor->first_name . ' ' . $doctor->last_name;

       // Construct name components
$doctor->notify(new RappelNotification(
    $doctor->first_name,  // Pass first name
    $doctor->last_name,   // Pass last name
    $doctor->email,
    $doctor->speciality,
    $doctor->phone
));

        Log::info('Reminder email sent to doctor: ' . $doctor->email . ' (' . $fullName . ')');

        return back()->with('success', 'Rappel envoyé avec succès.');

    } catch (\Exception $e) {
        Log::error('Failed to send reminder email: ' . $e->getMessage());
        return back()->with('error', 'Erreur lors de l\'envoi du rappel.');
    }
}
}

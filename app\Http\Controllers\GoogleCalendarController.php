<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Google_Client;
use Google_Service_Calendar;
use Google_Service_Calendar_Calendar;
use App\Models\CalendarAccount;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class GoogleCalendarController extends Controller
{
    /**
     * Get a valid Google access token, refreshing if necessary
     */
    public function getValidToken($userId)
    {
        Log::info('Getting valid Google token for user', ['user_id' => $userId]);
        
        $account = DB::table('calendar_accounts')
            ->where('user_id', $userId)
            ->where('provider', 'google')
            ->first();

        if (!$account) {
            Log::error('No Google calendar account found for user', ['user_id' => $userId]);
            return null;
        }
        
        Log::info('Found Google calendar account', [
            'user_id' => $userId,
            'account_id' => $account->id,
            'has_access_token' => !empty($account->access_token),
            'has_refresh_token' => !empty($account->refresh_token),
            'token_expiry' => $account->token_expiry,
            'expires_at' => $account->token_expiry ? Carbon::parse($account->token_expiry)->toDateTimeString() : 'null',
            'is_expired' => $account->token_expiry ? now()->greaterThan($account->token_expiry) : 'unknown'
        ]);

        // Check if current token is valid
        if ($account->token_expiry && now()->lessThan($account->token_expiry) && !empty($account->access_token)) {
            Log::info('Using existing valid Google token', ['user_id' => $userId]);
            return $account->access_token;
        }

        // Token is expired or missing, try to refresh
        Log::info('Google token expired or missing, attempting refresh', ['user_id' => $userId]);
        $newToken = $this->refreshGoogleAccessToken($userId);
        
        if ($newToken) {
            Log::info('Successfully refreshed Google token', ['user_id' => $userId]);
            return $newToken;
        }
        
        Log::error('Failed to refresh Google token', ['user_id' => $userId]);
        return null;
    }

    /**
     * Refresh Google access token using refresh token
     */
    private function refreshGoogleAccessToken($userId)
    {
        Log::info('Attempting to refresh Google access token', ['user_id' => $userId]);
        
        $account = DB::table('calendar_accounts')
            ->where('user_id', $userId)
            ->where('provider', 'google')
            ->first();

        if (!$account || !$account->refresh_token) {
            Log::error('No Google refresh token found for user', [
                'user_id' => $userId,
                'account_exists' => !!$account,
                'has_refresh_token' => $account ? !empty($account->refresh_token) : false
            ]);
            return false;
        }

        try {
            $client = new Google_Client();
            $client->setClientId(config('services.google.client_id'));
            $client->setClientSecret(config('services.google.client_secret'));
            
            // Handle encrypted refresh tokens
            $refreshToken = $account->refresh_token;
            try {
                $refreshToken = decrypt($refreshToken);
            } catch (\Exception $e) {
                // Token is not encrypted, use as-is
            }
            
            Log::info('Making Google refresh token request', [
                'user_id' => $userId,
                'client_id' => config('services.google.client_id'),
                'has_refresh_token' => !empty($refreshToken)
            ]);
            
            $newToken = $client->fetchAccessTokenWithRefreshToken($refreshToken);
            
            if (!empty($newToken['access_token'])) {
                // Update token in database
                $updateData = [
                    'access_token' => $newToken['access_token'],
                    'token_expiry' => now()->addSeconds($newToken['expires_in'] ?? 3600),
                    'updated_at' => now(),
                ];
                
                // Update refresh token if provided
                if (!empty($newToken['refresh_token'])) {
                    $updateData['refresh_token'] = $newToken['refresh_token'];
                }
                
                DB::table('calendar_accounts')
                    ->where('id', $account->id)
                    ->update($updateData);

                Log::info('Successfully refreshed and updated Google access token', [
                    'user_id' => $userId,
                    'new_expiry' => $updateData['token_expiry']->toDateTimeString()
                ]);

                return $newToken['access_token'];
            }

            Log::error('Google refresh token failed: no access token in response', [
                'user_id' => $userId,
                'response_keys' => array_keys($newToken)
            ]);
            return false;

        } catch (\Exception $e) {
            Log::error('Google token refresh error', [
                'user_id' => $userId,
                'message' => $e->getMessage(),
                'code' => $e->getCode()
            ]);
            return false;
        }
    }

    /**
     * Clear Google tokens and account data for user
     */
    private function clearGoogleTokens($userId)
    {
        Log::info('Clearing Google tokens and account data for user', ['user_id' => $userId]);
        
        try {
            // Completely delete the Google calendar account record instead of just clearing tokens
            $deleted = DB::table('calendar_accounts')
                ->where('user_id', $userId)
                ->where('provider', 'google')
                ->delete();
            
            if ($deleted > 0) {
                Log::info('Successfully deleted Google account record', [
                    'user_id' => $userId,
                    'deleted_records' => $deleted
                ]);
            } else {
                Log::warning('No Google account records found to delete', ['user_id' => $userId]);
            }
            
        } catch (\Exception $e) {
            Log::error('Failed to delete Google account record', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get Google Calendar events with robust error handling
     */
    public function getGoogleEvents($userId = null)
    {
        $userId = $userId ?? auth()->id();
        Log::info('Getting Google events for user', ['user_id' => $userId]);
        
        $token = $this->getValidToken($userId);
        
        if (!$token) {
            Log::error('No valid Google token found for user', ['user_id' => $userId]);
            return response()->json(['error' => 'Authentication required'], 401);
        }

        try {
            $client = new Google_Client();
            $client->setClientId(config('services.google.client_id'));
            $client->setClientSecret(config('services.google.client_secret'));
            $client->setAccessToken(['access_token' => $token]);
            
            $service = new Google_Service_Calendar($client);
            
            // Get calendar ID from account
            $account = DB::table('calendar_accounts')
                ->where('user_id', $userId)
                ->where('provider', 'google')
                ->first();
            
            $calendarId = $account->external_calendar_id ?? 'primary';
            
            $events = $service->events->listEvents($calendarId, [
                'maxResults' => 50,
                'orderBy' => 'startTime',
                'singleEvents' => true,
                'timeMin' => now()->subMonths(1)->toRfc3339String(),
                'timeZone' => 'Africa/Casablanca', // Force Morocco timezone
            ]);

            $formatted = [];
            foreach ($events->getItems() as $event) {
                $start = $event->getStart();
                $end = $event->getEnd();
                
                $startDateTime = null;
                $endDateTime = null;
                $isAllDay = false;
                
                if ($start) {
                    if (!empty($start->dateTime)) {
                        $startDateTime = $start->dateTime;
                    } elseif (!empty($start->date)) {
                        $startDateTime = $start->date;
                        $isAllDay = true;
                    }
                }
                
                if ($end) {
                    if (!empty($end->dateTime)) {
                        $endDateTime = $end->dateTime;
                    } elseif (!empty($end->date)) {
                        $endDateTime = $end->date;
                    }
                }
                
                $formatted[] = [
                    'id' => $event->getId(),
                    'title' => $event->getSummary() ?? 'Sans titre',
                    'start' => $startDateTime,
                    'end' => $endDateTime,
                    'allDay' => $isAllDay,
                    'provider' => 'google',
                    'status' => 'from_google',
                    'description' => $event->getDescription() ?? '',
                ];
            }

            Log::info('Successfully retrieved Google events', ['count' => count($formatted)]);
            return response()->json($formatted);

        } catch (\Exception $e) {
            Log::error('Google Calendar API Error', [
                'user_id' => $userId,
                'message' => $e->getMessage(),
                'code' => $e->getCode()
            ]);
            
            // If it's an authentication error, try to refresh token and retry
            if (strpos($e->getMessage(), 'invalid_grant') !== false || 
                strpos($e->getMessage(), 'unauthorized') !== false ||
                $e->getCode() == 401) {
                
                Log::info('Received Google auth error, attempting token refresh', ['user_id' => $userId]);
                
                $refreshedToken = $this->refreshGoogleAccessToken($userId);
                if ($refreshedToken) {
                    Log::info('Google token refreshed successfully, retrying API call', ['user_id' => $userId]);
                    // Retry the API call with the new token
                    try {
                        $client->setAccessToken(['access_token' => $refreshedToken]);
                        $service = new Google_Service_Calendar($client);
                        
                        $account = DB::table('calendar_accounts')
                            ->where('user_id', $userId)
                            ->where('provider', 'google')
                            ->first();
                        
                        $calendarId = $account->external_calendar_id ?? 'primary';
                        
                        $events = $service->events->listEvents($calendarId, [
                            'maxResults' => 50,
                            'orderBy' => 'startTime',
                            'singleEvents' => true,
                            'timeMin' => now()->subMonths(1)->toRfc3339String(),
                            'timeZone' => 'Africa/Casablanca', // Force Morocco timezone
                        ]);

                        $formatted = [];
                        foreach ($events->getItems() as $event) {
                            $start = $event->getStart();
                            $end = $event->getEnd();
                            
                            $startDateTime = null;
                            $endDateTime = null;
                            $isAllDay = false;
                            
                            if ($start) {
                                if (!empty($start->dateTime)) {
                                    $startDateTime = $start->dateTime;
                                } elseif (!empty($start->date)) {
                                    $startDateTime = $start->date;
                                    $isAllDay = true;
                                }
                            }
                            
                            if ($end) {
                                if (!empty($end->dateTime)) {
                                    $endDateTime = $end->dateTime;
                                } elseif (!empty($end->date)) {
                                    $endDateTime = $end->date;
                                }
                            }
                            
                            $formatted[] = [
                                'id' => $event->getId(),
                                'title' => $event->getSummary() ?? 'Sans titre',
                                'start' => $startDateTime,
                                'end' => $endDateTime,
                                'allDay' => $isAllDay,
                                'provider' => 'google',
                                'status' => 'from_google',
                                'description' => $event->getDescription() ?? '',
                            ];
                        }

                        Log::info('Google retry successful after token refresh', ['count' => count($formatted)]);
                        return response()->json($formatted);
                        
                    } catch (\Exception $retryError) {
                        Log::error('Google retry failed even after token refresh', [
                            'user_id' => $userId,
                            'retry_error' => $retryError->getMessage()
                        ]);
                    }
                }
                
                // If refresh failed or retry failed, then clear tokens and force disconnect
                Log::warning('Google token refresh failed or retry failed, forcing disconnect', ['user_id' => $userId]);
                $this->forceDisconnect($userId);
            }
            
            return response()->json(['error' => 'Authentication expired', 'details' => $e->getMessage()], 401);
        }
    }

    public function redirectToGoogle()
    {
        $client = new Google_Client();
        $client->setClientId(config('services.google.client_id'));
        $client->setClientSecret(config('services.google.client_secret'));
        $client->setRedirectUri(config('services.google.redirect'));
        $client->setScopes([
            Google_Service_Calendar::CALENDAR,
            'email',
            'profile',
        ]);
        $client->setAccessType('offline');
        $client->setPrompt('consent');
        $authUrl = $client->createAuthUrl();
        return redirect($authUrl);
    }

    public function handleGoogleCallback(Request $request)
    {
        $user = Auth::user();
        if (!$user) {
            return redirect()->route('login')->with('error', 'Vous devez être connecté.');
        }

        if ($request->has('error')) {
            $errorDescription = $request->get('error_description', $request->get('error'));
            Log::error('Google OAuth Error', ['description' => $errorDescription]);
            return redirect()->route('doctor.calendar')
                ->with('error', "Erreur d'authentification Google: {$errorDescription}");
        }

        if (!$request->has('code')) {
            Log::error('Google OAuth Callback missing code');
            return redirect()->route('doctor.calendar')
                ->with('error', 'Erreur: Code d\'autorisation Google manquant');
        }

        try {
            $client = new Google_Client();
            $client->setClientId(config('services.google.client_id'));
            $client->setClientSecret(config('services.google.client_secret'));
            $client->setRedirectUri(config('services.google.redirect'));

            $token = $client->fetchAccessTokenWithAuthCode($request->get('code'));
            
            if (isset($token['error'])) {
                Log::error('Google token exchange error', ['error' => $token['error']]);
                return redirect()->route('doctor.calendar')
                    ->with('error', 'Erreur lors de l\'échange du code: ' . $token['error']);
            }

            if (!isset($token['access_token'])) {
                Log::error('Google token response missing access_token', ['response' => $token]);
                return redirect()->route('doctor.calendar')
                    ->with('error', 'Erreur: Token d\'accès Google manquant dans la réponse');
            }

            $client->setAccessToken($token);
            $oauth = new \Google_Service_Oauth2($client);
            $googleUser = $oauth->userinfo->get();

            // Check if DocExpress calendar already exists, if not create it
            $service = new \Google_Service_Calendar($client);
            $calendarId = null;
            
            try {
                // List all calendars to find existing DocExpress calendar
                $calendarList = $service->calendarList->listCalendarList();
                $existingCalendar = null;
                
                foreach ($calendarList->getItems() as $cal) {
                    if ($cal->getSummary() === 'DocExpress') {
                        $existingCalendar = $cal;
                        break;
                    }
                }
                
                if ($existingCalendar) {
                    // Use existing DocExpress calendar
                    $calendarId = $existingCalendar->getId();
                    Log::info('Found existing DocExpress calendar for user', [
                        'user_id' => $user->id,
                        'calendar_id' => $calendarId
                    ]);
                } else {
                    // Create new DocExpress calendar
                    $calendar = new \Google_Service_Calendar_Calendar();
                    $calendar->setSummary('DocExpress');
                    $calendar->setTimeZone('Africa/Casablanca'); // Force Morocco timezone
                    
                    $createdCalendar = $service->calendars->insert($calendar);
                    $calendarId = $createdCalendar->getId();
                    Log::info('Created new DocExpress calendar for user', [
                        'user_id' => $user->id,
                        'calendar_id' => $calendarId
                    ]);
                }
                
            } catch (\Exception $e) {
                Log::error('Error managing DocExpress calendar', [
                    'user_id' => $user->id,
                    'message' => $e->getMessage()
                ]);
                $calendarId = 'primary'; // fallback to primary calendar
            }

            // Stocker ou mettre à jour le compte calendrier avec l'ID du calendrier créé
            CalendarAccount::updateOrCreate([
                'user_id' => $user->id,
                'provider' => 'google',
            ], [
                'email' => $googleUser->email,
                'access_token' => $token['access_token'], // stocké en clair
                'refresh_token' => isset($token['refresh_token']) ? $token['refresh_token'] : null,
                'token_expiry' => now()->addSeconds($token['expires_in'] ?? 3600),
                'external_calendar_id' => $calendarId,
            ]);

            // Souscrire aux notifications push Google Calendar (watch)
            try {
                $service = new \Google_Service_Calendar($client);
                $channelId = uniqid('calendar_', true);
                $webhookUrl = route('google-calendar.webhook');
                $channel = new \Google_Service_Calendar_Channel([
                    'id' => $channelId,
                    'type' => 'web_hook',
                    'address' => $webhookUrl,
                ]);
                $service->events->watch('primary', $channel);
                Log::info('Successfully subscribed to Google Calendar webhook', [
                    'user_id' => $user->id,
                    'channel_id' => $channelId
                ]);
            } catch (\Exception $e) {
                Log::error('Erreur souscription webhook Google Calendar', [
                    'user_id' => $user->id,
                    'message' => $e->getMessage()
                ]);
            }

            // Synchronisation automatique après connexion
            try {
                $doctorCalendarController = app(\App\Http\Controllers\Doctor\CalendarController::class);
                $syncResponse = $doctorCalendarController->syncGoogle(new \Illuminate\Http\Request());
                if (is_object($syncResponse) && method_exists($syncResponse, 'getData')) {
                    $syncData = $syncResponse->getData();
                    $syncStatus = isset($syncData->success) && $syncData->success ? 'Synchronisation réussie avec Google Calendar.' : 'Erreur lors de la synchronisation Google Calendar.';
                } else {
                    $syncStatus = 'Synchronisation terminée.';
                }
            } catch (\Exception $e) {
                $syncStatus = 'Erreur lors de la synchronisation Google Calendar : ' . $e->getMessage();
                Log::error('Google Calendar sync after connection failed', [
                    'user_id' => $user->id,
                    'message' => $e->getMessage()
                ]);
            }

            Log::info('Google OAuth authentication successful', ['user_id' => $user->id]);
            return redirect('/doctor/dashboard')->with([
                'success' => 'Google Calendar connecté !',
                'calendar_sync_status' => $syncStatus
            ]);

        } catch (\Exception $e) {
            Log::error('Google OAuth General Error', [
                'user_id' => $user->id ?? 'unknown',
                'message' => $e->getMessage()
            ]);
            return redirect()->route('doctor.calendar')
                ->with('error', 'Erreur inattendue Google: ' . $e->getMessage());
        }
    }

    // Déconnexion Google
    public function logoutGoogle()
    {
        $userId = auth()->id();
        Log::info('Starting Google Calendar disconnection', ['user_id' => $userId]);
        
        try {
            // Get account before clearing tokens
            $account = DB::table('calendar_accounts')
                ->where('user_id', $userId)
                ->where('provider', 'google')
                ->first();
            
            if ($account && !empty($account->access_token)) {
                // Try to revoke the token on Google's servers
                try {
                    $client = new Google_Client();
                    $client->setClientId(config('services.google.client_id'));
                    $client->setClientSecret(config('services.google.client_secret'));
                    $client->setAccessToken(['access_token' => $account->access_token]);
                    
                    // Revoke the token
                    $client->revokeToken();
                    Log::info('Successfully revoked Google token on Google servers', ['user_id' => $userId]);
                    
                } catch (\Exception $e) {
                    Log::warning('Failed to revoke Google token on Google servers', [
                        'user_id' => $userId,
                        'error' => $e->getMessage()
                    ]);
                    // Continue with local cleanup even if revocation fails
                }
                
                // Clean up calendar events from our database
                try {
                    $deletedEvents = DB::table('calendar_events')
                        ->where('user_id', $userId)
                        ->where('provider', 'google')
                        ->delete();
                    
                    Log::info('Cleaned up Google calendar events from database', [
                        'user_id' => $userId,
                        'deleted_events' => $deletedEvents
                    ]);
                } catch (\Exception $e) {
                    Log::error('Failed to clean up Google calendar events', [
                        'user_id' => $userId,
                        'error' => $e->getMessage()
                    ]);
                }
            }
            
            // Clear tokens from our database
            $this->clearGoogleTokens($userId);
            
            Log::info('Google Calendar disconnection completed successfully', ['user_id' => $userId]);
            return redirect()->route('doctor.calendar')->with('success', 'Déconnexion Google Calendar réussie. Tous les événements synchronisés ont été supprimés.');
            
        } catch (\Exception $e) {
            Log::error('Error during Google Calendar disconnection', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            
            // Even if there's an error, try to clear local tokens
            $this->clearGoogleTokens($userId);
            
            return redirect()->route('doctor.calendar')->with('warning', 'Déconnexion Google Calendar effectuée avec des erreurs. Veuillez vérifier les logs.');
        }
    }

    /**
     * Force disconnect Google Calendar when authentication fails
     * This method can be called when tokens are invalid and refresh fails
     */
    public function forceDisconnect($userId)
    {
        Log::info('Force disconnecting Google Calendar due to authentication failure', ['user_id' => $userId]);
        
        try {
            // Clean up calendar events from our database
            $deletedEvents = DB::table('calendar_events')
                ->where('user_id', $userId)
                ->where('provider', 'google')
                ->delete();
            
            Log::info('Force disconnect: Cleaned up Google calendar events', [
                'user_id' => $userId,
                'deleted_events' => $deletedEvents
            ]);
            
            // Clear tokens from our database (no need to revoke on Google since they're invalid)
            $this->clearGoogleTokens($userId);
            
            Log::info('Force disconnect: Google Calendar disconnection completed', ['user_id' => $userId]);
            return true;
            
        } catch (\Exception $e) {
            Log::error('Error during forced Google Calendar disconnection', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            
            // Try to clear tokens even if event cleanup fails
            try {
                $this->clearGoogleTokens($userId);
            } catch (\Exception $clearError) {
                Log::error('Failed to clear tokens during forced disconnect', [
                    'user_id' => $userId,
                    'error' => $clearError->getMessage()
                ]);
            }
            
            return false;
        }
    }

    /**
     * Check if Google Calendar is properly connected for a user
     */
    public function isConnected($userId = null)
    {
        $userId = $userId ?? auth()->id();
        
        $account = DB::table('calendar_accounts')
            ->where('user_id', $userId)
            ->where('provider', 'google')
            ->first();
        
        // Check if account exists and has required tokens
        if (!$account || empty($account->access_token) || empty($account->refresh_token)) {
            return false;
        }
        
        // Check if token is not expired (with 5 minute buffer)
        if ($account->token_expiry && now()->addMinutes(5)->greaterThan($account->token_expiry)) {
            // Try to refresh the token
            $refreshed = $this->refreshGoogleAccessToken($userId);
            return $refreshed !== false;
        }
        
        return true;
    }
}

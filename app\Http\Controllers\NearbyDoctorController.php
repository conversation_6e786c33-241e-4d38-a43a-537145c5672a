<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Doctor;
use DB;

class NearbyDoctorController extends Controller
{
    public function index(Request $request)
    {
        $latitude = $request->input('latitude');
        $longitude = $request->input('longitude');
        // Validate inputs
        if (is_null($latitude) || is_null($longitude)) {
            return response()->json(['error' => 'Invalid coordinates'], 400);
        }
        $haversine = "(6371 * acos(cos(radians($latitude)) 
                      * cos(radians(latitude)) 
                      * cos(radians(longitude) - radians($longitude)) 
                      + sin(radians($latitude)) 
                      * sin(radians(latitude))))";
        // Retrieve doctors within 50 km radius
        $doctors = Doctor::select('*')
            ->selectRaw("$haversine AS distance")
            ->havingRaw('distance <= ?', [25]) // 50 km radius
            ->orderBy('distance')
            ->get();
        // Log the doctors and their distances for debugging
        foreach ($doctors as $doctor) {
            logger()->info('Doctor: ' . $doctor->name . ' - Distance: ' . $doctor->distance . ' km');
        }
        return view('pages.doctors.nearby', compact('doctors'));
    }
}

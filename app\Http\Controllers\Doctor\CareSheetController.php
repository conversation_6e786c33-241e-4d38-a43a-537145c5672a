<?php

namespace App\Http\Controllers\Doctor;

use App\Http\Controllers\Controller;
use App\Http\Requests\CareSheet\CreateCareSheetRequest;
use App\Services\CareSheetService\ICareSheetService;
use Illuminate\Support\Facades\Log;

class CareSheetController extends Controller
{
    protected $careSheetService;

    public function __construct(ICareSheetService $careSheetService)
    {
        $this->careSheetService = $careSheetService;
    }

    public function download_care_sheet(CreateCareSheetRequest $request)
    {
        try {
            $result = $this->careSheetService->download($request->validated());
            session()->flash('success', trans('alerts.success.care_sheet_downloaded_successfully'));
            return $result;
        } catch (\Exception $e) {
            Log::error('Care sheet download error: ' . $e->getMessage());
            session()->flash('error', 'Failed to generate care sheet. Please try again or contact support.');
            return redirect()->back();
        }
    }
}

<?php

namespace App\Http\Controllers\Patient;

use App\Http\Controllers\Controller;
use App\Http\Requests\Patient\UpdatePatientRequest;
use App\Services\PatientService\IPatientService;

class PatientSettingsController extends Controller
{
    protected $patientService;

    public function __construct(IPatientService $patientService)
    {
        $this->patientService = $patientService;
    }
    public function index()
    {
        $patient = $this->patientService->get(auth()->user()->patient->id);
        return view('pages.user-accounts.patient.settings', compact('patient'));
    }
    public function update(UpdatePatientRequest $request)
    {
        $data = $request->validated();
        // uplode image
        if ($request->hasFile('image')) {
            $image = $request->file('image')->store('patients_images');
            $data['image_url'] = $image;
        }
        $this->patientService->update(
            $data,
            auth()->user()->id
        );
        return redirect()->back()->with([
            'success' => trans("alerts.success.your_information_have_been_saved_successfully")
        ]);
    }
}

<?php

namespace App\Http\Controllers\Doctor;

use App\Http\Controllers\Controller;
use App\Services\DoctorToCentreMedical\IDoctorToCentreMedical;
use App\Services\CentreMedicalService\ICentreMedicalService;
use App\Http\Requests\ClinicDocTime\AddSlotTime;
use App\Http\Requests\ClinicDocTime\UpdateSlotTime;
use Illuminate\Support\Facades\Auth;
use App\Services\DocClinicTime\DocClinicTimeServiceInterface;
class DoctorClinicController extends Controller
{
    protected $clinicService;
    protected $centreMedicalService;
    protected $docClinicTimeService;
    public function __construct(
        IDoctorToCentreMedical $clinicService,
        ICentreMedicalService $centreMedicalService,
        DocClinicTimeServiceInterface $docClinicTimeService,

    ) {
        $this->clinicService = $clinicService;
        $this->centreMedicalService = $centreMedicalService;
        $this->docClinicTimeService = $docClinicTimeService;

    }
    public function index(){
        $clinics = $this->clinicService->getinClinicArea(Auth::user()->doctor->id);
        return view('pages.user-accounts.doctor.ClinicArea.index', compact('clinics'));
    }
    public function clinicShedule($id){
        $clinic = $this->centreMedicalService->get($id);
        $clinicsdoc = $this->clinicService->get($clinic->id)->first();
        $doctor = Auth::user()->doctor;
        $clinicsdoc = $this->clinicService->getforclinicTime($doctor->id, $clinic->id);
        if (!$clinicsdoc) {
            abort(404, 'Clinic or Doctor not found');
        }
        $doc_center_id = $clinicsdoc->id;
   
        $slots = $this->docClinicTimeService->getByDoctorCenter($doc_center_id);
        return view('pages.user-accounts.doctor.ClinicArea.shedule', compact('clinic', 'doc_center_id', 'slots'));
    }
    public function addTimeSlot(AddSlotTime $request)
    {
        $data = [
            'centre_medical_doctor_id' => $request->centre_medical_doctor_id,
            'day' => $request->day,
            'start_time' => $request->start_time,
            'end_time' => $request->end_time,
        ];
        $timeSlot = $this->docClinicTimeService->createDocClinicTime($data);
        return response()->json(['success' => 'Time slot added successfully', 'id' => $timeSlot->id]);
    }
    public function editTimeSlot(UpdateSlotTime $request, $id)
    {
        $this->docClinicTimeService->updateDocClinicTime($id, $request->all());
        return response()->json(['success' => 'Time slot updated successfully']);
    }
    public function deleteTimeSlot($id)
    {
        $this->docClinicTimeService->deleteDocClinicTime($id);
        return response()->json(['success' => 'Time slot deleted successfully']);
    }
}

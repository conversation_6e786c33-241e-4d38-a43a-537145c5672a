<?php

namespace App\Http\Controllers\Pharmacy;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\Pharmacy\SearchRequest;
use App\Services\PharmacyService\PharmacyService;
use App\Http\Requests\Pharmacy\UpdatePharmacyRequest;
use App\Models\Prescription;
use App\Services\PatientService\PatientService;
use App\Services\PrescriptionService\PrescriptionService;
use Illuminate\Support\Facades\Auth;

class PharmacyController extends Controller
{

    protected $pharmacyService;
    protected $patientService;
    protected $prescriptionService;

    public function __construct(PharmacyService $pharmacyService, PatientService $patientService,PrescriptionService $prescriptionService)
    {
        $this->pharmacyService = $pharmacyService;
        $this->patientService = $patientService;
        $this->prescriptionService = $prescriptionService;
    }
    public function index()
    {
        return view('pages.user-accounts.pharmacy.dashboard');
    }
    public function edit()
    {
        $pharmacy = $this->pharmacyService->get(Auth::user()->pharmacy->id);
        return view('pages.user-accounts.pharmacy.settings'
        ,[
            'pharmacy' => $pharmacy
        ]);
    }
    public function update(UpdatePharmacyRequest $request)
    {
        $data = $request->validated();
        // uplode image
        if ($request->hasFile('image')) {
            $image = $request->file('image')->store('pharmacy_images');
            $data['image_url'] = $image;
        }
        $this->pharmacyService->update(
            $data,
            auth()->user()->id
        );
        return redirect()->route('settings')->with([
            'success' => trans("alerts.success.pharmacy_updated_successfully")
        ]);
    }
    public function searchPatient(SearchRequest $request)
    {
        $user = $this->pharmacyService->search($request->cin);
        return view('pages.user-accounts.pharmacy.dashboard', [
            'patient' => $user != null ? $user->patient : null,
        ]);
    }

    public function getPatient($id)
    {
        $patient = $this->patientService->getPatientWithoutUser($id);
        return view('pages.user-accounts.pharmacy.profile-patient.index',[
            'patient'=> $patient
        ]);
    }
    public function getPrescrption($pati_id,$presc_id)
    {
        $patient = $this->patientService->get($pati_id);
        $prescription = $this->prescriptionService->get($presc_id);
        return view('pages.user-accounts.pharmacy.profile-patient.prescription',[
            'prescription'=> $prescription,
            'patient'=>$patient
        ]);
    }

}

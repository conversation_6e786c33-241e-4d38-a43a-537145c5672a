<?php

namespace App\Http\Middleware;

use Closure;
use App\Models\Doctor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PatientBookMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        if($request->method()=="POST")
        {
            $doctorId = $request->doctor_id;
        }
        else
        {
            $doctorId = $request->route('id');
        }

        $doctor = Doctor::find($doctorId);

        // Check & verify with route, you will more understand
        if ($doctor->offre != "basic") {

            return $next($request);
        }

        return abort(401);
    }
}


<?php

namespace App\Http\Controllers\assistanteadministrative;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use App\Services\CentreMedicalService\CentreMedicalService;
use App\Services\DoctorToCentreMedical\DoctorToCentreMedical;
use App\Models\CentreMedical;

class SelectCentremedicalController extends Controller
{
    protected $centreMedicalService;
    protected $doctorToCentreMedical;

    public function __construct(
        CentreMedicalService $centreMedicalService,
        DoctorToCentreMedical $doctorToCentreMedical
    ) {
        $this->centreMedicalService = $centreMedicalService;
        $this->doctorToCentreMedical = $doctorToCentreMedical;
    }


    public function getclinicsforAssitant(Request $request)
    {
       $centrestodoc = $this->doctorToCentreMedical->getCentresByAssistant(Auth::user()->assistante->id);
    $centerdata = $this->centreMedicalService->get($centrestodoc->first()->centre_medical_id);
        return view('pages.assistante-administrative.clinicindex', compact('centerdata'));
    }
    public function selectCenter(Request $request, $centerid)
    {
        $center = $this->centreMedicalService->get($centerid);
        if (!$center) {
            abort(404, 'Doctor not found');
        }

        $request->session()->put('selected_clinic_id', $centerid);
        return redirect()->route('assistante.dashboard.clinic', ['doctorId' => $centerid]);
    }
}

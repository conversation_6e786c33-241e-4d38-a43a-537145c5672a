<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\Patient\CreatePatientRequest;
use App\Http\Requests\Patient\UpdatePatientByAdminRequest;
use App\Services\PatientService\IPatientService;

class PatientController extends Controller
{
    protected $patientService;

    public function __construct(IPatientService $patientService)
    {
        $this->patientService = $patientService;
    }

    public function index()
    {
        $patients = $this->patientService->all();

        return view('pages.admin.patients.index', compact('patients'));
    }

    public function store(CreatePatientRequest $request)
    {
        $this->patientService->create($request->validated());

        return redirect()->route('patients.index')->with([
            'success' => trans("alerts.success.patient_added_successfully")
        ]);
    }

    public function edit(Request $request)
    {

        $patient = $this->patientService->get($request->id);

        return response()->json($patient);
    }


    public function update(UpdatePatientByAdminRequest $request)
    {
        $this->patientService->update($request->validated(), $request->id);

        return redirect()->route('patients.index')->with([
            'success' => trans("alerts.success.patient_updated_successfully")
        ]);
    }

    public function block(Request $request)
    {
        $this->patientService->block($request->id);
        return response()->json(false);
    }

    public function unblock(Request $request)
    {
        $this->patientService->unblock($request->id);
        return response()->json(true);
    }
}

<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Google_Service_Calendar;
use Illuminate\Support\Facades\Log;
use App\Models\CalendarEvent;

class GoogleCalendarWebhookController extends Controller
{
    // Endpoint pour recevoir les notifications push Google Calendar
    public function handle(Request $request)
    {
        // Google envoie un header X-Goog-Resource-State (ex: exists, sync, deleted)
        $resourceState = $request->header('X-Goog-Resource-State');
        $eventId = $request->header('X-Goog-Resource-Id');
        $calendarId = $request->header('X-Goog-Channel-Id');

        Log::info('Google Calendar Webhook reçu', [
            'resource_state' => $resourceState,
            'event_id' => $eventId,
            'calendar_id' => $calendarId,
        ]);

        // Recherche des comptes Google liés
        $calendarAccounts = \App\Models\CalendarAccount::where('provider', 'google')->get();
        foreach ($calendarAccounts as $account) {
            try {
                $client = new \Google_Client();
                $client->setClientId(config('services.google.client_id'));
                $client->setClientSecret(config('services.google.client_secret'));
                $client->setRedirectUri(config('services.google.redirect'));
                $client->setAccessToken(decrypt($account->access_token));
                if ($client->isAccessTokenExpired() && $account->refresh_token) {
                    $client->fetchAccessTokenWithRefreshToken(decrypt($account->refresh_token));
                }
                $service = new \Google_Service_Calendar($client);
                $events = $service->events->listEvents('primary', [
                    'maxResults' => 10,
                    'orderBy' => 'startTime',
                    'singleEvents' => true,
                    'timeMin' => now()->subDays(1)->toRfc3339String(),
                ]);
                foreach ($events->getItems() as $event) {
                    // Vérifier si l'événement existe déjà dans calendar_events
                    $calendarEvent = \App\Models\CalendarEvent::where('provider', 'google')
                        ->where('external_event_id', $event->getId())
                        ->first();
                    if ($event->getStatus() === 'cancelled') {
                        if ($calendarEvent) {
                            $calendarEvent->update(['sync_status' => 'deleted', 'last_synced_at' => now()]);
                            // Optionnel : supprimer le rendez-vous interne
                        }
                    } else {
                        if ($calendarEvent) {
                            // Mettre à jour le rendez-vous interne si besoin
                            $calendarEvent->update(['sync_status' => 'synced', 'last_synced_at' => now()]);
                        } else {
                            // Créer un nouvel enregistrement si besoin
                            \App\Models\CalendarEvent::create([
                                'user_id' => $account->user_id,
                                'appointment_id' => null, // à lier si possible
                                'provider' => 'google',
                                'external_event_id' => $event->getId(),
                                'sync_status' => 'synced',
                                'last_synced_at' => now(),
                            ]);
                        }
                    }
                }
            } catch (\Exception $e) {
                \Log::error('Erreur synchronisation webhook Google Calendar : ' . $e->getMessage());
            }
        }

        return response()->json(['status' => 'ok']);
    }
}

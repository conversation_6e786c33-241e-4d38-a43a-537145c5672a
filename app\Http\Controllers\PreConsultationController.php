<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Appointment;
use App\Models\PreConsultationForm;
use App\Models\MedicalRecord;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;

class PreConsultationController extends Controller
{
    /**
     * Display the pre-consultation form for a specific appointment
     */    public function show($appointmentId)
    {
        // First, try to find the appointment
        try {
            $appointment = Appointment::with(['doctor.user', 'patient.user', 'preConsultationForm', 'clinic'])
                ->findOrFail($appointmentId);
        } catch (\Exception $e) {
            Log::error('Error finding appointment: ' . $e->getMessage());
            abort(404, 'Rendez-vous non trouvé.');
        }
        
        // Check if user is authorized to view this form
        $user = Auth::user();
        $canView = false;
        
        if ($user->role === 'patient' && $appointment->patient && $appointment->patient->user_id === $user->id) {
            $canView = true;
        } elseif ($user->role === 'doctor' && $appointment->doctor && $appointment->doctor->user_id === $user->id) {
            $canView = true;
        } elseif (in_array($user->role, ['admin', 'assistante-administrative'])) {
            $canView = true;
        }
        
        if (!$canView) {
            abort(403, 'Vous n\'êtes pas autorisé à voir ce formulaire.');
        }
        
        // Get or create pre-consultation form
        try {
            $preConsultationForm = $appointment->preConsultationForm;
            if (!$preConsultationForm) {
                $preConsultationForm = PreConsultationForm::create([
                    'appointment_id' => $appointment->id,
                    'is_completed' => false
                ]);
            }
            
            return view('pages.user-accounts.patient.pre-consultation-form', compact('appointment', 'preConsultationForm'));
            
        } catch (\Exception $e) {
            Log::error('Error creating/displaying pre-consultation form: ' . $e->getMessage());
            abort(500, 'Erreur lors du chargement du formulaire.');
        }
    }
    
    /**
     * Store the pre-consultation form data
     */
    public function store(Request $request, $appointmentId)
    {
        try {
            $appointment = Appointment::with(['patient.user', 'doctor.user'])->findOrFail($appointmentId);
            
            // Validate user authorization
            $user = Auth::user();
            if ($user->role === 'patient' && $appointment->patient && $appointment->patient->user_id !== $user->id) {
                return response()->json(['error' => 'Non autorisé'], 403);
            }
              // Validate form data
            $validatedData = $request->validate([
                'consultation_reason' => 'required|string|max:255',
                'symptoms' => 'required|string',
                'symptom_duration' => 'required|string|max:100',
                'pain_level' => 'nullable|integer|min:0|max:10',
                'medical_history' => 'nullable|string',
                'current_medications' => 'nullable|string',
                'allergies' => 'nullable|string',
                'emergency_questions' => 'nullable|array',
            ]);
            
            // Get or create pre-consultation form
            $preConsultationForm = $appointment->preConsultationForm;
            if (!$preConsultationForm) {
                $preConsultationForm = new PreConsultationForm(['appointment_id' => $appointment->id]);
            }
              // Update form data
            $preConsultationForm->fill($validatedData);
            $preConsultationForm->is_completed = true;
            $preConsultationForm->completed_at = now();
            $preConsultationForm->save();              // Generate PDF
            $pdfPath = $this->generatePDF($preConsultationForm, $appointment);
            if ($pdfPath) {
                $preConsultationForm->pdf_path = $pdfPath;
                $preConsultationForm->save();
                
                // Save to medical records
                $this->saveMedicalRecord($appointment, $pdfPath, $preConsultationForm);
            }            
            
            // Check if request is AJAX
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Formulaire de pré-consultation enregistré avec succès. Le PDF médical a été généré.',
                    'pdf_path' => $pdfPath
                ]);
            }
            
            // For non-AJAX requests, redirect back with success message
            return redirect()->back()->with('success', 'Formulaire de pré-consultation enregistré avec succès. Le PDF médical a été généré.');
              } catch (\Exception $e) {
            Log::error('Error saving pre-consultation form: ' . $e->getMessage());
            
            // Provide more specific error messages based on the exception
            $errorMessage = 'Erreur lors de l\'enregistrement du formulaire. Veuillez réessayer.';
            
            if (str_contains($e->getMessage(), 'Route') && str_contains($e->getMessage(), 'not defined')) {
                $errorMessage = 'Erreur de configuration du système. Veuillez contacter le support technique.';
            } elseif (str_contains($e->getMessage(), 'SQLSTATE')) {
                $errorMessage = 'Erreur de base de données. Vos données n\'ont pas pu être sauvegardées.';
            } elseif (str_contains($e->getMessage(), 'PDF')) {
                $errorMessage = 'Le formulaire a été sauvegardé mais la génération du PDF a échoué.';
            }
            
            return response()->json([
                'error' => $errorMessage,
                'technical_details' => config('app.debug') ? $e->getMessage() : null,
                'timestamp' => now()->toISOString()
            ], 500);
        }
    }
    
    /**
     * Generate PDF from pre-consultation form data
     */
    private function generatePDF(PreConsultationForm $form, Appointment $appointment)
    {
        try {
            $data = [
                'form' => $form,
                'appointment' => $appointment,
                'patient' => $appointment->patient,
                'doctor' => $appointment->doctor,
                'generated_at' => now()->format('d/m/Y H:i'),
                'locale' => app()->getLocale(),
                'isRtl' => in_array(app()->getLocale(), ['ar'])
            ];
            
            $pdf = PDF::loadView('pdf.pre-consultation-form', $data);
            
            // Set PDF direction for RTL languages
            if (in_array(app()->getLocale(), ['ar'])) {
                $pdf->setOption('enable-smart-shrinking', true);
                $pdf->setOption('direction', 'rtl');
            }
            
            // Create filename
            $filename = 'pre-consultation-' . $appointment->id . '-' . now()->format('Y-m-d-H-i-s') . '.pdf';
            $filepath = 'medical_records/' . $filename;
            
            // Save PDF
            Storage::disk('public')->put($filepath, $pdf->output());
            
            return $filepath;
            
        } catch (\Exception $e) {
            Log::error('Error generating pre-consultation PDF: ' . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Save pre-consultation form as medical record
     */
    private function saveMedicalRecord(Appointment $appointment, string $pdfPath, PreConsultationForm $form)
    {
        try {
            MedicalRecord::create([
                'patient_id' => $appointment->patient_id,
                'doctor_id' => $appointment->doctor_id,
                'appointment_id' => $appointment->id,
                'type' => 'pre_consultation',
                'title' => 'Formulaire de Pré-consultation - ' . Carbon::parse($appointment->date)->format('d/m/Y'),
                'description' => 'Formulaire de pré-consultation rempli par le patient avant le rendez-vous.',
                'file_path' => $pdfPath,
                'file_type' => 'pdf',
                'created_at' => now(),
                'updated_at' => now()
            ]);
            
        } catch (\Exception $e) {
            Log::error('Error saving medical record: ' . $e->getMessage());
        }
    }
    
    /**
     * Download the pre-consultation form PDF
     */
    public function downloadPDF($appointmentId)
    {
        try {
            $appointment = Appointment::with(['preConsultationForm'])->findOrFail($appointmentId);
            
            // Check authorization
            $user = Auth::user();
            $canDownload = false;
            
            if ($user->role === 'patient' && $appointment->patient && $appointment->patient->user_id === $user->id) {
                $canDownload = true;
            } elseif ($user->role === 'doctor' && $appointment->doctor && $appointment->doctor->user_id === $user->id) {
                $canDownload = true;
            } elseif (in_array($user->role, ['admin', 'assistante-administrative'])) {
                $canDownload = true;
            }
            
            if (!$canDownload) {
                return redirect()->back()->with('error', 'Non autorisé à télécharger ce fichier.');
            }
            
            $form = $appointment->preConsultationForm;
            if (!$form || !$form->pdf_path || !Storage::disk('public')->exists($form->pdf_path)) {
                return redirect()->back()->with('error', 'Fichier PDF non trouvé.');
            }
            
            $filePath = storage_path('app/public/' . $form->pdf_path);
            return response()->download($filePath, 'pre-consultation-form.pdf');
            
        } catch (\Exception $e) {
            Log::error('Error downloading pre-consultation PDF: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Erreur lors du téléchargement du fichier.');
        }
    }
    
    /**
     * Display the pre-consultation form with token verification (for email links)
     */    public function showWithToken(Request $request, $appointmentId)
    {
        $token = $request->query('token');
        
        if (!$token) {
            abort(403, 'Token d\'accès requis.');
        }
        
        // Try to find the appointment
        try {
            $appointment = Appointment::with(['doctor.user', 'patient.user', 'preConsultationForm', 'clinic'])
                ->findOrFail($appointmentId);
        } catch (\Exception $e) {
            Log::error('Error finding appointment for token access: ' . $e->getMessage());
            abort(404, 'Rendez-vous non trouvé.');
        }
        
        // Verify token
        $expectedToken = hash('sha256', $appointment->id . $appointment->created_at . config('app.key'));
        
        if (!hash_equals($expectedToken, $token)) {
            abort(403, 'Token d\'accès invalide.');
        }
        
        // Check if appointment is still valid (not older than 30 days)
        if ($appointment->created_at->diffInDays(now()) > 30) {
            abort(403, 'Ce lien a expiré.');
        }
        
        // Get or create pre-consultation form
        try {
            $preConsultationForm = $appointment->preConsultationForm;
            if (!$preConsultationForm) {
                $preConsultationForm = PreConsultationForm::create([
                    'appointment_id' => $appointment->id,
                    'is_completed' => false
                ]);
            }
            
            return view('pages.user-accounts.patient.pre-consultation-form', compact('appointment', 'preConsultationForm'));
            
        } catch (\Exception $e) {
            Log::error('Error creating/displaying pre-consultation form with token: ' . $e->getMessage());
            abort(500, 'Erreur lors du chargement du formulaire.');
        }
    }
    
    /**
     * Store pre-consultation form data with token authentication
     */
    public function storeWithToken(Request $request, $appointmentId)
    {
        try {
            $token = $request->input('token');
            
            if (!$token) {
                return response()->json(['error' => 'Token d\'accès requis.'], 403);
            }
            
            $appointment = Appointment::with(['patient.user', 'doctor.user'])->findOrFail($appointmentId);
            
            // Verify token
            $expectedToken = hash('sha256', $appointment->id . $appointment->created_at . config('app.key'));
            
            if (!hash_equals($expectedToken, $token)) {
                return response()->json(['error' => 'Token d\'accès invalide.'], 403);
            }
            
            // Check if appointment is still valid (not older than 30 days)
            if ($appointment->created_at->diffInDays(now()) > 30) {
                return response()->json(['error' => 'Ce lien a expiré.'], 403);
            }
            
            // Validate form data
            $validatedData = $request->validate([
                'consultation_reason' => 'required|string|max:255',
                'symptoms' => 'required|string',
                'symptom_duration' => 'required|string|max:100',
                'pain_level' => 'nullable|integer|min:0|max:10',
                'medical_history' => 'nullable|string',
                'current_medications' => 'nullable|string',
                'allergies' => 'nullable|string',
                'emergency_questions' => 'nullable|array',
            ]);
            
            // Get or create pre-consultation form
            $preConsultationForm = $appointment->preConsultationForm;
            if (!$preConsultationForm) {
                $preConsultationForm = new PreConsultationForm(['appointment_id' => $appointment->id]);
            }
            
            // Update form data
            $preConsultationForm->fill($validatedData);
            $preConsultationForm->is_completed = true;
            $preConsultationForm->completed_at = now();
            $preConsultationForm->save();
            
            // Generate PDF
            $pdfPath = $this->generatePDF($preConsultationForm, $appointment);
            if ($pdfPath) {
                $preConsultationForm->pdf_path = $pdfPath;
                $preConsultationForm->save();
                
                // Save to medical records
                $this->saveMedicalRecord($appointment, $pdfPath, $preConsultationForm);
            }
            
            return response()->json([
                'success' => true,
                'message' => 'Formulaire de pré-consultation enregistré avec succès. Le PDF médical a été généré.',
                'pdf_path' => $pdfPath
            ]);
            
        } catch (\Exception $e) {
            Log::error('Error saving pre-consultation form with token: ' . $e->getMessage());
            
            return response()->json([
                'error' => 'Erreur lors de l\'enregistrement du formulaire. Veuillez réessayer.',
                'technical_details' => config('app.debug') ? $e->getMessage() : null,
                'timestamp' => now()->toISOString()
            ], 500);
        }
    }
}

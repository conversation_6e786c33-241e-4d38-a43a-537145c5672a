<?php

namespace App\Http\Controllers\CenterMedical;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Requests\CentreMedical\UpdateCentreMedicalRequest;
use App\Http\Requests\CentreMedical\DeleteCentreImageRequest;
use App\Services\CentreMedicalService\CentreMedicalService;

use App\Repositories\AddressRepository;
use App\Models\AddressImage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class CentreMedicalSettingsController extends Controller
{
    protected $centreMedicalService;
    protected $addressRepository;

    public function __construct(CentreMedicalService $centreMedicalService, AddressRepository $addressRepository)
    {
        $this->centreMedicalService = $centreMedicalService;
        $this->addressRepository = $addressRepository;
    }

    public function index()
    {
        $centreMedical = Auth::user()->CentreMedical;
        return view('pages.user-accounts.centreMedical.settings', compact('centreMedical'));
    }
    public function updateBasicInfo(UpdateCentreMedicalRequest $request)
    {
        try {
            $centreMedical = Auth::user()->CentreMedical;
            if ($request->hasFile('logo')) {
                if ($centreMedical->logo) {
                    Storage::delete('public/' . $centreMedical->logo);
                }
                $logoPath = $request->file('logo')->store('centre_medical_logos', 'public');
                $centreMedical->logo = $logoPath;
            }
            $this->centreMedicalService->update([
                'name' => $request->name,
                'clinic_type' => $request->clinic_type,
                'presentation' => $request->presentation,
                'logo' => $centreMedical->logo,
            ], $centreMedical->id);
            $this->addressRepository->update([
                'address' => $request->address,
                'postal_code' => $request->postal_code,
                'district' => $request->district,
                'city' => $request->city,
                'country' => $request->country,
            ], $centreMedical->address->id);
            return redirect()->back()->with('success', 'Centre Medical updated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to update basic info. Please try again.');
        }
    }
    public function store_clinic_image(Request $request)
    {
        $path = $request->file('file')->store('clinics_images');
        $this->centreMedicalService->addClinicImage([
            'image_url' => $path,
            'id' => $request->address_id
        ]);
        session()->flash('success', trans("alerts.success.image_added"));
        return response()->json([
            'message' => trans('alerts.success.image_added'),
            'image_url' => $path,
        ], 200);
    }
    public function delete_clinic_image(DeleteCentreImageRequest $request)
    {
        $image = AddressImage::find($request->id);
        if ($image) {
            if (Storage::exists('public/' . $image->image_url)) {
                Storage::delete('public/' . $image->image_url);
            }
            $image->delete();
            return response()->json([
                'success' => trans("alerts.success.image_deleted")
            ]);
        } else {
            return response()->json([
                'error' => trans("alerts.error.image_not_found")
            ], 404);
        }
    }
}

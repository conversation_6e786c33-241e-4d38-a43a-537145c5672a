<?php

namespace App\Http\Controllers\assistanteadministrative;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use App\Services\CentreMedicalService\CentreMedicalService;
use App\Services\DoctorToCentreMedical\DoctorToCentreMedical;

class AssistanteadministrativeDoctorsController extends Controller
{
    protected $centreMedicalService;
    protected $doctorToCentreMedical;

    public function __construct(
        CentreMedicalService $centreMedicalService,
        DoctorToCentreMedical $doctorToCentreMedical
    ) {
        $this->centreMedicalService = $centreMedicalService;
        $this->doctorToCentreMedical = $doctorToCentreMedical;
    }

    public function index(Request $request)
    {
        $user = Auth::user();
        $centreId = Auth::user()->assistante->centre_medical_doctor_id;
        $medicalCenterid = $this->doctorToCentreMedical->find($centreId);

        $medicalCenter = $this->centreMedicalService->get($medicalCenterid->first()->centre_medical_id);
        $assistantType = $user->assistante->type;
        if ($assistantType === 'accuille_clinique') {
            $doctors = $this->doctorToCentreMedical->getDoctorsByCentre($medicalCenterid->first()->centre_medical_id);
        } elseif ($assistantType === 'service_clinique') {
            $service = $user->assistante->service;
            $doctors = $this->doctorToCentreMedical->getDoctorsByCentreAndService($medicalCenterid->first()->centre_medical_id, $service);
        } else {
            $doctors = collect();
        }

        $translatedDoctors = $doctors->map(function ($doctor) {
            $doctor->translated_speciality = __('specialities.' . $doctor->name, [], 'fr');
            $doctor->url_district = strtolower(trim(preg_replace('/[^a-zA-Z0-9]+/', '-', $doctor->district)));
            $doctor->url_name = strtolower(trim(preg_replace('/[^a-zA-Z0-9]+/', '-', $doctor->first_name . '-' . $doctor->last_name)));
            return $doctor;
        });

        return view('pages.user-accounts.assistante-administrative.doctor-profile.index', compact('medicalCenter', 'doctors'));
    }
}

<?php

namespace App\Http\Controllers\Common;
use Carbon\Carbon;
use App\Http\Controllers\Controller;
use App\Services\PatientClinique\IPatientClinique;
use App\Services\AppointmentService\AppointmentService;
use App\Services\DocClinicTime\DocClinicTimeService;
use Illuminate\Http\Request;
use App\Models\Doctor;
use App\Models\DocClinicTime;
use Illuminate\Support\Facades\log;
use Illuminate\Support\Facades\Auth;
use App\Models\Appointment;
use App\Mail\AssistantConfirmedDoctorMail;
use App\Mail\AssistantConfirmedPatientMail;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Cache;
use App\Jobs\UpdateFreeAppointmentCache;

class AppointmentCliniqueController extends Controller
{
    protected $patientCliniqueService;
    protected $appointmentService;
    protected $docClinicTimeService;

    public function __construct(IPatientClinique $patientCliniqueService, AppointmentService $appointmentService, DocClinicTimeService $docClinicTimeService)
    {
        $this->patientCliniqueService = $patientCliniqueService;
        $this->appointmentService = $appointmentService;
        $this->docClinicTimeService = $docClinicTimeService;
    }

    public function index($doctorId)
    {
        return view('pages.user-accounts.assistante-administrative.appointmentsClinique', compact('doctorId'));
    }

    public function getDocClinicTimes(Request $request)
    {
        try {
            $doctorId = $request->query('doctor_id');
            $selectedClinic = $request->session()->get('selected_clinic_id');
            $times = $this->docClinicTimeService->getDocClinicTimesByDoctorId($doctorId, $selectedClinic);
            log::info($times);
            return response()->json($times);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function getApptByDoctorClinique(Request $request)
    {
        $doctorId = $request->query('doctor_id');
        $clinicId = $request->query('centre_id');
        $appointments = $this->appointmentService->getApptByDoctorClinique($doctorId, $clinicId);
        return response()->json($appointments);
    }

    /**
     * Confirm a single appointment
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function confirmAppointment(Request $request)
    {
        try {
            $appointmentId = $request->input('appointment_id');
            
            if (!$appointmentId) {
                return response()->json(['error' => 'Appointment ID is required'], 400);
            }
              $appointment = Appointment::findOrFail($appointmentId);
            $appointment->status = 'confirmed';
            $appointment->save();
            
            // Invalidate cache for this doctor
            $this->invalidateDoctorCache($appointment->doctor_id);
            
            // Send confirmation emails
            $this->sendConfirmationEmails($appointment);
            
            return response()->json(['success' => true, 'message' => 'Appointment confirmed successfully']);
        } catch (\Exception $e) {
            Log::error('Error confirming appointment: ' . $e->getMessage(), [
                'appointment_id' => $request->input('appointment_id')
            ]);
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Confirm multiple appointments at once
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function confirmMultipleAppointments(Request $request)
    {
        try {
            $appointmentIds = $request->input('appointment_ids');
            
            if (!$appointmentIds || !is_array($appointmentIds) || count($appointmentIds) === 0) {
                return response()->json(['error' => 'Appointment IDs are required'], 400);
            }
              $appointments = Appointment::whereIn('id', $appointmentIds)->get();
            
            foreach ($appointments as $appointment) {
                $appointment->status = 'confirmed';
                $appointment->save();
                
                // Invalidate cache for this doctor
                $this->invalidateDoctorCache($appointment->doctor_id);
                
                // Send confirmation emails for each appointment
                $this->sendConfirmationEmails($appointment);
            }
            
            return response()->json([
                'success' => true, 
                'message' => 'Appointments confirmed successfully',
                'count' => count($appointments)
            ]);
        } catch (\Exception $e) {
            Log::error('Error confirming multiple appointments: ' . $e->getMessage());
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Submit appointment through assistant interface
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function submitAppointmentAss(Request $request)
    {
        try {
            Log::info('========== CLINIC APPOINTMENT CREATION PROCESS STARTED ==========');
            Log::info($request->all());
            
            $data = $request->all();
            $patientData = [
                'first_name' => $data['first_name'],
                'last_name' => $data['last_name'],
                'phone' => $data['phone'],
                'clinique_id' => $data['centre_id'],
            ];
            
            // Check if patient exists or create new one
            $existingPatient = $this->patientCliniqueService->findByfullname($data['first_name'], $data['last_name']);
            if ($existingPatient) {
                $patientId = $existingPatient->id;
                Log::info('Using existing patient clinique', ['patient_id' => $patientId]);
            } else {
                $patient = $this->patientCliniqueService->create($patientData);
                $patientId = $patient->id;
                Log::info('Created new patient clinique', ['patient_id' => $patientId]);
            }
            
            // Create appointment
            $appointmentData = [
                'patient_id' => 0,
                'patient_clinique_id' => $patientId,
                'date' => $data['appointment_date'] . ' ' . $data['time_slot'],
                'clinique_id' => $data['centre_id'],
                'doctor_id' => $data['doctor_id'],
                'motif' => $data['motif'],
                'status' => 'confirmed',
            ];
              Log::info('Creating clinic appointment', $appointmentData);
            $appointment = $this->appointmentService->createForcliniqueAss($appointmentData);
            
            // Invalidate cache for this doctor
            $this->invalidateDoctorCache($appointment->doctor_id);
            
            // Send confirmation emails and SMS
            Log::info('Sending notifications for clinic appointment', [
                'appointment_id' => $appointment->id,
                'doctor_id' => $appointment->doctor_id,
                'clinic_id' => $appointment->clinique_id,
                'patient_clinique_id' => $appointment->patient_clinique_id
            ]);
            
            $this->sendConfirmationEmails($appointment);
            
            return response()->json($appointment, 201);
        } catch (\Exception $e) {
            Log::error('Error in clinic appointment creation: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Send confirmation notifications to all relevant parties
     * 
     * @param Appointment $appointment
     * @return void
     */
    protected function sendConfirmationEmails(Appointment $appointment)
    {
        try {
            // Load necessary relationships
            $appointment->load(['doctor.user', 'patient.user', 'patient_clinique', 'clinic']);
            
            // Get doctor information
            $doctor = $appointment->doctor;
            $doctorEmail = $doctor->user ? $doctor->user->email : null;
            $doctorName = $doctor->user ? $doctor->user->first_name . ' ' . $doctor->user->last_name : 'Doctor';
            $doctorPhone = $doctor->user ? $doctor->user->phone : null;
            
            // Get patient information
            $patientName = '';
            $patientEmail = null;
            $patientPhone = null;
            
            if ($appointment->patient && $appointment->patient->user) {
                // Regular patient with user account
                $patientUser = $appointment->patient->user;
                $patientName = $patientUser->first_name . ' ' . $patientUser->last_name;
                $patientEmail = $patientUser->email;
                $patientPhone = $patientUser->phone;
            } elseif ($appointment->patient_clinique) {
                // Patient from clinic
                $patientName = $appointment->patient_clinique->first_name . ' ' . $appointment->patient_clinique->last_name;
                $patientEmail = $appointment->patient_clinique->email;
                $patientPhone = $appointment->patient_clinique->phone;
            }
            
            // Get clinic information
            $clinicName = $appointment->clinic ? $appointment->clinic->name : 'DocExpress Clinic';
            $clinicPhone = $appointment->clinic ? $appointment->clinic->phone : null;
            
            // Format date for better readability
            $formattedDate = Carbon::parse($appointment->date)->format('l, F j, Y \a\t g:i A');
            $smsFormattedDate = Carbon::parse($appointment->date)->format('d/m/Y à H:i');
            
            // Get motif and type
            $motif = $appointment->motif ?? 'consultation';
            $type = $appointment->type ?? 'in_person';
            
            // Send email to doctor if we have their email
            if ($doctorEmail) {
                Mail::to($doctorEmail)->send(new AssistantConfirmedDoctorMail(
                    $doctorName,
                    $patientName,
                    $patientPhone ?? 'Not provided',
                    $patientEmail ?? 'Not provided',
                    $formattedDate,
                    $motif,
                    $type
                ));
            }
            
            // Send email to patient if we have their email
            if ($patientEmail) {
                Mail::to($patientEmail)->send(new AssistantConfirmedPatientMail(
                    $patientName,
                    $doctorName,
                    $formattedDate,
                    $motif,
                    $type
                ));
            }
            
            // Prepare SMS notifications
            $otpService = app(\App\Services\OTP\OTPService::class);
              // Prepare SMS messages - Messages personnalisés pour les rendez-vous via clinique
            $doctorMessage = "DocExpress: Rendez-vous confirmé avec le patient {$patientName} le {$smsFormattedDate} à {$clinicName}. Motif: {$motif}. Plus d'informations sur votre compte.";
            $patientMessage = "DocExpress: Votre rendez-vous médical avec Dr. {$doctorName} est programmé pour le {$smsFormattedDate} à {$clinicName}. N'oubliez pas d'apporter vos documents médicaux importants.";
            
            // Send SMS notifications
            $smsData = [
                'appointment_id' => $appointment->id,
            ];
            
            // Add doctor SMS if we have phone
            if ($doctorPhone) {
                $smsData['doctor_phone'] = $doctorPhone;
                $smsData['doctor_message'] = $doctorMessage;
            }
            
            // Add patient SMS if we have phone
            if ($patientPhone) {
                $smsData['patient_phone'] = $patientPhone;
                $smsData['patient_message'] = $patientMessage;
            }
            
            // Add clinic SMS if we have phone
            if ($clinicPhone) {
                $clinicMessage = "DocExpress: Nouveau RDV confirmé pour le {$smsFormattedDate} - Patient: {$patientName}, Médecin: Dr. {$doctorName}. Motif: {$motif}. Type: " . ($type === 'remotely' ? 'À distance' : 'En cabinet') . ".";
                $smsData['clinic_phone'] = $clinicPhone;
                $smsData['clinic_message'] = $clinicMessage;
            }
            
            // Send SMS to assistants for this clinic
            if ($appointment->clinic_id) {
                $assistants = \App\Models\Assistante_ad::with('user')
                    ->where('clinique_id', $appointment->clinic_id)
                    ->get();
                
                foreach ($assistants as $index => $assistant) {
                    if ($assistant->user && $assistant->user->phone) {
                        $assistantName = $assistant->user->first_name;
                        $assistantPhone = $assistant->user->phone;
                        $assistantMessage = "DocExpress: {$assistantName}, nouveau rendez-vous confirmé pour le {$smsFormattedDate} - Patient: {$patientName}, Médecin: Dr. {$doctorName}. Motif: {$motif}.";
                        
                        $smsData["assistant{$index}_phone"] = $assistantPhone;
                        $smsData["assistant{$index}_message"] = $assistantMessage;
                        
                        // Send email to assistant
                        Mail::to($assistant->user->email)->send(new AssistantConfirmedDoctorMail(
                            $doctorName,
                            $patientName,
                            $patientPhone ?? 'Not provided',
                            $patientEmail ?? 'Not provided',
                            $formattedDate,
                            $motif,
                            $type
                        ));
                    }
                }
            }
            
            // Send all SMS notifications
            if (count($smsData) > 1) { // If we have more than just appointment_id
                $otpService->sendClinicAppointmentNotifications($smsData);
            }
            
        } catch (\Exception $e) {
            Log::error('Error sending confirmation notifications', [
                'error' => $e->getMessage(),
                'appointment_id' => $appointment->id
            ]);
        }
    }

    public function submitAppointment(Request $request)
    {
        if (!Auth::check()) {
            return redirect('/login?redirect=' . urlencode(url()->current()));
        }
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'phone' => 'required|string|max:15',
            'appointment_date' => 'required|date',
            'time_slot' => 'required|string',
            'centre_id' => 'required|integer',
            'doctor_id' => 'required|integer',
            'motif' => 'required|in:consultation,suivi,controle',
        ]);
        $data = $request->all();
        $patientData = [
            'first_name' => $data['first_name'],
            'last_name' => $data['last_name'],
            'phone' => $data['phone'],
        ];
        $patient = $this->patientCliniqueService->create($patientData);
        $appointmentData = [
            'patient_id' => $patient->id,
            'date' => $data['appointment_date'] . ' ' . $data['time_slot'],
            'clinique_id' => $data['centre_id'],
            'doctor_id' => $data['doctor_id'],
            'motif_consultation' => $data['motif'],
            'status' => 'confirmed',        ];
        $appointment = $this->appointmentService->createForclinique($appointmentData);
        if ($appointment) {
            // Invalidate cache for this doctor
            $this->invalidateDoctorCache($appointment->doctor_id);
            
            return response()->json($appointment, 201);
        } else {
            return response()->json(['error' => 'Failed to save appointment'], 500);
        }
    }

    public function getDoctorAvailability($doctorId, $clinicId)
    {
        try {
            $doctorCenter = Doctor::find($doctorId)
                ->medicalCenters()
                ->where('centre_medical_id', $clinicId)
                ->first();

            if (!$doctorCenter) {
                return response()->json([], 200);
            }
            $slots = DocClinicTime::where('centre_medical_doctor_id', $doctorCenter->pivot->id)
                ->whereNotNull('start_time')
                ->whereNotNull('end_time')
                ->get();
            if ($slots->isEmpty()) {
                Log::info('No slots found');
                return response()->json([], 200);
            }
            $availability = [];
            $startDate = now();
            $endDate = now()->addDays(7);
            $bookedAppointments = $this->appointmentService
                ->getApptByDoctorClinique($doctorId, $clinicId)
                ->where('status', '!=', 'cancelled');

            for ($date = clone $startDate; $date <= $endDate; $date->addDay()) {
                $dayAbbrev = ucfirst(substr(strtolower($date->locale('fr')->dayName), 0, 3));
                $daySchedules = $slots->filter(function($slot) use ($dayAbbrev) {
                    return $slot->day === $dayAbbrev && !is_null($slot->start_time) && !is_null($slot->end_time);
                });
                if ($daySchedules->isEmpty()) {
                    continue;
                }
                $timeSlots = [];
                foreach ($daySchedules as $schedule) {
                    $startTime = Carbon::parse($schedule->start_time);
                    $endTime = Carbon::parse($schedule->end_time);
                    if (!$startTime || !$endTime || $startTime >= $endTime) {
                        continue;
                    }

                    while ($startTime < $endTime) {
                        $currentSlot = $startTime->format('H:i');
                        $isBooked = $bookedAppointments->contains(function($appointment) use ($date, $currentSlot) {
                            return Carbon::parse($appointment->date)->format('Y-m-d') === $date->format('Y-m-d')
                                && Carbon::parse($appointment->date)->format('H:i') === $currentSlot;
                        });
                        if (!$isBooked) {
                            $timeSlots[] = $currentSlot;
                        }
                        $startTime->addMinutes(30);
                    }
                }
                if (!empty($timeSlots)) {
                    sort($timeSlots);
                    $availability[$date->format('Y-m-d')] = array_values(array_unique($timeSlots));
                }
            }
            return response()->json($availability);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function getInfoAppt($id_appt)
    {
        $appointment = $this->appointmentService->getAppointmentInfo($id_appt);
        if ($appointment) {
            return response()->json([
                'id' => $appointment->id,
                'date' => $appointment->date,
                'time' => $appointment->time,
                'status' => $appointment->status,
                'patient' => [
                    'first_name' => $appointment->patient_first_name,
                    'last_name' => $appointment->patient_last_name,
                    'email' => $appointment->email ?? null,
                    'phone' => $appointment->phone ?? null,
                ],
            ]);
        }
        return response()->json(['message' => 'Appointment not found'], 404);
    }
    
    /**
     * Invalidate cache for a doctor and trigger immediate cache update
     */
    private function invalidateDoctorCache($doctorId)
    {
        $cacheKey = "free_appointment_doctor_{$doctorId}";
        Cache::forget($cacheKey);
        
        // Dispatch immediate cache update for this doctor
        UpdateFreeAppointmentCache::dispatch()->onQueue('high');
        
        Log::info("Cache invalidated and update dispatched for doctor {$doctorId}");
    }
}
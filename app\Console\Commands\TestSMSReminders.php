<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\SendAppointmentReminderSMS;
use App\Models\Appointment;
use App\Services\SMSService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class TestSMSReminders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sms:test-reminder 
                            {appointment_id? : ID of an existing appointment to test with} 
                            {type=24h : Type of reminder (24h or 48h)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the SMS reminder functionality';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $appointmentId = $this->argument('appointment_id');
        $reminderType = $this->argument('type');
        
        $this->info('Testing SMS reminder system in ' . 
                   (config('services.sms.mock_mode') ? 'MOCK MODE' : 'LIVE MODE'));
        
        // If no appointment ID provided, find a suitable one or create a test
        if (!$appointmentId) {
            $this->info('No appointment ID provided. Looking for a suitable appointment...');
            $appointment = $this->findSuitableAppointment();
            
            if (!$appointment) {
                if ($this->confirm('No suitable appointment found. Create a test appointment?', true)) {
                    $appointment = $this->createTestAppointment();
                    $this->info('Created test appointment with ID: ' . $appointment->id);
                } else {
                    $this->error('Test cancelled.');
                    return 1;
                }
            } else {
                $this->info('Found suitable appointment with ID: ' . $appointment->id);
            }
        } else {
            $appointment = Appointment::with(['doctor.user', 'patient.user', 'clinic', 'patient_clinique'])
                ->find($appointmentId);
                
            if (!$appointment) {
                $this->error("Appointment with ID {$appointmentId} not found!");
                return 1;
            }
        }
        
        // Display appointment details
        $this->displayAppointmentDetails($appointment);
        
        // Confirm sending the test reminder
        if ($this->confirm('Send SMS reminder for this appointment?', true)) {
            try {
                // Process the job directly
                $job = new SendAppointmentReminderSMS($appointment, $reminderType);
                $job->handle();
                
                $this->info('SMS reminder job processed successfully!');
                $this->info('Check your logs at storage/logs/laravel.log for details.');
                
                return 0;
            } catch (\Exception $e) {
                $this->error('Error processing SMS reminder: ' . $e->getMessage());
                $this->error('Check logs for more details.');
                Log::error("Error in test reminder command: {$e->getMessage()}", [
                    'exception' => $e
                ]);
                return 1;
            }
        }
        
        $this->info('Test cancelled.');
        return 0;
    }
    
    /**
     * Find a suitable appointment for testing
     */
    private function findSuitableAppointment()
    {
        // Try to find a confirmed appointment with required relations
        return Appointment::where('status', 'confirmed')
            ->whereHas('doctor.user')
            ->whereHas('patient.user')
            ->with(['doctor.user', 'patient.user', 'clinic', 'patient_clinique'])
            ->orderBy('date', 'desc')
            ->first();
    }
    
    /**
     * Create a test appointment
     */
    private function createTestAppointment()
    {
        // Find the first doctor and patient
        $doctor = \App\Models\Doctor::first();
        $patient = \App\Models\Patient::first();
        
        if (!$doctor || !$patient) {
            $this->error('Cannot create test: No doctors or patients found in database.');
            exit(1);
        }
        
        // Create appointment for tomorrow
        return Appointment::create([
            'doctor_id' => $doctor->id,
            'patient_id' => $patient->id,
            'date' => Carbon::tomorrow()->setHour(14)->setMinute(30),
            'status' => 'confirmed',
            'type' => 'in_person',
            'motif' => 'Test SMS reminder'
        ]);
    }
    
    /**
     * Display appointment details
     */
    private function displayAppointmentDetails($appointment)
    {
        $doctor = $appointment->doctor;
        $doctorName = $doctor->user ? ($doctor->user->first_name . ' ' . $doctor->user->last_name) : 'Unknown';
        $doctorPhone = $doctor->user ? $doctor->user->phone : 'Unknown';
        
        $patientName = 'Unknown';
        $patientPhone = 'Unknown';
        
        if ($appointment->patient && $appointment->patient->user) {
            $patientName = $appointment->patient->user->first_name . ' ' . $appointment->patient->user->last_name;
            $patientPhone = $appointment->patient->user->phone;
        } elseif ($appointment->patient_clinique) {
            $patientName = $appointment->patient_clinique->first_name . ' ' . $appointment->patient_clinique->last_name;
            $patientPhone = $appointment->patient_clinique->phone;
        }
        
        $this->info('');
        $this->info('📅 APPOINTMENT DETAILS:');
        $this->info('------------------------');
        $this->info('ID: ' . $appointment->id);
        $this->info('Date: ' . Carbon::parse($appointment->date)->format('Y-m-d H:i'));
        $this->info('Status: ' . $appointment->status);
        $this->info('Type: ' . $appointment->type);
        $this->info('');
        $this->info('👨‍⚕️ Doctor: ' . $doctorName);
        $this->info('   Phone: ' . $doctorPhone);
        $this->info('');
        $this->info('🧑 Patient: ' . $patientName);
        $this->info('   Phone: ' . $patientPhone);
        $this->info('');
        if ($appointment->clinic) {
            $this->info('🏥 Clinic: ' . $appointment->clinic->name);
        }
        $this->info('');
    }
}

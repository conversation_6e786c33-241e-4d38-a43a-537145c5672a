<?php

namespace App\Http\Controllers\assistanteadministrative;

use App\Http\Controllers\Controller;
use App\Http\Requests\CareSheet\CreateCareSheetRequest;
use App\Services\CareSheetService\ICareSheetService;

class CareSheetController extends Controller
{
    protected $careSheetService;

    public function __construct(ICareSheetService $careSheetService)
    {
        $this->careSheetService = $careSheetService;
    }

    public function download_care_sheet(CreateCareSheetRequest $request)
    {
        session()->flash('success', trans('alerts.success.care_sheet_downloaded_successfully'));

        return $this->careSheetService->download($request->validated());
    }
}

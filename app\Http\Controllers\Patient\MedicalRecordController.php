<?php
namespace App\Http\Controllers\Patient;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Dependent;
use App\Models\MedicalRecord;
use App\Models\Patient;
use Illuminate\Support\Facades\Auth;
use App\Models\Appointment;
use App\Models\Doctor;
class MedicalRecordController extends Controller
{
    public function index()
    {
        $patient = Patient::where('user_id', Auth::id())->first();
        $dependent = Dependent::where('user_id', Auth::id())->get();
        $medecalRecord = MedicalRecord::where('patient_id', $patient->id)
            ->orderBy('created_at', 'desc')
            ->get();
        // Build doctors list from appointments
        $appointmentDoctorIds = Appointment::where('patient_id', $patient->id)->pluck('doctor_id')->unique();
        $appointmentDoctors = Doctor::with(['user','speciality'])->whereIn('id', $appointmentDoctorIds)->get();
            // return abort(404);
        return view('pages.user-accounts.patient.medical-record', [
            'patient' => $patient,
            'dependent' => $dependent,
            'medecalRecord' => $medecalRecord,
            'appointmentDoctors' => $appointmentDoctors
        ]);
    }
    public function store(Request $request)
    {
        $patient = Patient::with('user')->where('user_id', Auth::id())->first();
        $request->validate([
            'name' => ['required', 'string', 'max:20'],
            'type' => ['required', 'in:forMe,dependent'],
            'title' => ['required', 'string'],
            'document_type' => ['required','nullable', 'in:ordonnance,examen,imagerie'],
            'file' => ['required', 'mimes:jpeg,png,jpg,pdf,docx,doc', 'max:2048'],
        ]);
        $path = $request->file('file')->store("medical_records");
        $name = '';
        if ($request->type == 'forMe') {
            $name = $patient->user->first_name;
        } else {
            $name = $request->dependent;
        }
        MedicalRecord::create([
            'name' => $name,
            'type' => $request->type,
            'title' => $request->title,
            'document_type' => $request->document_type,
            'file' => $path,
            'symptoms' => $request->symptoms,
            'patient_id' => $patient->id
        ]);
        return redirect()->back()->with([
            'success' => trans("alerts.success.added_successfully")
        ]);
    }
    public function download($id)
    {
        $med = MedicalRecord::find($id);
        $path = storage_path('app/' . $med->file);
        return response()->download($path);
    }
    public function destroy($id)
    {
        $med = MedicalRecord::findorfail($id);
        $med->delete();
        return redirect()->back()->with([
            'success' => trans("alerts.success.deleted_successfully")
        ]);
    }
}
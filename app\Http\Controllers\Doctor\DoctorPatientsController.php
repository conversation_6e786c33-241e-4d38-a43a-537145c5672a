<?php

namespace App\Http\Controllers\Doctor;

use App\Http\Controllers\Controller;
use App\Services\DoctorService\IDoctorService;
use Illuminate\Support\Facades\Auth;

class DoctorPatientsController extends Controller
{
    protected $doctorService;
    public function __construct(IDoctorService $doctorService)
    {
        $this->doctorService = $doctorService;
    }
    public function index()
    {
        $my_patients = $this->doctorService->get(Auth::user()->doctor->id)->patients;
        return view('pages.user-accounts.doctor.patients', ['patients' => $my_patients]);
    }
}

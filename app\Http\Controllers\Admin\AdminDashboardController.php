<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\AppointmentService\IAppointmentService;
use App\Services\AssistanteService\IAssistanteService;
use App\Services\DoctorService\IDoctorService;
use App\Services\PatientService\IPatientService;
use App\Services\PharmacyService\IPharmacyService;

class AdminDashboardController extends Controller
{

    protected $doctorService;
    protected $patientService;
    protected $appointmentService;
    protected $pharmacyService;
    protected $assistanteService;
    public function __construct(IDoctorService $doctorService, IPatientService $patientService, IAppointmentService $appointmentService,IPharmacyService $pharmacyService, IAssistanteService $assistanteService)
    {
        $this->doctorService = $doctorService;
        $this->patientService = $patientService;
        $this->appointmentService = $appointmentService;
        $this->pharmacyService = $pharmacyService;
        $this->assistanteService = $assistanteService;
    }

    public function index()
    {
        $totalPatients = $this->patientService->count();
        $totalDoctors = $this->doctorService->count();
        $totalPharmacies = $this->pharmacyService->count();
        $totalassistantes = $this->assistanteService->count();
        $appointmentsCount = $this->appointmentService->getCount();
        $doctors = $this->doctorService->all();
        $patients = $this->patientService->all();
       
        return view(
            'pages.admin.dashboard.index',
            compact(
                'totalPatients',
                'totalDoctors',
                'totalPharmacies',
                'totalassistantes',
                'doctors',
                'patients',
            ),
            $appointmentsCount
        );
    }
}

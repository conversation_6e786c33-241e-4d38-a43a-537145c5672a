<?php

namespace App\Http\Controllers\CenterMedical;

use App\Http\Controllers\Controller;
use App\Services\CentreMedicalService\CentreMedicalService;
use App\Repositories\AddressRepository;
use App\Services\DoctorToCentreMedical\DoctorToCentreMedical;
use Illuminate\Support\Facades\Auth;
use App\Models\Appointment;
class CenterMedicalDashboardController extends Controller
{
    protected $centreMedicalService;
    protected $doctorToCentreMedical;
    protected $addressRepository;
    public function __construct(
        CentreMedicalService $centreMedicalService,
        DoctorToCentreMedical $doctorToCentreMedical,
        AddressRepository $addressRepository
    ) {
        $this->centreMedicalService = $centreMedicalService;
        $this->doctorToCentreMedical = $doctorToCentreMedical;
        $this->addressRepository = $addressRepository;
    }

    public function index()
    {
        $centreId = Auth::user()->centreMedical->id;
        $doctorCount = $this->doctorToCentreMedical->countDoctorsInCentre($centreId);
        
        // Fetch appointments with proper relationships for both patient types
        $appointments = Appointment::where('clinique_id', $centreId)
            ->where('status', '!=', 'cancelled')
            ->with([
                'patient.user', 
                'patient_clinique', 
                'doctor.user', 
                'doctor.speciality'
            ])
            ->get();
        
        return view('pages.user-accounts.centreMedical.dashboard', compact('doctorCount', 'appointments'));
    }
    Public function GetAssistant()
    {
        $Assistants =$this->centreMedicalService->GetAssistant(Auth::user()->centreMedical->id);
        return  view('pages.user-accounts.centreMedical.assistant',compact('Assistants'));
    }
}

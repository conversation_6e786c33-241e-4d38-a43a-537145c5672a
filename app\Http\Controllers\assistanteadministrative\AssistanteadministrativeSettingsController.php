<?php

namespace App\Http\Controllers\assistanteadministrative;

use App\Http\Controllers\Controller;
use App\Http\Requests\Doctor\AddEducationRequest;
use App\Http\Requests\Doctor\AddExperienceRequest;
use App\Http\Requests\Doctor\DeleteEducationRequest;
use App\Http\Requests\Doctor\DeleteExperienceRequest;
use App\Http\Requests\Doctor\UpdatePersonalInfoRequest;
use App\Http\Requests\Doctor\UpdateServicesRequest;
use App\Services\AssistanteService\IAssistanteService;
use Illuminate\Support\Facades\Auth;

class AssistanteadministrativeSettingsController extends Controller
{
    protected $assistanteservice;

    public function __construct(IAssistanteService $assistanteservice)
    {
        $this->assistanteservice = $assistanteservice;
    }

    public function index()
    {
        $doctor = $this->assistanteservice->get(Auth::user()->assistante->id);

        return view('pages.user-accounts.assistante-administrative.settings.settings', compact('doctor'));
    }

    public function updatePersonalInfo(UpdatePersonalInfoRequest $request)
    {
        $data = $request->validated();
        if ($request->hasFile('image')) {
            $image = $request->file('image')->store('doctors_images');
            $data['image_url'] = $image;
        }

        $this->assistanteservice->updatePersonalInfo($data, Auth::id());

        return redirect()->back()->with([
            'success' => trans("alerts.success.your_information_have_been_saved_successfully")
        ]);
    }

    public function updateServices(UpdateServicesRequest $req)
    {
        $this->assistanteservice->updateServices($req->validated()['services'] ?? '');

        return redirect()->back()->with([
            "success" => trans("alerts.success.your_services_have_been_save_successfully")
        ])->withFragment('services-section');
    }

    public function store_education(AddEducationRequest $req)
    {
        $this->assistanteservice->addEducation($req->validated());

        return redirect()->back()->with([
            'success' => trans("alerts.success.your_education_have_been_saved_successfully")
        ])->withFragment('education-section');
    }

    public function store_experience(AddExperienceRequest $req)
    {
        $this->assistanteservice->addExperience($req->validated());

        return redirect()->back()->with([
            'success' => trans("alerts.success.your_experience_have_been_saved_successfullyy")
        ])->withFragment('experience-section');
    }

    public function destory_education(DeleteEducationRequest $req)
    {
        $this->assistanteservice->deleteEducation($req->id);

        return response()->json([
            'success' => trans("alerts.success.education_deleted")
        ]);
    }

    public function destory_experience(DeleteExperienceRequest $req)
    {
        $this->assistanteservice->deleteExperience($req->id);

        return response()->json([
            'success' => trans("alerts.success.experience_deleted")
        ]);
    }
}

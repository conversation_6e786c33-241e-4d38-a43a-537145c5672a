<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Doctor;
use App\Models\EmailHistory;
use App\Mail\BulkDoctorMail;
use App\Mail\QuarterlyOfferMail;
use App\Mail\SemiAnnualOfferMail;
use App\Mail\AnnualOfferMail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class MailingController extends Controller
{
    /**
     * Display the mailing interface
     */
    public function index()
    {
        // Get count of active doctors
        $activeDoctorsCount = Doctor::whereHas('user', function($query) {
            $query->where('account_status', 1);
        })->count();

        // Get all active doctors with user info
        $doctors = Doctor::with('user')
            ->whereHas('user', function($query) {
                $query->where('account_status', 1);
            })
            ->get();

        return view('pages.admin.mailing.index', compact('activeDoctorsCount', 'doctors'));
    }    /**
     * Send bulk email to all doctors
     */
    public function sendBulkEmail(Request $request)
    {
        $request->validate([
            'message' => 'required|string',
        ]);
        $customSubject = $request->input('custom_subject');
        $doctorIds = $request->input('doctors');
        $query = Doctor::with('user')->whereHas('user', function($query) {
            $query->where('account_status', 1);
        });
        if (is_array($doctorIds) && count($doctorIds) > 0) {
            $query->whereIn('id', $doctorIds);
        }
        $doctors = $query->get();
        // If it's an AJAX request, handle it differently for progress tracking
        if ($request->ajax()) {
            return $this->sendBulkEmailAjax($request);
        }

        try {
            $successCount = 0;
            $failedCount = 0;
            $failedEmails = [];

            foreach ($doctors as $doctor) {
                try {
                    if ($doctor->user && $doctor->user->email) {
                        $doctor_name = trim($doctor->user->first_name . ' ' . $doctor->user->last_name);
                              Mail::to($doctor->user->email)->send(new BulkDoctorMail(
                            $doctor_name,
                            $request->message,
                            $customSubject
                        ));
                        
                        $successCount++;
                        
                        // Record email in history
                        $this->recordEmailHistory(
                            $doctor,
                            'bulk',
                            $customSubject ?: 'Email personnalisé',
                            $request->message,
                            'sent'
                        );
                        
                        Log::info('Bulk email sent successfully', [
                            'doctor_id' => $doctor->id,
                            'email' => $doctor->user->email,
                            'name' => $doctor_name
                        ]);
                    } else {
                        $failedCount++;
                        $failedEmails[] = 'Doctor ID: ' . $doctor->id . ' (No email)';
                        
                        Log::warning('Doctor has no email address', [
                            'doctor_id' => $doctor->id
                        ]);
                    }
                } catch (\Exception $e) {
                    $failedCount++;
                    $failedEmails[] = $doctor->user->email ?? 'Doctor ID: ' . $doctor->id;
                    
                    // Record failed email in history for bulk emails
                    if (isset($doctor) && $failedCount > 0) {
                        $this->recordEmailHistory(
                            $doctor,
                            'bulk',
                            $customSubject ?: 'Email personnalisé',
                            $request->message,
                            'failed',
                            $e->getMessage()
                        );
                    }
                    
                    Log::error('Failed to send bulk email to doctor', [
                        'doctor_id' => $doctor->id,
                        'email' => $doctor->user->email ?? 'unknown',
                        'error' => $e->getMessage()
                    ]);
                }
            }

            $message = "Email envoyé avec succès à {$successCount} médecins.";
            if ($failedCount > 0) {
                $message .= " {$failedCount} emails ont échoué.";
            }

            return redirect()->back()->with([
                'success' => $message,
                'email_stats' => [
                    'success' => $successCount,
                    'failed' => $failedCount,
                    'failed_emails' => $failedEmails
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Error in bulk email sending', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()->with([
                'error' => 'Une erreur est survenue lors de l\'envoi des emails: ' . $e->getMessage()
            ]);
        }
    }    /**
     * Send bulk email with AJAX progress tracking
     */
    private function sendBulkEmailAjax(Request $request)
    {
        // Récupérer la sélection des médecins (si fournie)
        $doctorIds = $request->input('doctors');
        $customSubject = $request->input('custom_subject');
        
        $query = Doctor::with('user')->whereHas('user', function($query) {
            $query->where('account_status', 1);
        });
        if (is_array($doctorIds) && count($doctorIds) > 0) {
            $query->whereIn('id', $doctorIds);
        }
        $doctors = $query->get();

        try {
            $totalDoctors = $doctors->count();
            $successCount = 0;
            $failedCount = 0;
            $failedEmails = [];

            foreach ($doctors as $index => $doctor) {
                try {
                    if ($doctor->user && $doctor->user->email) {
                        $doctor_name = trim($doctor->user->first_name . ' ' . $doctor->user->last_name);
                        
                        Mail::to($doctor->user->email)->send(new BulkDoctorMail(
                            $doctor_name,
                            $request->message,
                            $customSubject
                        ));
                        
                        $successCount++;
                        
                        // Record email in history for AJAX bulk emails
                        $this->recordEmailHistory(
                            $doctor,
                            'bulk',
                            $customSubject ?: 'Email personnalisé',
                            $request->message,
                            'sent'
                        );
                        
                        Log::info('Bulk email sent successfully', [
                            'doctor_id' => $doctor->id,
                            'email' => $doctor->user->email,
                            'name' => $doctor_name
                        ]);
                    } else {
                        $failedCount++;
                        $failedEmails[] = 'Doctor ID: ' . $doctor->id . ' (No email)';
                        
                        Log::warning('Doctor has no email address', [
                            'doctor_id' => $doctor->id
                        ]);
                    }
                } catch (\Exception $e) {
                    $failedCount++;
                    $failedEmails[] = $doctor->user->email ?? 'Doctor ID: ' . $doctor->id;
                    
                    // Record failed email in history for AJAX bulk emails
                    $this->recordEmailHistory(
                        $doctor,
                        'bulk',
                        $customSubject ?: 'Email personnalisé',
                        $request->message,
                        'failed',
                        $e->getMessage()
                    );
                    
                    Log::error('Failed to send bulk email to doctor', [
                        'doctor_id' => $doctor->id,
                        'email' => $doctor->user->email ?? 'unknown',
                        'error' => $e->getMessage()
                    ]);
                }

                // Simulate progress by sending partial responses
                if (($index + 1) % 5 == 0 || $index == $totalDoctors - 1) {
                    $processed = $index + 1;
                    $percentage = round(($processed / $totalDoctors) * 100);
                    
                    // For AJAX, we'll return the final result
                    if ($index == $totalDoctors - 1) {
                        return response()->json([
                            'success' => true,
                            'completed' => true,
                            'processed' => $processed,
                            'total' => $totalDoctors,
                            'percentage' => 100,
                            'success_count' => $successCount,
                            'failed_count' => $failedCount,
                            'message' => "Email envoyé avec succès à {$successCount} médecins." . ($failedCount > 0 ? " {$failedCount} emails ont échoué." : '')
                        ]);
                    }
                }
            }

        } catch (\Exception $e) {
            Log::error('Error in AJAX bulk email sending', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => true,
                'message' => 'Une erreur est survenue lors de l\'envoi des emails: ' . $e->getMessage()
            ], 500);
        }
    }    /**
     * Send predefined launch offer email to all doctors
     */
    public function sendLaunchOfferEmail(Request $request)
    {
        $defaultMessage = "Merci pour votre confiance, vous faites désormais partie des médecins actifs sur Docexpress. Nous lançons maintenant les abonnements officiels pour les médecins sélectionnés, avec une offre spéciale de lancement.";

        // Create new request with message and preserve doctor selection
        $requestData = [
            'message' => $defaultMessage,
            'custom_subject' => 'Offre spéciale de lancement - DocExpress'
        ];
        
        // Preserve doctor selection if provided
        if ($request->has('doctors')) {
            $requestData['doctors'] = $request->input('doctors');
        }
        
        $newRequest = new Request($requestData);
        
        // If it's an AJAX request, handle it with progress tracking
        if ($request->ajax()) {
            return $this->sendBulkEmailAjax($newRequest);
        }
        
        return $this->sendBulkEmail($newRequest);
    }/**
     * Send premium quarterly offer email (3 months)
     */
    public function sendQuarterlyOfferEmail(Request $request)
    {
        // If it's an AJAX request, handle it with progress tracking
        if ($request->ajax()) {
            return $this->sendPremiumOfferEmailAjax($request, 'quarterly');
        }
        
        return $this->sendPremiumOfferEmail('quarterly');
    }

    /**
     * Send premium semi-annual offer email (6 months)
     */
    public function sendSemiAnnualOfferEmail(Request $request)
    {
        // If it's an AJAX request, handle it with progress tracking
        if ($request->ajax()) {
            return $this->sendPremiumOfferEmailAjax($request, 'semi-annual');
        }
        
        return $this->sendPremiumOfferEmail('semi-annual');
    }

    /**
     * Send premium annual offer email (12 months)
     */
    public function sendAnnualOfferEmail(Request $request)
    {
        // If it's an AJAX request, handle it with progress tracking
        if ($request->ajax()) {
            return $this->sendPremiumOfferEmailAjax($request, 'annual');
        }
        
        return $this->sendPremiumOfferEmail('annual');
    }    /**
     * Send premium offer email (generic method)
     */
    private function sendPremiumOfferEmail($offerType)
    {
        $doctorIds = request('doctors');
        $query = Doctor::with('user')->whereHas('user', function($query) {
            $query->where('account_status', 1);
        });
        if (is_array($doctorIds) && count($doctorIds) > 0) {
            $query->whereIn('id', $doctorIds);
        }
        $doctors = $query->get();
        try {
            $successCount = 0;
            $failedCount = 0;
            $failedEmails = [];
            foreach ($doctors as $doctor) {
                try {
                    if ($doctor->user && $doctor->user->email) {
                        $doctor_name = trim($doctor->user->first_name . ' ' . $doctor->user->last_name);
                        $mailClass = $this->getPremiumOfferMailClass($offerType);
                        Mail::to($doctor->user->email)->send(new $mailClass($doctor_name));
                        $successCount++;
                        
                        // Record premium offer email in history
                        $this->recordPremiumOfferEmailHistory($doctor, $offerType, 'sent');
                        
                        Log::info('Premium offer email sent successfully', [
                            'doctor_id' => $doctor->id,
                            'email' => $doctor->user->email,
                            'name' => $doctor_name,
                            'offer_type' => $offerType
                        ]);
                    } else {
                        $failedCount++;
                        $failedEmails[] = 'Doctor ID: ' . $doctor->id . ' (No email)';
                        Log::warning('Doctor has no email address', [
                            'doctor_id' => $doctor->id
                        ]);
                    }
                } catch (\Exception $e) {
                    $failedCount++;
                    $failedEmails[] = $doctor->user->email ?? 'Doctor ID: ' . $doctor->id;
                    
                    // Record failed premium offer email in history
                    $this->recordPremiumOfferEmailHistory($doctor, $offerType, 'failed', $e->getMessage());
                    
                    Log::error('Failed to send premium offer email to doctor', [
                        'doctor_id' => $doctor->id,
                        'email' => $doctor->user->email ?? 'unknown',
                        'error' => $e->getMessage(),
                        'offer_type' => $offerType
                    ]);
                }
            }
            $message = "Email d'offre {$offerType} envoyé avec succès à {$successCount} médecins.";
            if ($failedCount > 0) {
                $message .= " {$failedCount} emails ont échoué.";
            }
            return redirect()->back()->with([
                'success' => $message,
                'email_stats' => [
                    'success' => $successCount,
                    'failed' => $failedCount,
                    'failed_emails' => $failedEmails
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error in premium offer email sending', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'offer_type' => $offerType
            ]);
            return redirect()->back()->with([
                'error' => 'Une erreur est survenue lors de l\'envoi des emails: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Send premium offer email with AJAX progress tracking
     */
    private function sendPremiumOfferEmailAjax(Request $request, $offerType)
    {
        $doctorIds = $request->input('doctors');
        $query = Doctor::with('user')->whereHas('user', function($query) {
            $query->where('account_status', 1);
        });
        if (is_array($doctorIds) && count($doctorIds) > 0) {
            $query->whereIn('id', $doctorIds);
        }
        $doctors = $query->get();
        try {
            $totalDoctors = $doctors->count();
            $successCount = 0;
            $failedCount = 0;
            $failedEmails = [];
            foreach ($doctors as $index => $doctor) {
                try {
                    if ($doctor->user && $doctor->user->email) {
                        $doctor_name = trim($doctor->user->first_name . ' ' . $doctor->user->last_name);                        $mailClass = $this->getPremiumOfferMailClass($offerType);
                        Mail::to($doctor->user->email)->send(new $mailClass($doctor_name));
                        $successCount++;
                        
                        // Record premium offer email in history for AJAX
                        $this->recordPremiumOfferEmailHistory($doctor, $offerType, 'sent');
                        
                        Log::info('Premium offer email sent successfully', [
                            'doctor_id' => $doctor->id,
                            'email' => $doctor->user->email,
                            'name' => $doctor_name,
                            'offer_type' => $offerType
                        ]);
                    } else {
                        $failedCount++;
                        $failedEmails[] = 'Doctor ID: ' . $doctor->id . ' (No email)';
                        Log::warning('Doctor has no email address', [
                            'doctor_id' => $doctor->id
                        ]);
                    }                } catch (\Exception $e) {
                    $failedCount++;
                    $failedEmails[] = $doctor->user->email ?? 'Doctor ID: ' . $doctor->id;
                    
                    // Record failed premium offer email in history for AJAX
                    $this->recordPremiumOfferEmailHistory($doctor, $offerType, 'failed', $e->getMessage());
                    
                    Log::error('Failed to send premium offer email to doctor', [
                        'doctor_id' => $doctor->id,
                        'email' => $doctor->user->email ?? 'unknown',
                        'error' => $e->getMessage(),
                        'offer_type' => $offerType
                    ]);
                }
                if ($index == $totalDoctors - 1) {
                    return response()->json([
                        'success' => true,
                        'completed' => true,
                        'processed' => $totalDoctors,
                        'total' => $totalDoctors,
                        'percentage' => 100,
                        'success_count' => $successCount,
                        'failed_count' => $failedCount,
                        'message' => "Email d'offre {$offerType} envoyé avec succès à {$successCount} médecins." . ($failedCount > 0 ? " {$failedCount} emails ont échoué." : '')
                    ]);
                }
            }
        } catch (\Exception $e) {
            Log::error('Error in AJAX premium offer email sending', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'offer_type' => $offerType
            ]);
            return response()->json([
                'success' => false,
                'error' => true,
                'message' => 'Une erreur est survenue lors de l\'envoi des emails: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get the appropriate Mailable class for the offer type
     */
    private function getPremiumOfferMailClass($offerType)
    {
        switch ($offerType) {
            case 'quarterly':
                return QuarterlyOfferMail::class;
            case 'semi-annual':
                return SemiAnnualOfferMail::class;
            case 'annual':
                return AnnualOfferMail::class;
            default:
                throw new \InvalidArgumentException("Invalid offer type: {$offerType}");
        }
    }

    /**
     * Send standard quarterly offer email (3 months)
     */
    public function sendStandardQuarterlyOfferEmail(Request $request)
    {
        if ($request->ajax()) {
            return $this->sendStandardOfferEmailAjax($request, 'standard-quarterly');
        }
        return $this->sendStandardOfferEmail('standard-quarterly');
    }

    /**
     * Send standard semi-annual offer email (6 months)
     */
    public function sendStandardSemiAnnualOfferEmail(Request $request)
    {
        if ($request->ajax()) {
            return $this->sendStandardOfferEmailAjax($request, 'standard-semi-annual');
        }
        return $this->sendStandardOfferEmail('standard-semi-annual');
    }

    /**
     * Send standard annual offer email (12 months)
     */
    public function sendStandardAnnualOfferEmail(Request $request)
    {
        if ($request->ajax()) {
            return $this->sendStandardOfferEmailAjax($request, 'standard-annual');
        }
        return $this->sendStandardOfferEmail('standard-annual');
    }

    /**
     * Send standard general offer email (all offers)
     */
    public function sendStandardGeneralOfferEmail(Request $request)
    {
        if ($request->ajax()) {
            return $this->sendStandardOfferEmailAjax($request, 'standard-general');
        }
        return $this->sendStandardOfferEmail('standard-general');    }    /**
     * Send standard offer email (generic method)
     */
    private function sendStandardOfferEmail($offerType)
    {
        try {
            $doctorIds = request('doctors');
            $query = Doctor::with('user')->whereHas('user', function($query) {
                $query->where('account_status', 1);
            });
            if (is_array($doctorIds) && count($doctorIds) > 0) {
                $query->whereIn('id', $doctorIds);
            }
            $doctors = $query->get();

            $successCount = 0;
            $failedCount = 0;
            $failedEmails = [];

            foreach ($doctors as $doctor) {
                try {
                    if ($doctor->user && $doctor->user->email) {
                        $doctor_name = trim($doctor->user->first_name . ' ' . $doctor->user->last_name);
                        $mailClass = $this->getStandardOfferMailClass($offerType);
                        Mail::to($doctor->user->email)->send(new $mailClass($doctor_name));
                        $successCount++;
                        
                        // Record standard offer email in history
                        $this->recordStandardOfferEmailHistory($doctor, $offerType, 'sent');
                        
                        Log::info('Standard offer email sent successfully', [
                            'doctor_id' => $doctor->id,
                            'email' => $doctor->user->email,
                            'name' => $doctor_name,
                            'offer_type' => $offerType
                        ]);
                    } else {
                        $failedCount++;
                        $failedEmails[] = 'Doctor ID: ' . $doctor->id . ' (No email)';
                        Log::warning('Doctor has no email address', [
                            'doctor_id' => $doctor->id
                        ]);
                    }
                } catch (\Exception $e) {
                    $failedCount++;
                    $failedEmails[] = $doctor->user->email ?? 'Doctor ID: ' . $doctor->id;
                    
                    // Record failed standard offer email in history
                    $this->recordStandardOfferEmailHistory($doctor, $offerType, 'failed', $e->getMessage());
                    Log::error('Failed to send standard offer email to doctor', [
                        'doctor_id' => $doctor->id,
                        'email' => $doctor->user->email ?? 'unknown',
                        'error' => $e->getMessage(),
                        'offer_type' => $offerType
                    ]);
                }
            }

            $message = "Email d'offre standard {$offerType} envoyé avec succès à {$successCount} médecins.";
            if ($failedCount > 0) {
                $message .= " {$failedCount} emails ont échoué.";
            }

            return redirect()->back()->with([
                'success' => $message,
                'email_stats' => [
                    'success' => $successCount,
                    'failed' => $failedCount,
                    'failed_emails' => $failedEmails
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error in standard offer email sending', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'offer_type' => $offerType
            ]);
            return redirect()->back()->with([
                'error' => 'Une erreur est survenue lors de l\'envoi des emails: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Send standard offer email with AJAX progress tracking
     */    private function sendStandardOfferEmailAjax(Request $request, $offerType)
    {
        try {
            $doctorIds = $request->input('doctors');
            $query = Doctor::with('user')->whereHas('user', function($query) {
                $query->where('account_status', 1);
            });
            if (is_array($doctorIds) && count($doctorIds) > 0) {
                $query->whereIn('id', $doctorIds);
            }
            $doctors = $query->get();

            $totalDoctors = $doctors->count();
            $successCount = 0;
            $failedCount = 0;
            $failedEmails = [];

            foreach ($doctors as $index => $doctor) {
                try {
                    if ($doctor->user && $doctor->user->email) {
                        $doctor_name = trim($doctor->user->first_name . ' ' . $doctor->user->last_name);                        $mailClass = $this->getStandardOfferMailClass($offerType);
                        Mail::to($doctor->user->email)->send(new $mailClass($doctor_name));
                        $successCount++;
                        
                        // Record standard offer email in history for AJAX
                        $this->recordStandardOfferEmailHistory($doctor, $offerType, 'sent');
                        
                        Log::info('Standard offer email sent successfully', [
                            'doctor_id' => $doctor->id,
                            'email' => $doctor->user->email,
                            'name' => $doctor_name,
                            'offer_type' => $offerType
                        ]);
                    } else {
                        $failedCount++;
                        $failedEmails[] = 'Doctor ID: ' . $doctor->id . ' (No email)';
                        Log::warning('Doctor has no email address', [
                            'doctor_id' => $doctor->id
                        ]);
                    }                } catch (\Exception $e) {
                    $failedCount++;
                    $failedEmails[] = $doctor->user->email ?? 'Doctor ID: ' . $doctor->id;
                    
                    // Record failed standard offer email in history for AJAX
                    $this->recordStandardOfferEmailHistory($doctor, $offerType, 'failed', $e->getMessage());
                    
                    Log::error('Failed to send standard offer email to doctor', [
                        'doctor_id' => $doctor->id,
                        'email' => $doctor->user->email ?? 'unknown',
                        'error' => $e->getMessage(),
                        'offer_type' => $offerType
                    ]);
                }
                if ($index == $totalDoctors - 1) {
                    return response()->json([
                        'success' => true,
                        'completed' => true,
                        'processed' => $totalDoctors,
                        'total' => $totalDoctors,
                        'percentage' => 100,
                        'success_count' => $successCount,
                        'failed_count' => $failedCount,
                        'message' => "Email d'offre standard {$offerType} envoyé avec succès à {$successCount} médecins." . ($failedCount > 0 ? " {$failedCount} emails ont échoué." : '')
                    ]);
                }
            }
        } catch (\Exception $e) {
            Log::error('Error in AJAX standard offer email sending', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'offer_type' => $offerType
            ]);
            return response()->json([
                'success' => false,
                'error' => true,
                'message' => 'Une erreur est survenue lors de l\'envoi des emails: ' . $e->getMessage()
            ], 500);
        }
    }    /**
     * Get the appropriate Mailable class for the standard offer type
     */
    private function getStandardOfferMailClass($offerType)
    {
        switch ($offerType) {
            case 'standard-quarterly':
                return \App\Mail\StandardQuarterlyOfferMail::class;
            case 'standard-semi-annual':
                return \App\Mail\StandardSemiAnnualOfferMail::class;
            case 'standard-annual':
                return \App\Mail\StandardAnnualOfferMail::class;
            case 'standard-general':
                return \App\Mail\StandardGeneralOfferMail::class;
            default:
                throw new \InvalidArgumentException("Invalid standard offer type: {$offerType}");
        }
    }

    /**
     * Get doctors who created their account more than 2 months ago
     */
    public function getDoctorsOlderThan2Months(Request $request)
    {
        try {
            $twoMonthsAgo = Carbon::now()->subMonths(2);
            
            $veteranDoctors = Doctor::with('user')
                ->whereHas('user', function($query) use ($twoMonthsAgo) {
                    $query->where('account_status', 1)
                          ->where('created_at', '<=', $twoMonthsAgo);
                })
                ->get();

            $doctorsData = $veteranDoctors->map(function($doctor) {
                if ($doctor->user) {
                    return [
                        'id' => $doctor->id,
                        'name' => trim($doctor->user->first_name . ' ' . $doctor->user->last_name),
                        'email' => $doctor->user->email,
                        'created_at' => $doctor->user->created_at->format('d/m/Y'),
                        'months_old' => $doctor->user->created_at->diffInMonths(now())
                    ];
                }
                return null;
            })->filter();

            return response()->json([
                'success' => true,
                'doctors' => $doctorsData,
                'count' => $doctorsData->count(),
                'cutoff_date' => $twoMonthsAgo->format('d/m/Y')
            ]);

        } catch (\Exception $e) {
            Log::error('Error getting veteran doctors', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Erreur lors de la récupération des médecins anciens: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get email history with pagination and filtering
     */
    public function emailHistory(Request $request)
    {
        try {
            $query = EmailHistory::with('doctor.user')
                ->orderBy('sent_at', 'desc');

            // Apply filters if provided
            if ($request->has('email_type') && $request->email_type != '') {
                $query->where('email_type', $request->email_type);
            }

            if ($request->has('status') && $request->status != '') {
                $query->where('status', $request->status);
            }

            if ($request->has('date_from') && $request->date_from != '') {
                $query->whereDate('sent_at', '>=', $request->date_from);
            }

            if ($request->has('date_to') && $request->date_to != '') {
                $query->whereDate('sent_at', '<=', $request->date_to);
            }

            $emailHistory = $query->paginate(50);

            return view('pages.admin.mailing.history', compact('emailHistory'));

        } catch (\Exception $e) {
            Log::error('Error getting email history', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()->with([
                'error' => 'Erreur lors de la récupération de l\'historique des emails: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Export email history to CSV
     */
    public function exportEmailHistory(Request $request)
    {
        try {
            $query = EmailHistory::with('doctor.user')
                ->orderBy('sent_at', 'desc');

            // Apply same filters as history view
            if ($request->has('email_type') && $request->email_type != '') {
                $query->where('email_type', $request->email_type);
            }

            if ($request->has('status') && $request->status != '') {
                $query->where('status', $request->status);
            }

            if ($request->has('date_from') && $request->date_from != '') {
                $query->whereDate('sent_at', '>=', $request->date_from);
            }

            if ($request->has('date_to') && $request->date_to != '') {
                $query->whereDate('sent_at', '<=', $request->date_to);
            }

            $emailHistory = $query->get();

            $filename = 'email_history_' . date('Y-m-d_H-i-s') . '.csv';
            
            $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            ];

            $callback = function() use ($emailHistory) {
                $file = fopen('php://output', 'w');
                
                // Add BOM for UTF-8
                fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));
                
                // CSV headers
                fputcsv($file, [
                    'ID',
                    'Médecin',
                    'Email',
                    'Type d\'email',
                    'Sujet',
                    'Statut',
                    'Date d\'envoi',
                    'Message d\'erreur'
                ]);

                foreach ($emailHistory as $email) {
                    fputcsv($file, [
                        $email->id,
                        $email->doctor_name,
                        $email->doctor_email,
                        $email->email_type,
                        $email->subject,
                        $email->status == 'sent' ? 'Envoyé' : 'Échoué',
                        $email->sent_at->format('d/m/Y H:i:s'),
                        $email->error_message ?? ''
                    ]);
                }

                fclose($file);
            };

            return response()->stream($callback, 200, $headers);

        } catch (\Exception $e) {
            Log::error('Error exporting email history', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()->with([
                'error' => 'Erreur lors de l\'export de l\'historique: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Record email in history
     */
    private function recordEmailHistory($doctor, $emailType, $subject, $messageContent, $status, $errorMessage = null)
    {
        try {
            EmailHistory::create([
                'doctor_id' => $doctor->id,
                'doctor_email' => $doctor->user->email ?? $doctor->email,
                'doctor_name' => $doctor->user ? trim($doctor->user->first_name . ' ' . $doctor->user->last_name) : 'Unknown',
                'email_type' => $emailType,
                'subject' => $subject,
                'message_content' => $messageContent,
                'sent_from_device' => request()->ip(),
                'status' => $status,
                'error_message' => $errorMessage,
                'sent_at' => now(),
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to record email history', [
                'doctor_id' => $doctor->id,
                'email_type' => $emailType,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Record premium offer email history
     */
    private function recordPremiumOfferEmailHistory($doctor, $offerType, $status, $errorMessage = null)
    {
        $this->recordEmailHistory(
            $doctor,
            'premium-offer',
            $this->getPremiumOfferSubject($offerType),
            $this->getPremiumOfferMessageContent($offerType),
            $status,
            $errorMessage
        );
    }

    /**
     * Record standard offer email history
     */
    private function recordStandardOfferEmailHistory($doctor, $offerType, $status, $errorMessage = null)
    {
        $this->recordEmailHistory(
            $doctor,
            'standard-offer',
            $this->getStandardOfferSubject($offerType),
            $this->getStandardOfferMessageContent($offerType),
            $status,
            $errorMessage
        );
    }

    /**
     * Get the standard offer subject based on offer type
     */
    private function getStandardOfferSubject($offerType)
    {
        switch ($offerType) {
            case 'standard-quarterly':
                return 'Offre Standard 3 mois - DocExpress';
            case 'standard-semi-annual':
                return 'Offre Standard 6 mois - DocExpress';
            case 'standard-annual':
                return 'Offre Standard 12 mois - DocExpress';
            case 'standard-general':
                return 'Offres Standard - DocExpress';
            default:
                return 'Offre Standard - DocExpress';
        }
    }

    /**
     * Get the standard offer message content based on offer type
     */
    private function getStandardOfferMessageContent($offerType)
    {
        switch ($offerType) {
            case 'standard-quarterly':
                return 'Découvrez notre offre standard pour 3 mois avec des fonctionnalités essentielles pour votre pratique médicale.';
            case 'standard-semi-annual':
                return 'Découvrez notre offre standard pour 6 mois avec des fonctionnalités essentielles pour votre pratique médicale.';
            case 'standard-annual':
                return 'Découvrez notre offre standard pour 12 mois avec des fonctionnalités essentielles pour votre pratique médicale.';
            case 'standard-general':
                return 'Découvrez nos offres standard avec des fonctionnalités essentielles pour votre pratique médicale.';
            default:
                return 'Découvrez notre offre standard pour votre pratique médicale.';
        }
    }

    /**
     * Get the premium offer subject based on offer type
     */
    private function getPremiumOfferSubject($offerType)
    {
        switch ($offerType) {
            case 'quarterly':
                return 'Offre Premium 3 mois - DocExpress';
            case 'semi-annual':
                return 'Offre Premium 6 mois - DocExpress';
            case 'annual':
                return 'Offre Premium 12 mois - DocExpress';
            default:
                return 'Offre Premium - DocExpress';
        }
    }

    /**
     * Get the premium offer message content based on offer type
     */
    private function getPremiumOfferMessageContent($offerType)
    {
        switch ($offerType) {
            case 'quarterly':
                return 'Découvrez notre offre premium pour 3 mois avec toutes les fonctionnalités avancées pour votre pratique médicale.';
            case 'semi-annual':
                return 'Découvrez notre offre premium pour 6 mois avec toutes les fonctionnalités avancées pour votre pratique médicale.';
            case 'annual':
                return 'Découvrez notre offre premium pour 12 mois avec toutes les fonctionnalités avancées pour votre pratique médicale.';
            default:
                return 'Découvrez notre offre premium avec toutes les fonctionnalités avancées pour votre pratique médicale.';
        }
    }
}

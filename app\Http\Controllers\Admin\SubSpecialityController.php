<?php

namespace App\Http\Controllers\Admin;
use App\Repositories\SpecialityRepositoryInterface;
use App\Http\Requests\SubSpeciality\CreateSubSpecialityRequest;
use App\Http\Requests\SubSpeciality\UpdateSubSpecialityRequest;
use App\Repositories\SubSpecialityRepositoryInterface;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use App\Models\SubSpeciality;

class SubSpecialityController extends Controller
{
    protected $subspecialityRepository;
    protected $specialityRepository;

    public function __construct(SubSpecialityRepositoryInterface $subspecialityRepository,
    SpecialityRepositoryInterface $specialityRepository)
    {
        $this->subspecialityRepository = $subspecialityRepository;
        $this->specialityRepository = $specialityRepository;
    }

    public function index()
    {
        $subspecialities = $this->subspecialityRepository->all();
        $specialities = $this->specialityRepository->all();
        return view('pages.admin.subspecialities.index', compact('subspecialities', 'specialities'));

    }

    public function store(CreateSubSpecialityRequest $request)
    {
        // $request->validate([
        //     'name' => 'required|unique:sub_specialities',
        //     'speciality_id' => 'required|exists:specialities,id',
        // ]);

        // SubSpeciality::create($request->only(['name', 'speciality_id']));
        $this->subspecialityRepository->store($request->validated());
        return redirect()->back()->with('success', 'Subspeciality added successfully.');
    }

    public function edit(Request $request)
    {
        $subspeciality = $this->subspecialityRepository->get($request->id);
        return response()->json([
            'id' => $subspeciality->id,
            'name_en' => $subspeciality->name_en,
            'name_fr' => $subspeciality->name_fr,
            'speciality_id' => $subspeciality->speciality_id
        ]);
    }

    public function update(UpdateSubSpecialityRequest $request)
    {
        $data = $request->validated();

        $this->subspecialityRepository->update($data, $request->id);

        return redirect()->back()->with('success', trans("alerts.success.subspeciality_update_successfully"));
    }

} 

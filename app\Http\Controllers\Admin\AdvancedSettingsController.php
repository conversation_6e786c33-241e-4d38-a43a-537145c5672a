<?php



namespace App\Http\Controllers\Admin;
use App\Http\Controllers\Controller;
use App\Repositories\PatientRepositoryInterface;
use App\Repositories\DoctorRepositoryInterface;
use App\Repositories\AppointmentRepositoryInterface;
use Illuminate\Support\Facades\Log;
use App\Models\Review;

class AdvancedSettingsController extends Controller
{
    protected $patientrepository;
    protected $doctorrepository;
    protected $appointmentrepository;
    public function __construct(PatientRepositoryInterface $patientrepository, DoctorRepositoryInterface $doctorrepository, AppointmentRepositoryInterface $appointmentrepository)
    {
        $this->patientrepository = $patientrepository;
        $this->doctorrepository = $doctorrepository;
        $this->appointmentrepository = $appointmentrepository;
    }
    public function index()
    {
        
       
        return view('pages.admin.advanced-settings.index');
    }
    public function AutomaticBookingAppointments()
    {
        Log::info('Starting automatic booking of appointments.');
        $patients = $this->patientrepository->Getall();
        $doctors = $this->doctorrepository->Getall();
   
        $createdAppointments = 0;
        $processedPatients = []; // Track patients who already have an appointment
        
        foreach ($patients as $patient) {
            // Skip if patient already has an appointment
            if (in_array($patient->patient_id, $processedPatients)) {
                continue;
            }
            
            foreach ($doctors as $doctor) {
                if ($patient->user->gender == $doctor->user->gender && $patient->city == $doctor->city) {
                    if ($this->doctorrepository->hasValidAddress($doctor->id)) {
                        $appointmentDate = date('Y-m-d', strtotime('+' . rand(1, 30) . ' days'));
                        $appointmentTime = sprintf('%02d:%02d:00', rand(9, 18), rand(0, 1) == 0 ? 0 : 30);
                        $appointment = [
                            'patient_id' => $patient->patient_id,
                            'doctor_id' => $doctor->id,
                            'date' => $appointmentDate . ' ' . $appointmentTime,
                            'status' => 'pending',
                        ];
                        try {
                            $this->appointmentrepository->create($appointment);
                            $createdAppointments++;
                            $processedPatients[] = $patient->patient_id; // Mark this patient as processed
                            break; // Exit doctor loop after successful appointment
                        } catch (\Exception $e) {
                            Log::error('Failed to create appointment for patient ID ' . $patient->id . ' and doctor ID ' . $doctor->id . ': ' . $e->getMessage());
                        }
                    } else {
                        Log::warning('Doctor ID ' . $doctor->id . ' does not have a valid address.');
                    }
                } else {
                    Log::info('Skipping patient ID ' . $patient->id . ' and doctor ID ' . $doctor->id . ' due to gender or city mismatch.');
                }
            }
        }
        
        if ($createdAppointments > 0) {
            Log::info('Successfully created ' . $createdAppointments . ' appointments.');
            return response()->json(['message' => 'Appointments created successfully']);
        } else {
            Log::warning('No appointments were created.');
            return response()->json(['message' => 'No appointments were created'], 500);
        }
    }
    public function AutomaticReviews()
    {
        $appointments = $this->appointmentrepository->all();
        $createdReviews = 0;   
        Log::debug('Total appointments retrieved: ' . $appointments->count());   
        $reviewMessagesPath = base_path('public/assets/js/reviews.json');
        $reviewMessages = [];   
        $processedPatients = []; // Track patients who have already submitted a review
        
        if (file_exists($reviewMessagesPath)) {
            $jsonContent = json_decode(file_get_contents($reviewMessagesPath), true);        
            $reviewMessages = $jsonContent['phrases'];
        }         
        
        foreach ($appointments as $appointment) {
            // Skip if patient has already submitted a review
            if (in_array($appointment->patient_id, $processedPatients)) {
                continue;
            }
            
            if ($appointment->patient_id != 0) {
                $review = [
                    'patient_id' => $appointment->patient_id,
                    'doctor_id' => $appointment->doctor_id,
                    'rating' => 5,
                    'body' => $reviewMessages[array_rand($reviewMessages)],
                ];    
                try {
                    Review::create($review);
                    $createdReviews++;
                    $processedPatients[] = $appointment->patient_id; // Mark this patient as processed
                    Log::info('Created review for appointment ID: ' . $appointment->id);
                } catch (\Exception $e) {
                    Log::error('Failed to create review for appointment ID ' . $appointment->id . ': ' . $e->getMessage());
                }
            } else {
                Log::debug('Appointment ID ' . $appointment->id . ' not eligible: status=' . $appointment->status . ', patient_id=' . $appointment->patient_id);
            }
        }      
        
        if ($createdReviews > 0) {
            Log::info('Successfully created ' . $createdReviews . ' reviews.');
            return response()->json(['message' => 'Reviews created successfully'], 200);
        } else {
            Log::warning('No reviews were created.');
            return response()->json(['message' => 'No reviews were created'], 200);
        }
    }       
        
}

<?php

namespace App\Http\Controllers\assistanteadministrative;

use App\Http\Controllers\Controller;
use App\Http\Requests\MedicalCertificate\CreateMedicalCertificateRequest;
use App\Services\MedicalCertificateService\IMedicalCertificateService;

class MedicalCertificateController extends Controller
{
    protected $medicalCertificateService;

    public function __construct(IMedicalCertificateService $medicalCertificateService)
    {
        $this->medicalCertificateService = $medicalCertificateService;
    }

    public function store(CreateMedicalCertificateRequest $request)
    {
        $data = $request->validated();

        if (empty($data)) {
            return;
        }

        $document = $this->medicalCertificateService->create($data);

        return $document->stream('document.pdf');
    }
}

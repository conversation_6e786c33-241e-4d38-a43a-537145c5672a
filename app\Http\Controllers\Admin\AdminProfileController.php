<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\UpdateAdminRequest;
use App\Repositories\UserRepositoryInterface;

class AdminProfileController extends Controller
{
    protected $userRepository;

    public function __construct(UserRepositoryInterface $userRepository)
    {
        $this->userRepository = $userRepository;
    }

    public function index()
    {
        return view('pages.admin.profile.index');
    }

    public function update(UpdateAdminRequest $request)
    {
        $this->userRepository->update($request->validated(), auth()->id());

        return redirect()->route('profile.index')->with([
            'success' => trans("alerts.success.profile_updated_successfully")
        ]);
    }
}

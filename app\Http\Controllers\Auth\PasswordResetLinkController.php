<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log; // Add this line
use Illuminate\Support\Facades\Password;
use Illuminate\View\View;

class PasswordResetLinkController extends Controller
{
    /**
     * Display the password reset link request view.
     */
    public function create(Request $request): View
    {
        $email = $request->input('email', '');
        return view('pages.auth.forgot-password', ['email' => $email]);
    }

    /**
     * Handle an incoming password reset link request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'email' => ['required', 'email'],
        ]);

        // Retrieve the user's name from the database
        $user = \App\Models\User::where('email', $request->email)->first();
        $name = $user ? $user->last_name . ' ' . $user->first_name : '';

        // Log the email sending attempt
        Log::info('Attempting to send password reset link to: ' . $request->email);

        // We will send the password reset link to this user. Once we have attempted
        // to send the link, we will examine the response then see the message we
        // need to show to the user. Finally, we'll send out a proper response.
        $status = Password::sendResetLink(
            $request->only('email'),
            function ($user, $token) use ($name, $request) {
                Log::info('Sending notification to: ' . $request->email); // Add this line
                $user->notify(new \App\Notifications\ResetPasswordNotification($token, $name, $request->email, $request->route()->getName()));
            }
        );

        return $status == Password::RESET_LINK_SENT
                    ? back()->with('status', __($status))
                    : back()->withInput($request->only('email'))
                            ->withErrors(['email' => __($status)]);
    }
}

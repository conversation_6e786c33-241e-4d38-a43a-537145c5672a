<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\SMSService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;

class AppointmentReminderDaemon extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'appointments:daemon';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Run as a daemon to continuously check and send appointment reminders';

    /**
     * A flag to control the daemon loop
     */
    protected $shouldRun = true;

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting appointment reminder daemon...');
        Log::info('Appointment reminder daemon started');

        // Set up signal handling for graceful shutdown
        $this->setupSignalHandlers();

        // Run the daemon loop
        while ($this->shouldRun) {
            try {
                $this->checkAppointmentReminders();
                
                // Process signals if available
                if (function_exists('pcntl_signal_dispatch')) {
                    pcntl_signal_dispatch();
                }
                
                sleep(60); // Check every minute
            } catch (\Exception $e) {
                Log::error('Daemon error: ' . $e->getMessage());
                sleep(30); // Wait before retrying after error
            }
        }

        Log::info('Appointment reminder daemon stopped');
        return 0;
    }
    
    /**
     * Set up signal handlers for graceful shutdown
     */
    private function setupSignalHandlers()
    {
        if (function_exists('pcntl_signal')) {
            pcntl_signal(SIGTERM, function() {
                $this->shouldRun = false;
                $this->info('Signal received, stopping daemon gracefully...');
            });
            pcntl_signal(SIGINT, function() {
                $this->shouldRun = false;
                $this->info('Signal received, stopping daemon gracefully...');
            });
        }
    }
    
    /**
     * Check for appointments that need reminders
     */
    protected function checkAppointmentReminders()
    {
        $now = Carbon::now();
        
        // Only run every 5 minutes to reduce database load
        if ($now->minute % 5 == 0) {
            try {
                $smsService = new SMSService();
                $stats = $smsService->sendAppointmentReminders();
                
                $this->logReminderStats($stats);
            } catch (\Exception $e) {
                Log::error('Error in reminder service: ' . $e->getMessage());
            }
        }
    }
    
    /**
     * Log reminder statistics
     */
    private function logReminderStats($stats)
    {
        if (!isset($stats['types'])) {
            return;
        }
        
        $this->info("24h reminders: {$stats['types']['24h']['sent_to_patients']} patients, {$stats['types']['24h']['sent_to_doctors']} doctors");
        $this->info("48h reminders: {$stats['types']['48h']['sent_to_patients']} patients, {$stats['types']['48h']['sent_to_doctors']} doctors");
        
        if ($stats['errors'] > 0) {
            $this->warn("Encountered {$stats['errors']} errors during processing");
        }
    }
}

<?php

namespace App\Http\Controllers\assistanteadministrative;

use App\Http\Controllers\Controller;
use App\Services\AppointmentService\IAppointmentService;
use App\Services\DoctorService\IDoctorService;
use App\Services\MedicalCertificateService\IMedicalCertificateService;
use App\Services\PatientService\IPatientService;
use App\Services\PrescriptionService\IPrescriptionService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

class PatientProfileController extends Controller
{
    protected $appointmentService;
    protected $doctorService;
    protected $patientService;
    protected $prescriptionService;
    protected $medicalCertificateService;

    public function __construct(
        IAppointmentService $appointmentService,
        IDoctorService $doctorService,
        IPatientService $patientService,
        IPrescriptionService $prescriptionService,
        IMedicalCertificateService $medicalCertificateService
    ) {
        $this->appointmentService = $appointmentService;
        $this->doctorService = $doctorService;
        $this->patientService = $patientService;
        $this->prescriptionService = $prescriptionService;
        $this->medicalCertificateService = $medicalCertificateService;
    }

    public function index(Request $request, $doctor_id, $id)
    {
        $doctor = $this->doctorService->get($doctor_id);
        if (!$doctor) {
            return abort(404, 'Doctor not found');
        }

        $patient = $this->patientService->get($id);
        if (!$patient) {
            return abort(404, 'Patient not found');
        }

        $medical_certificates = $this->medicalCertificateService->getAll($id);
        $prescriptions = $this->prescriptionService->getPatientPrescriptions($id);
        $appointments = $this->appointmentService->getDoctorPatientAppointments($doctor->id, $patient->id);

        return view('pages.user-accounts.assistante-administrative.patient-profile.index', compact('doctor', 'patient', 'appointments', 'medical_certificates', 'prescriptions'));
    }
}

<?php

namespace App\Http\Controllers\Auth;

use Illuminate\View\View;
use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\RegistrationRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\RedirectResponse;
use Illuminate\Auth\Events\Registered;
use App\Providers\RouteServiceProvider;
use App\Services\PatientService\PatientService;
use App\Services\OTP\OTPService;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;

class RegisteredUserController extends Controller
{
    protected $patientService;
    protected $otpService;

    public function __construct(PatientService $patientService, OTPService $otpService)
    {
        $this->patientService = $patientService;
        $this->otpService = $otpService;
    }

    /**
     * Display the registration view.
     */
    public function create(): View
    {
        // Log the registration page access
        Log::info('Registration page accessed', [
            'url' => request()->fullUrl(),
            'referer' => request()->header('referer'),
            'user_agent' => request()->userAgent(),
            'ip' => request()->ip()
        ]);
        
        return view('pages.auth.register');
    }

    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(RegistrationRequest $request): RedirectResponse
    {
        try {
            // Log the registration attempt
            Log::info('Registration attempt', [
                'email' => $request->email,
                'phone' => $request->phone,
                'url' => request()->fullUrl(),
                'ip' => request()->ip()
            ]);
            
            $userData = $request->validated();
            
            // Create user but don't log in yet
            $user = $this->patientService->create($userData);
            
            Log::info('User created successfully', [
                'user_id' => $user->id,
                'email' => $user->email,
                'phone' => $user->phone
            ]);
            
            // Generate OTP - Fixed parameter order: min must be <= max
            $otp = random_int(100000, 999999);
            $user->otp = $otp;
            $user->otp_expires_at = now()->addMinutes(5);
            $user->save();
            
            // Send OTP
            $otpSent = $this->otpService->sendOTP($user->phone, $otp);
            
            Log::info('OTP generation and sending', [
                'user_id' => $user->id,
                'otp_sent' => $otpSent,
                'expires_at' => $user->otp_expires_at
            ]);
            
            // Store user ID in session for OTP verification
            Session::put('user_for_otp', $user->id);
            
            // Redirect to OTP verification page
            return redirect()->route('otp.verify');
            
        } catch (\Illuminate\Database\QueryException $e) {
            Log::error('Registration database error', [
                'error' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'email' => $request->email,
                'phone' => $request->phone
            ]);
            
            if ($e->errorInfo[1] == 1062) {
                return redirect()->back()->withErrors(['phone' => 'The phone number has already been taken.'])->withInput();
            }
            throw $e;
        } catch (\Exception $e) {
            Log::error('Registration general error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'email' => $request->email,
                'phone' => $request->phone
            ]);
            
            throw $e;
        }
    }

    public function conditionsgénerale()
    {
        return view('layouts.components.Conditions.conditions-génerale');
    }
    
    public function conditionsparticulier()
    {   
        return view('layouts.components.Conditions.CU_Docexpress');    
    }
}

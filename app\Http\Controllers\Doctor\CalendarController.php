<?php

namespace App\Http\Controllers\Doctor;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Models\Appointment;
use App\Models\CalendarEvent;
use App\Models\CalendarAccount;
use Illuminate\Http\Request;
use Carbon\Carbon;

class CalendarController extends Controller
{
    public function index()
    {
        $user = Auth::user();
        $appointments = Appointment::where('doctor_id', $user->doctor->id)->get();
        $calendarEvents = CalendarEvent::where('user_id', $user->id)->get();

        $events = [];
        // Ajoute les rendez-vous internes
        foreach ($appointments as $appt) {
            $calEvent = $calendarEvents->where('appointment_id', $appt->id)->first();
            $events[] = [
                'title' => $appt->motif,
                'start' => $appt->date,
                'status' => $appt->status,
                'sync_status' => $calEvent ? $calEvent->sync_status : 'none',
                'appointment_id' => $appt->id,
                'description' => $calEvent ? $calEvent->description ?? '' : '',
            ];
        }
        // Ajoute les événements Google non liés à un rendez-vous interne
        $selectedCalendarId = request('calendar_id');
        $googleEvents = $calendarEvents->where('appointment_id', null)->where('provider', 'google');
        if ($selectedCalendarId) {
            $googleEvents = $googleEvents->where('google_calendar_id', $selectedCalendarId);
        }
        foreach ($googleEvents as $event) {
            $events[] = [
                'title' => $event->title ?? $event->external_event_id,
                'start' => $event->start_time ?? $event->last_synced_at ?? now(),
                'status' => 'from_google',
                'sync_status' => $event->sync_status,
                'appointment_id' => null,
                'description' => $event->description ?? '',
            ];
        }

        // Ajoute les événements Outlook non liés à un rendez-vous interne
        $outlookEvents = $calendarEvents->where('appointment_id', null)->where('provider', 'outlook');
        foreach ($outlookEvents as $event) {
            $events[] = [
                'title' => $event->title ?? $event->external_event_id,
                'start' => $event->start_time ?? $event->last_synced_at ?? now(),
                'status' => 'from_outlook',
                'sync_status' => $event->sync_status,
                'appointment_id' => null,
                'description' => $event->description ?? '',
            ];
        }

        return view('pages.user-accounts.doctor.calendar.calendar', [
            'events' => $events,
        ]);
    }

    public function syncGoogle(Request $request)
    {
        $user = Auth::user();
        $calendarAccount = $user->calendarAccounts()->where('provider', 'google')->first();
        if (!$calendarAccount) {
            return response()->json(['success' => false, 'error' => 'Google Calendar non connecté']);
        }
        
        try {
            // Use the robust Google Calendar controller
            $googleController = app(\App\Http\Controllers\GoogleCalendarController::class);
            $events = $googleController->getGoogleEvents($user->id);
            
            if (!$events) {
                return response()->json(['success' => false, 'error' => 'Impossible de récupérer les événements Google Calendar']);
            }
            
            // Get valid Google client with token management
            $client = new \Google_Client();
            $client->setClientId(config('services.google.client_id'));
            $client->setClientSecret(config('services.google.client_secret'));
            $client->setRedirectUri(config('services.google.redirect'));
            
            $validToken = $googleController->getValidToken($user->id);
            if (!$validToken) {
                return response()->json(['success' => false, 'error' => 'Token Google Calendar invalide ou expiré']);
            }
            
            $client->setAccessToken(['access_token' => $validToken]);
            $service = new \Google_Service_Calendar($client);
            // Utiliser le calendrier Google dédié créé lors de la connexion
            $calendarId = $request->input('calendar_id') ?? $request->calendar_id ?? $calendarAccount->external_calendar_id ?? 'primary';

            // 1. Exporter les rendez-vous internes vers Google Calendar (si non synchronisés)
            $appointments = Appointment::where('doctor_id', $user->doctor->id)->get();
            foreach ($appointments as $appt) {
                $existingEvent = CalendarEvent::where('provider', 'google')
                    ->where('appointment_id', $appt->id)
                    ->where('user_id', $user->id)
                    ->first();
                if (!$existingEvent) {
                    $patient = $appt->patient;
                    $patientInfo = '';
                    if ($patient && $patient->user) {
                        $patientInfo = "Patient : " . ($patient->user->first_name ?? '') . " " . ($patient->user->last_name ?? '') . "\n";
                        if (!empty($patient->user->phone)) {
                            $patientInfo .= "Téléphone : " . $patient->user->phone . "\n";
                        }
                        if (!empty($patient->user->email)) {
                            $patientInfo .= "Email : " . $patient->user->email . "\n";
                        }
                    }
                    $event = new \Google_Service_Calendar_Event([
                        'summary' => $appt->motif,
                        'description' => trim(($appt->comment ?? '') . "\n" . $patientInfo),
                        'start' => [
                            'dateTime' => $this->formatAppointmentDateForCalendar($appt->date)->format('Y-m-d\TH:i:s.000'),
                            'timeZone' => 'Africa/Casablanca', // Force Morocco timezone
                        ],
                        'end' => [
                            'dateTime' => $this->formatAppointmentDateForCalendar($appt->date)->addHour()->format('Y-m-d\TH:i:s.000'),
                            'timeZone' => 'Africa/Casablanca', // Force Morocco timezone
                        ],
                    ]);
                    try {
                        // Debug log the event data being sent to Google
                        Log::info('Creating Google event with data', [
                            'appointment_id' => $appt->id,
                            'event_summary' => $appt->motif,
                            'start_datetime' => $this->formatAppointmentDateForCalendar($appt->date)->format('Y-m-d\TH:i:s.000'),
                            'end_datetime' => $this->formatAppointmentDateForCalendar($appt->date)->addHour()->format('Y-m-d\TH:i:s.000'),
                            'timezone_config' => config('app.timezone'),
                            'appointment_original_date' => $appt->date,
                            'calendar_id' => $calendarId
                        ]);
                        
                        $createdEvent = $service->events->insert($calendarId, $event);
                        CalendarEvent::create([
                            'user_id' => $user->id,
                            'appointment_id' => $appt->id,
                            'provider' => 'google',
                            'google_calendar_id' => $calendarId,
                            'external_event_id' => $createdEvent->getId(),
                            'sync_status' => 'pending',
                            'last_synced_at' => now(),
                        ]);
                    } catch (\Exception $e) {
                        Log::error('Erreur export rendez-vous Google Calendar : ' . $e->getMessage());
                    }
                }
            }

            // 2. Importer les événements Google Calendar (du calendrier dédié) dans la base locale (si non synchronisés)
            $events = $service->events->listEvents($calendarId, [
                'maxResults' => 50,
                'orderBy' => 'startTime',
                'singleEvents' => true,
                'timeMin' => now()->subMonths(1)->toRfc3339String(),
                'timeZone' => 'Africa/Casablanca', // Force Morocco timezone for all requests
            ]);
            foreach ($events->getItems() as $event) {
                Log::info('Google event debug', [
                    'id' => $event->getId(),
                    'start' => $event->getStart(),
                    'description' => $event->getDescription(),
                    'summary' => $event->getSummary(),
                    'raw' => $event,
                ]);
                $existing = CalendarEvent::where('provider', 'google')
                    ->where('external_event_id', $event->getId())
                    ->where('user_id', $user->id)
                    ->first();
                if (!$existing && $event->getStatus() !== 'cancelled') {
                    $start = $event->getStart();
                    $startTime = null;
                    if ($start) {
                        if (isset($start->dateTime) && $start->dateTime) {
                            $originalDateTime = $start->dateTime;
                            $originalTimezone = $start->timeZone ?? '';
                            
                            // Extract date and time using regex
                            preg_match('/(\d{4}-\d{2}-\d{2})T(\d{2}:\d{2}:\d{2})/', $originalDateTime, $matches);
                            
                            if (count($matches) >= 3) {
                                $date = $matches[1];
                                $time = $matches[2];
                                
                                // Check if timezone is America/New_York and subtract 6 hours
                                if ($originalTimezone === 'America/New_York') {
                                    $dateTime = \Carbon\Carbon::parse($date . ' ' . $time);
                                    $adjustedDateTime = $dateTime->subHours(6);
                                    $startTime = $adjustedDateTime->format('Y-m-d H:i:s');
                                    
                                    Log::info('Google event America/New_York timezone adjustment', [
                                        'original_datetime' => $originalDateTime,
                                        'original_timezone' => $originalTimezone,
                                        'extracted_time' => $time,
                                        'adjusted_time' => $adjustedDateTime->format('H:i:s'),
                                        'final_stored_time' => $startTime,
                                        'note' => 'Subtracted 6 hours for America/New_York timezone'
                                    ]);
                                } else {
                                    // For other timezones, keep the exact time
                                    $startTime = $date . ' ' . $time;
                                    
                                    Log::info('Google event time extraction (no timezone adjustment)', [
                                        'original_datetime' => $originalDateTime,
                                        'original_timezone' => $originalTimezone,
                                        'extracted_time' => $time,
                                        'final_stored_time' => $startTime,
                                        'note' => 'No timezone adjustment needed'
                                    ]);
                                }
                            } else {
                                // Fallback to previous method
                                $eventTime = \Carbon\Carbon::parse($originalDateTime);
                                $startTime = $eventTime->format('Y-m-d H:i:s');
                                
                                Log::info('Google event time fallback', [
                                    'original_datetime' => $originalDateTime,
                                    'stored_time' => $startTime,
                                    'note' => 'Using fallback parsing method'
                                ]);
                            }
                            
                        } elseif (isset($start->date) && $start->date) {
                            $startTime = \Carbon\Carbon::parse($start->date)->toDateTimeString();
                        }
                    }
                    $description = $event->getDescription() ?? '';
                    $title = $event->getSummary() ?? $event->getId();
                    CalendarEvent::create([
                        'user_id' => $user->id,
                        'appointment_id' => null, // non lié à un rendez-vous interne
                        'provider' => 'google',
                        'google_calendar_id' => $calendarId,
                        'external_event_id' => $event->getId(),
                        'sync_status' => 'synced',
                        'last_synced_at' => now(),
                        'start_time' => $startTime,
                        'description' => $description,
                        'title' => $title,
                    ]);
                }
            }
            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            Log::error('Erreur synchronisation Google Calendar : ' . $e->getMessage());
            return response()->json(['success' => false, 'error' => $e->getMessage()]);
        }
    }

    public function getGoogleCalendars()
    {
        $user = Auth::user();
        $calendarAccount = $user->calendarAccounts()->where('provider', 'google')->first();
        if (!$calendarAccount) {
            return response()->json([]);
        }
        try {
            // Use the robust Google Calendar controller
            $googleController = app(\App\Http\Controllers\GoogleCalendarController::class);
            $validToken = $googleController->getValidToken($user->id);
            
            if (!$validToken) {
                Log::error('Google Calendar token invalid for calendar list');
                return response()->json([]);
            }
            
            $client = new \Google_Client();
            $client->setClientId(config('services.google.client_id'));
            $client->setClientSecret(config('services.google.client_secret'));
            $client->setRedirectUri(config('services.google.redirect'));
            $client->setAccessToken(['access_token' => $validToken]);
            
            $service = new \Google_Service_Calendar($client);
            $calendarList = $service->calendarList->listCalendarList();
            $calendars = [];
            foreach ($calendarList->getItems() as $cal) {
                $calendars[] = [
                    'id' => $cal->getId(),
                    'summary' => $cal->getSummary(),
                ];
            }
            return response()->json($calendars);
        } catch (\Exception $e) {
            Log::error('Erreur récupération calendriers Google : ' . $e->getMessage());
            return response()->json([]);
        }
    }


 public function syncOutlook(Request $request)
{
    $user = Auth::user();
    $calendarAccount = $user->calendarAccounts()->where('provider', 'outlook')->first();

    if (!$calendarAccount) {
        return response()->json(['success' => false, 'error' => 'Outlook Calendar non connecté']);
    }

    try {
        // Use the robust Outlook Calendar controller
        $outlookController = app(\App\Http\Controllers\OutlookCalendarController::class);
        $events = $outlookController->getOutlookEvents($user->id);
        
        if (!$events) {
            return response()->json(['success' => false, 'error' => 'Impossible de récupérer les événements Outlook Calendar']);
        }
        
        // Get valid access token with robust token management
        $validToken = $outlookController->getValidToken($user->id);
        if (!$validToken) {
            return response()->json(['success' => false, 'error' => 'Token Outlook Calendar invalide ou expiré']);
        }

        $client = new \GuzzleHttp\Client([
            'headers' => [
                'Authorization' => 'Bearer ' . $validToken,
                'Accept' => 'application/json',
            ]
        ]);

        // Get calendar ID from request or use stored one
        $requestCalendarId = $request->input('calendar_id');
        $calendarId = $requestCalendarId ?: $calendarAccount->external_calendar_id;
        
        // Update stored calendar ID if different from request
        if ($requestCalendarId && $requestCalendarId !== $calendarAccount->external_calendar_id) {
            $calendarAccount->update(['external_calendar_id' => $requestCalendarId]);
        }

        // 1. Exporter les rendez-vous internes vers Outlook
        $appointments = Appointment::where('doctor_id', $user->doctor->id)->get();
        foreach ($appointments as $appt) {
            $existingEvent = \App\Models\CalendarEvent::where('provider', 'outlook')
                ->where('appointment_id', $appt->id)
                ->where('user_id', $user->id)
                ->first();

            if (!$existingEvent) {
                $patient = $appt->patient;
                $patientInfo = '';
                if ($patient && $patient->user) {
                    $patientInfo = "Patient : " . ($patient->user->first_name ?? '') . " " . ($patient->user->last_name ?? '') . "\n";
                    if (!empty($patient->user->phone)) {
                        $patientInfo .= "Téléphone : " . $patient->user->phone . "\n";
                    }
                    if (!empty($patient->user->email)) {
                        $patientInfo .= "Email : " . $patient->user->email . "\n";
                    }
                }

                $eventData = [
                    "subject" => $appt->motif,
                    "body" => [
                        "contentType" => "HTML",
                        "content" => trim(($appt->comment ?? '') . "\n" . $patientInfo)
                    ],
                    "start" => [
                        "dateTime" => $this->formatAppointmentDateForCalendar($appt->date)->format('Y-m-d\TH:i:s.000'),
                        "timeZone" => $this->getAppTimezone()
                    ],
                    "end" => [
                        "dateTime" => $this->formatAppointmentDateForCalendar($appt->date)->addHour()->format('Y-m-d\TH:i:s.000'),
                        "timeZone" => $this->getAppTimezone()
                    ]
                ];

                try {
                    $eventsEndpoint = $calendarId 
                        ? "https://graph.microsoft.com/v1.0/me/calendars/{$calendarId}/events"
                        : 'https://graph.microsoft.com/v1.0/me/events';
                    
                    // Debug log the event data being sent
                    Log::info('Creating Outlook event with data', [
                        'appointment_id' => $appt->id,
                        'event_data' => $eventData,
                        'endpoint' => $eventsEndpoint,
                        'timezone_config' => config('app.timezone'),
                        'appointment_original_date' => $appt->date
                    ]);
                        
                    $response = $client->post($eventsEndpoint, [
                        'headers' => [
                            'Authorization' => 'Bearer ' . $validToken,
                            'Accept' => 'application/json',
                            'Content-Type' => 'application/json',
                            'Prefer' => 'outlook.timezone="' . config('app.timezone') . '"',
                        ],
                        'json' => $eventData
                    ]);
                    $createdEvent = json_decode($response->getBody(), true);

                    \App\Models\CalendarEvent::create([
                        'user_id' => $user->id,
                        'appointment_id' => $appt->id,
                        'provider' => 'outlook',
                        'external_event_id' => $createdEvent['id'],
                        'sync_status' => 'pending',
                        'last_synced_at' => now(),
                    ]);
                } catch (\Exception $e) {
                    Log::error('Erreur export rendez-vous Outlook : ' . $e->getMessage());
                }
            }
        }

        // 2. Importer les événements Outlook vers la base locale
        $eventsEndpoint = $calendarId 
            ? "https://graph.microsoft.com/v1.0/me/calendars/{$calendarId}/events"
            : 'https://graph.microsoft.com/v1.0/me/events';
            
        $response = $client->get($eventsEndpoint, [
            'headers' => [
                'Authorization' => 'Bearer ' . $validToken,
                'Accept' => 'application/json',
                'Prefer' => 'outlook.timezone="' . config('app.timezone') . '"',
            ],
            'query' => [
                '$select' => 'subject,start,end,bodyPreview,isAllDay,id,isCancelled',
                '$orderby' => 'start/dateTime',
                '$top' => 50,
            ],
        ]);
        $events = json_decode($response->getBody(), true);

        foreach ($events['value'] as $event) {
            $existing = \App\Models\CalendarEvent::where('provider', 'outlook')
                ->where('external_event_id', $event['id'])
                ->where('user_id', $user->id)
                ->first();

            if (!$existing && (!isset($event['isCancelled']) || !$event['isCancelled'])) {
                $start = $event['start']['dateTime'] ?? null;
                $title = $event['subject'] ?? $event['id'];
                $description = $event['bodyPreview'] ?? '';

                // Keep the exact time as displayed in Outlook Calendar
                $correctedStartTime = null;
                if ($start) {
                    // Parse the datetime and keep the exact time as shown
                    $parsedDate = \Carbon\Carbon::parse($start);
                    $correctedStartTime = $parsedDate->format('Y-m-d H:i:s');
                    
                    Log::info('Outlook event time preservation', [
                        'original_datetime' => $start,
                        'displayed_time' => $parsedDate->format('H:i:s'),
                        'stored_time' => $correctedStartTime,
                        'note' => 'Keeping exact time as displayed in Outlook Calendar'
                    ]);
                }

                \App\Models\CalendarEvent::create([
                    'user_id' => $user->id,
                    'appointment_id' => null,
                    'provider' => 'outlook',
                    'external_event_id' => $event['id'],
                    'sync_status' => 'synced',
                    'last_synced_at' => now(),
                    'start_time' => $correctedStartTime,
                    'description' => $description,
                    'title' => $title,
                ]);
            }
        }

        return response()->json(['success' => true]);

    } catch (\Exception $e) {
        Log::error('Erreur synchronisation Outlook Calendar : ' . $e->getMessage());
        return response()->json(['success' => false, 'error' => $e->getMessage()]);
    }
}

    public function getOutlookCalendars()
    {
        $user = Auth::user();
        $calendarAccount = $user->calendarAccounts()->where('provider', 'outlook')->first();
        if (!$calendarAccount) {
            return response()->json([]);
        }
        try {
            // Use the robust Outlook Calendar controller
            $outlookController = app(\App\Http\Controllers\OutlookCalendarController::class);
            return $outlookController->getOutlookCalendars();
            
        } catch (\Exception $e) {
            Log::error('Erreur récupération calendriers Outlook : ' . $e->getMessage());
            return response()->json([]);
        }
    }

    /**
     * Format appointment date for calendar operations with Morocco timezone
     * This method ensures that appointment times are never converted but are properly
     * formatted with Morocco timezone
     */
    private function formatAppointmentDateForCalendar($appointmentDate)
    {
        // Parse the appointment date assuming it's already in Morocco timezone
        $parsed = \Carbon\Carbon::parse($appointmentDate, 'Africa/Casablanca');
        
        // Ensure the timezone is explicitly set to Morocco timezone
        // This prevents any automatic timezone conversion
        $parsed->setTimezone('Africa/Casablanca');
        
        return $parsed;
    }

    /**
     * Get the application timezone for calendar operations - always Morocco
     */
    private function getAppTimezone()
    {
        return 'Africa/Casablanca'; // Always use Morocco timezone
    }
}

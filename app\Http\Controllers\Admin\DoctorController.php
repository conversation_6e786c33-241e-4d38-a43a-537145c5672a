<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\Doctor\CreateDoctorRequest;
use App\Http\Requests\Doctor\UpdateDoctorRequest;
use App\Repositories\SpecialityRepositoryInterface;
use App\Repositories\DoctorToCentreMedicalRepositoryInterface;
use App\Repositories\SubSpecialityRepositoryInterface;
use App\Repositories\DocClinicTimeRepositoryInterface;
use App\Repositories\EducationRepositoryInterface;
use App\Repositories\ExperienceRepositoryInterface;
use App\Services\DoctorService\IDoctorService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Mail\SendDoctorPdf;
use App\Models\SubSpeciality;
use App\Models\Doctor;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Mail;
use App\Models\CentreMedicalDoctor;
use Barryvdh\DomPDF\Facade\Pdf;


class DoctorController extends Controller
{

    protected $doctorService;
    protected $specialityRepository;
    protected $subSpecialityRepository;
    protected $doctorToCentreMedicalRepository;
    protected $DocClinicTimeRepository;
    protected $educationRepository;
    protected $experienceRepository;

    public function __construct(
        IDoctorService $doctorService,
        SpecialityRepositoryInterface $specialityRepository,
        SubSpecialityRepositoryInterface $subSpecialityRepository,
        DoctorToCentreMedicalRepositoryInterface $doctorToCentreMedicalRepository,
        DocClinicTimeRepositoryInterface $DocClinicTimeRepository,
        EducationRepositoryInterface $educationRepository,
        ExperienceRepositoryInterface $experienceRepository
    ) {
        $this->doctorService = $doctorService;
        $this->specialityRepository = $specialityRepository;
        $this->subSpecialityRepository = $subSpecialityRepository;
        $this->doctorToCentreMedicalRepository = $doctorToCentreMedicalRepository;
        $this->DocClinicTimeRepository = $DocClinicTimeRepository;
        $this->educationRepository = $educationRepository;
        $this->experienceRepository = $experienceRepository;
    }

    public function index()
    {
        $doctors = $this->doctorService->all();
        $specialities = $this->specialityRepository->all();
        $subspecialities = $this->subSpecialityRepository->all();

        return view('pages.admin.doctors.index', compact('doctors', 'specialities', 'subspecialities'));
    }
protected function store_education_and_experience($request, $userId) {
    Log::info("Starting store_education_and_experience for user: $userId");

    if ($request->has('degree')) {
        $degrees = $request->input('degree');

        Log::info("Education data received", [
            'degrees' => $degrees,
            'count' => count($degrees)
        ]);

        foreach ($request->input('degree') as $index => $degree) {

            if (empty($degree)) {
                Log::warning("Skipping education entry due to missing degree", [
                    'index' => $index,
                    'degree' => $degree
                ]);
                continue;
            }

            Log::info("Processing education with defaults", [
                'index' => $index,
                'degree' => $degree,
            ]);

            $educationData = [
                'user_id' => $userId,
                'degree' => $degree,
            ];

            try {
                if (!$this->doctorService->findEducation($userId, $educationData)) {
                    $this->doctorService->addEducation($educationData);
                    Log::info("Education entry added successfully");
                } else {
                    Log::info("Education entry already exists, skipped");
                }
            } catch (\Exception $e) {
                Log::error("Failed to add education entry", [
                    'error' => $e->getMessage(),
                    'data' => $educationData
                ]);
            }
        }
    } else {
        Log::info("No education data found in request");
    }

    if ($request->has('hospital_name')) {
        Log::info("Processing experience data");
        foreach ($request->input('hospital_name') as $index => $hospitalName) {
            if (empty($hospitalName)) {
                Log::warning("Skipping empty hospital name at index $index");
                continue;
            }

            $experienceData = [
                'user_id' => $userId,
                'hospital_name' => $hospitalName,

            ];

            Log::info("Adding experience entry", [
                'index' => $index,
                'data' => $experienceData
            ]);

            try {
                if (!$this->doctorService->findExperience($userId, $experienceData)) {
                    $this->doctorService->addExperience($experienceData);
                    Log::info("Experience entry added successfully");
                } else {
                    Log::info("Experience entry already exists, skipped");
                }
            } catch (\Exception $e) {
                Log::error("Failed to add experience entry", [
                    'error' => $e->getMessage(),
                    'data' => $experienceData
                ]);
            }
        }
    } else {
        Log::info("No experience data found in request");
    }

}
public function redirectOldDoctor($old_speciality, $old_city, $old_full_name)
{
    try {
        // Convert special characters and decode URL parts
        $new_speciality = $this->getNewSpecialitySlug($old_speciality);
        $new_city = Str::slug(Str::ascii(urldecode($old_city)));
        $new_full_name = Str::slug(Str::ascii(urldecode($old_full_name)));

        // Clean up slugs
        $new_speciality = trim($new_speciality, '-');
        $new_city = trim($new_city, '-');
        $new_full_name = trim($new_full_name, '-');

        // Redirect fallback
        if (empty($new_speciality) || empty($new_city) || empty($new_full_name)) {
            return redirect('/medecin', 301);
        }

        // Try to find the doctor to get the actual city from database
        $doctor = $this->findDoctorByNameAndCity($new_full_name, $new_city);

        if ($doctor && $doctor->address && $doctor->address->city) {
            $city_slug = Str::slug(Str::ascii($doctor->address->city));
            $new_url = url("/profil-medecin/{$new_speciality}/{$city_slug}/{$new_full_name}");
        } else {
            // Fallback: use the provided city (for backward compatibility)
            $new_url = url("/profil-medecin/{$new_speciality}/{$new_city}/{$new_full_name}");
        }

        return redirect($new_url, 301);

    } catch (\Exception $e) {
        Log::error('Error redirecting old doctor URL', [
            'old_speciality' => $old_speciality,
            'old_city' => $old_city,
            'old_full_name' => $old_full_name,
            'error' => $e->getMessage()
        ]);

        return redirect('/medecin', 301);
    }
}

/**
 * Helper method to find doctor by name and city
 */
private function findDoctorByNameAndCity($full_name, $city)
{
    try {
        // Convert slug back to searchable format
        $name_parts = explode('-', $full_name);
        $first_name = ucfirst($name_parts[0] ?? '');
        $last_name = ucfirst(implode(' ', array_slice($name_parts, 1)));

        $doctor = Doctor::with(['user', 'address'])
            ->whereHas('user', function($query) use ($first_name, $last_name) {
                $query->where('first_name', 'like', "%{$first_name}%")
                      ->where('last_name', 'like', "%{$last_name}%");
            })
            ->whereHas('address', function($query) use ($city) {
                $city_name = str_replace('-', ' ', $city);
                $query->where('city', 'like', "%{$city_name}%");
            })
            ->first();

        return $doctor;
    } catch (\Exception $e) {
        Log::error('Error finding doctor', ['error' => $e->getMessage()]);
        return null;
    }
}

    private function getNewSpecialitySlug($old_speciality)
    {
        $decoded = urldecode(str_replace('+', ' ', $old_speciality));
        $subspeciality = SubSpeciality::where('name', $decoded)->first();
        if (!$subspeciality) {
            $subspeciality = SubSpeciality::whereRaw("LOWER(REPLACE(name, 'é', 'e')) = ?", [Str::lower(Str::ascii($decoded))])->first();
        }
        if ($subspeciality) {
            return Str::slug(__('subspecialities.' . $subspeciality->name, [], 'fr'));
        }
        return Str::slug(Str::ascii($decoded));
    }
 public function store(CreateDoctorRequest $request)
    {
        try {
            // Debug request data
            Log::info("Creating doctor with data", [
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'has_education' => $request->has('degree'),
                'has_experience' => $request->has('hospital_name'),
                'education_count' => $request->has('degree') ? count($request->input('degree')) : 0,
                'experience_count' => $request->has('hospital_name') ? count($request->input('hospital_name')) : 0,
                'degree_values' => $request->input('degree', []),
                'hospital_name_values' => $request->input('hospital_name', []),
                'has_translations_en' => $request->has('education_en') || $request->has('experience_en'),
                'has_translations_ar' => $request->has('education_ar') || $request->has('experience_ar'),
                'request_keys' => array_keys($request->all())
            ]);

            // Create the doctor
            $result = $this->doctorService->create($request->validated());

            Log::info("Doctor creation result", ['result' => $result]);

            // Get the doctor ID and user ID from the result
            $doctorId = null;
            $userId = null;

            if (is_array($result)) {
                if (isset($result['doctor']) && isset($result['doctor']->id)) {
                    $doctorId = $result['doctor']->id;
                    $userId = $result['user_id'] ?? $result['doctor']->user_id;
                    Log::info("Retrieved IDs from result array", ['doctorId' => $doctorId, 'userId' => $userId]);
                } elseif (isset($result['user_id'])) {
                    $userId = $result['user_id'];
                    Log::info("Retrieved user ID from result array", ['userId' => $userId]);
                }
            } elseif (is_object($result)) {
                if (isset($result->id)) {
                    $doctorId = $result->id;
                    $userId = $result->user_id ?? null;
                    Log::info("Retrieved IDs from result object", ['doctorId' => $doctorId, 'userId' => $userId]);
                }
            }

            // If we don't have both IDs, try to get them from the email
            if (!$doctorId || !$userId) {
                if ($request->filled('email')) {
                    $userByEmail = \App\Models\User::where('email', $request->email)->first();
                    if ($userByEmail) {
                        $userId = $userByEmail->id;
                        Log::info("Retrieved user ID by email lookup", ['userId' => $userId, 'email' => $request->email]);

                        // Try to get doctor by user_id
                        try {
                            $doctorByUserId = \App\Models\Doctor::where('user_id', $userId)->first();
                            if ($doctorByUserId) {
                                $doctorId = $doctorByUserId->id;
                                Log::info("Retrieved doctor ID by user_id lookup", ['doctorId' => $doctorId]);
                            }
                        } catch (\Exception $e) {
                            Log::error("Failed to get doctor by user_id", ['userId' => $userId, 'error' => $e->getMessage()]);
                        }
                    }
                }
            }

            if (!$userId) {
                Log::error("Could not determine user ID from doctor creation", ['result' => $result]);
                return redirect()->route('doctor.index')->with([
                    'error' => trans("alerts.error.doctor_creation_failed")
                ]);
            }

            // Call store_education_and_experience with the properly determined userId
            $this->store_education_and_experience($request, $userId); // false = create mode

            // Get the doctor using the correct method
            Log::info("Attempting to retrieve doctor", ['doctorId' => $doctorId, 'userId' => $userId]);

            $doctor = null;
            if ($doctorId) {
                // If we have the doctor ID, use it directly
                try {
                    $doctor = $this->doctorService->get($doctorId);
                    // Ensure address relationship is loaded
                    if ($doctor && !$doctor->relationLoaded('address')) {
                        $doctor->load('address');
                    }
                    Log::info("Doctor found by doctorId", [
                        'doctorId' => $doctorId,
                        'hasAddress' => $doctor && $doctor->address ? 'yes' : 'no',
                        'addressData' => $doctor && $doctor->address ? [
                            'address' => $doctor->address->address,
                            'office_name' => $doctor->address->office_name
                        ] : 'null'
                    ]);
                } catch (\Exception $e) {
                    Log::warning("Failed to get doctor by doctorId, trying by userId", ['doctorId' => $doctorId, 'error' => $e->getMessage()]);
                }
            }

            if (!$doctor && $userId) {
                // Fallback to getting doctor by user ID
                try {
                    $doctor = $this->doctorService->getByUserId($userId);
                    // Ensure address relationship is loaded
                    if ($doctor && !$doctor->relationLoaded('address')) {
                        $doctor->load('address');
                    }
                    Log::info("Doctor found by userId", [
                        'userId' => $userId,
                        'hasAddress' => $doctor && $doctor->address ? 'yes' : 'no',
                        'addressData' => $doctor && $doctor->address ? [
                            'address' => $doctor->address->address,
                            'office_name' => $doctor->address->office_name
                        ] : 'null'
                    ]);
                } catch (\Exception $e) {
                    Log::error("Failed to get doctor by userId", ['userId' => $userId, 'error' => $e->getMessage()]);
                }
            }

            Log::info("Doctor retrieval result", [
                'doctor' => $doctor ? 'found' : 'not found',
                'doctorId' => $doctor ? $doctor->id : null,
                'userId' => $userId
            ]);

            if (!$doctor) {
                Log::error("Failed to retrieve created doctor", ['userId' => $userId]);

                // Try to continue with translations anyway if we have translation data
                $hasTranslations = $request->has('education_en') || $request->has('experience_en') ||
                                  $request->has('education_ar') || $request->has('experience_ar') ||
                                  $request->has('first_name_en') || $request->has('first_name_ar') ||
                                  $request->has('services_en') || $request->has('services_ar');

                if ($hasTranslations) {
                    Log::info("Doctor not found but translations exist, attempting to save translations anyway");

                    // Create a minimal doctor object for translation purposes
                    $fakeDoctor = (object) [
                        'id' => $userId,
                        'user_id' => $userId,
                        'user' => (object) [
                            'id' => $userId,
                            'first_name' => $request->first_name,
                            'last_name' => $request->last_name,
                        ],
                        'address' => (object) [
                            'address' => $request->address ?? null,
                            'office_name' => $request->office_name ?? null,
                        ],
                        'services' => $request->services ?? null,
                    ];

                    try {
                        $this->storeTranslations($request, $fakeDoctor);
                        Log::info("Translations stored successfully with fake doctor object");
                    } catch (\Exception $e) {
                        Log::error("Error storing translations with fake doctor", [
                            'exception' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]);
                    }
                }

                return redirect()->route('doctor.index')->with([
                    'success' => trans("alerts.success.doctor_added_successfully")
                ]);
            }

            Log::info("Doctor found successfully", [
                'doctorId' => $doctor->id,
                'userId' => $doctor->user_id,
                'firstName' => $doctor->user->first_name ?? 'N/A',
                'lastName' => $doctor->user->last_name ?? 'N/A'
            ]);

            // Check for translations in request - log all translation fields
            Log::info("Checking for translations in request", [
                'all_request_keys' => array_keys($request->all()),
                'education_en' => $request->has('education_en'),
                'experience_en' => $request->has('experience_en'),
                'education_ar' => $request->has('education_ar'),
                'experience_ar' => $request->has('experience_ar'),
                'first_name_en' => $request->has('first_name_en'),
                'first_name_ar' => $request->has('first_name_ar'),
                'services_en' => $request->has('services_en'),
                'services_ar' => $request->has('services_ar')
            ]);

            $hasTranslations = $request->has('education_en') || $request->has('experience_en') ||
                              $request->has('education_ar') || $request->has('experience_ar') ||
                              $request->has('first_name_en') || $request->has('first_name_ar') ||
                              $request->has('services_en') || $request->has('services_ar');

            Log::info("Translation check result", [
                'hasTranslations' => $hasTranslations,
                'education_en_count' => $request->has('education_en') ? count($request->input('education_en')) : 0,
                'experience_en_count' => $request->has('experience_en') ? count($request->input('experience_en')) : 0,
                'education_ar_count' => $request->has('education_ar') ? count($request->input('education_ar')) : 0,
                'experience_ar_count' => $request->has('experience_ar') ? count($request->input('experience_ar')) : 0,
                'services_en' => $request->has('services_en'),
                'services_ar' => $request->has('services_ar')
            ]);

            // Store translations with better error handling
            try {
                Log::info("About to call storeTranslations method");
                
                // IMPORTANT: Ensure address relationship is loaded before calling storeTranslations
                if ($doctor) {
                    // Force refresh the doctor with address relationship
                    $doctor = $this->doctorService->get($doctor->id);
                    
                    // Explicitly load the address relationship
                    if (!$doctor->relationLoaded('address')) {
                        $doctor->load('address');
                    }
                    
                    // If address still not loaded, try to get it directly
                    if (!$doctor->address) {
                        try {
                            $address = \App\Models\Address::where('user_id', $doctor->user_id)->first();
                            if ($address) {
                                $doctor->setRelation('address', $address);
                                Log::info("Manually loaded address relationship", [
                                    'doctor_id' => $doctor->id,
                                    'address' => $address->address,
                                    'office_name' => $address->office_name
                                ]);
                            }
                        } catch (\Exception $e) {
                            Log::warning("Failed to manually load address", ['error' => $e->getMessage()]);
                        }
                    }

                    Log::info("Final doctor state before translations", [
                        'doctor_id' => $doctor->id,
                        'has_address_relation' => $doctor->relationLoaded('address'),
                        'address_object' => $doctor->address,
                        'address_value' => $doctor->address ? $doctor->address->address : 'null',
                        'office_name_value' => $doctor->address ? $doctor->address->office_name : 'null'
                    ]);
                }
                
                $this->storeTranslations($request, $doctor);
                Log::info("Translations stored successfully for doctor ID: {$doctor->id}");

                // Double-check if translation files exist
                $enFilePath = lang_path('en/education.php');
                $arFilePath = lang_path('ar/education.php');
                Log::info("Translation file check after storing", [
                    'enFileExists' => file_exists($enFilePath),
                    'arFileExists' => file_exists($arFilePath),
                    'enFileSize' => file_exists($enFilePath) ? filesize($enFilePath) : 0,
                    'arFileSize' => file_exists($arFilePath) ? filesize($arFilePath) : 0
                ]);
            } catch (\Exception $e) {
                Log::error("Error storing translations", [
                    'doctorId' => $doctor->id,
                    'exception' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                // Don't fail the whole request if just translations fail
            }

        } catch (\Throwable $th) {
            Log::error("Error creating doctor", [
                'exception' => $th->getMessage(),
                'trace' => $th->getTraceAsString(),
                'line' => $th->getLine(),
                'file' => $th->getFile()
            ]);

            // Even if there's an error, try to save translations if they exist
            try {
                $hasTranslations = $request->has('education_en') || $request->has('experience_en') ||
                                  $request->has('education_ar') || $request->has('experience_ar') ||
                                  $request->has('first_name_en') || $request->has('first_name_ar') ||
                                  $request->has('services_en') || $request->has('services_ar');

                if ($hasTranslations) {
                    Log::info("Exception occurred but translations exist, attempting emergency save");

                    // Create a minimal fake doctor object
                    $fakeDoctor = (object) [
                        'id' => 999999, // fake ID
                        'user_id' => 999999,
                        'user' => (object) [
                            'id' => 999999,
                            'first_name' => $request->first_name ?? 'Unknown',
                            'last_name' => $request->last_name ?? 'Unknown',
                        ],
                        'address' => (object) [
                            'address' => $request->address ?? null,
                            'office_name' => $request->office_name ?? null,
                        ],
                        'services' => $request->services ?? null,
                    ];

                    $this->storeTranslations($request, $fakeDoctor);
                    Log::info("Emergency translation save completed");
                }
            } catch (\Exception $e) {
                Log::error("Emergency translation save failed", [
                    'exception' => $e->getMessage()
                ]);
            }

            return redirect()->route('doctor.index')->with([
                'error' => trans("alerts.error.something_went_wrong")
            ]);
        }

        return redirect()->route('doctor.index')->with([
            'success' => trans("alerts.success.doctor_added_successfully")
        ]);
    }

    public function show($speciality, $city, $full_name)
     {

        $translatedSpeciality = __('specialities.' . $speciality, [], 'fr');
        $clinic_id= request()->query('lieu1-id')??null;




        $doctor = $this->doctorService->getByFullName($full_name);


        if (!$doctor) return abort(404);

        if ($doctor->user->account_status == 0) return view(
            'pages.user-accounts.doctor.profile.profile-blocked',
            ['doctor' => $doctor]
        );
        $center_medical = null;

        if ($clinic_id) {
            // Try with a more explicit join
            $center_medical = $doctor->medicalCenters()
                ->where('centre_medical_doctor.centre_medical_id', $clinic_id)
                ->first();

            // If that doesn't work, add debugging
            if (!$center_medical) {
                Log::info('Medical center query info', [
                    'doctor_id' => $doctor->id,
                    'clinic_id' => $clinic_id,
                    'doctor_has_centers' => $doctor->medicalCenters()->count() > 0,
                    'all_centers' => $doctor->medicalCenters()->pluck('centre_medical_id')->toArray()
                ]);
            }
        }


        $can_review = Auth::check() && Auth::user()->patient && $doctor->appointments()->where('patient_id', Auth::user()->patient->id)->where('status', 'completed')->exists();
        if (is_null($can_review)) {
            $can_review = false;
        }

        $already_reviewed = Auth::check() && Auth::user()->patient && auth()->user()->patient->reviews()->where('doctor_id', $doctor->id)->exists();

        $holidaysdate = [];
        $holidays = [];
        foreach ($doctor->holidays as $holiday) {
            $holidaysdate[] = $holiday['date_holiday'];
            $holidays[] = $holiday;
        }
        $holidaysdate = array_unique($holidaysdate);
        $holidays = array_unique($holidays);
        $holidaysdate = array_unique($holidaysdate);

        $center_medicals = $this->doctorToCentreMedicalRepository->getfordoctor($doctor->id);


        // return view(
        //     'pages.user-accounts.doctor.profile.profile',
        //     ['doctor' => $doctor, 'can_review' => $can_review, 'holidays' => $holidays, 'holidaysdate' => $holidaysdate]
        // );


        return view('pages.user-accounts.doctor.profile.profile', [
            'doctor' => $doctor,
            'can_review' => $can_review,
            'holidays' => $holidays,
            'holidaysdate' => $holidaysdate,
            'center_medicals' => $center_medicals,
            'speciality' => $speciality,
            'city' => $city,
            'full_name' => $full_name,
            'selected_clinic' => $center_medical,
            'docclinictime' => $docclinictime ?? null
        ]);
    }

    //   public function edit(Request $request)
    // {
    //     $doctor = $this->doctorService->get($request->id);

    //     return response()->json($doctor); // experiences will now be included in the response
    // }

    public function update(UpdateDoctorRequest $request, $doctorId)
    {
        try {
            // Debug request data
            Log::info("Updating doctor with data", [
                'doctorId' => $doctorId,
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'has_education' => $request->has('degree'),
                'has_experience' => $request->has('hospital_name'),
                'has_translations_en' => $request->has('education_en') || $request->has('experience_en'),
                'has_translations_ar' => $request->has('education_ar') || $request->has('experience_ar')
            ]);

            $doctor = $this->doctorService->get($doctorId);
            
            if (!$doctor) {
                Log::error("Doctor not found for update", ['doctorId' => $doctorId]);
                return redirect()->route('doctor.index')->with([
                    'error' => trans("alerts.error.doctor_not_found")
                ]);
            }

            // Ensure address relationship is loaded
            if (!$doctor->relationLoaded('address')) {
                $doctor->load('address');
            }

            $userId = $doctor->user_id; // Get the user ID from the doctor object
            $this->store_education_and_experience($request, $userId);

            Log::info("Doctor found for update", [
                'doctorId' => $doctor->id,
                'userId' => $doctor->user_id,
                'firstName' => $doctor->user->first_name ?? 'N/A',
                'lastName' => $doctor->user->last_name ?? 'N/A',
                'hasAddress' => $doctor && $doctor->address ? 'yes' : 'no',
                'addressData' => $doctor && $doctor->address ? [
                    'address' => $doctor->address->address,
                    'office_name' => $doctor->address->office_name
                ] : 'null'
            ]);

            // Update the doctor
            $this->doctorService->update($request->validated(), $userId);

            if ($request->has('services')) {
                $this->doctorService->updateServices($request->services, $doctorId);
            }

            // Refresh the doctor object to get the latest data including address
            $doctor->refresh();
            if (!$doctor->relationLoaded('address')) {
                $doctor->load('address');
            }

            Log::info("Doctor refreshed after update", [
                'hasAddress' => $doctor && $doctor->address ? 'yes' : 'no',
                'updatedAddressData' => $doctor && $doctor->address ? [
                    'address' => $doctor->address->address,
                    'office_name' => $doctor->address->office_name
                ] : 'null'
            ]);

            $userId = $doctor->user_id; // Get the actual user ID from the doctor object

            // // Ensure education and experience are stored with logging
             Log::info("About to update education and experience for doctor", ['doctorId' => $doctorId, 'userId' => $userId]);
    // true = update mode

            // Check for translations in request - log all translation fields
            Log::info("Checking for translations in update request", [
                'all_request_keys' => array_keys($request->all()),
                'request_data_sample' => [
                    'first_name_en' => $request->input('first_name_en'),
                    'first_name_ar' => $request->input('first_name_ar'),
                    'address_en' => $request->input('address_en'), 
                    'address_ar' => $request->input('address_ar'),
                    'office_name_en' => $request->input('office_name_en'),
                    'office_name_ar' => $request->input('office_name_ar'),
                    'services_en' => $request->input('services_en'),
                    'services_ar' => $request->input('services_ar'),
                    'degree_en' => $request->input('degree_en', []),
                    'degree_ar' => $request->input('degree_ar', []),
                    'hospital_name_en' => $request->input('hospital_name_en', []),
                    'hospital_name_ar' => $request->input('hospital_name_ar', []),
                    'education_keys' => $request->input('education_keys', []),
                    'experience_keys' => $request->input('experience_keys', [])
                ],
                'education_en' => $request->has('education_en'),
                'experience_en' => $request->has('experience_en'),
                'education_ar' => $request->has('education_ar'),
                'experience_ar' => $request->has('experience_ar'),
                'degree_en' => $request->has('degree_en'),
                'degree_ar' => $request->has('degree_ar'),
                'hospital_name_en' => $request->has('hospital_name_en'),
                'hospital_name_ar' => $request->has('hospital_name_ar'),
                'first_name_en' => $request->has('first_name_en'),
                'first_name_ar' => $request->has('first_name_ar'),
                'services_en' => $request->has('services_en'),
                'services_ar' => $request->has('services_ar'),
                'address_en' => $request->has('address_en'),
                'address_ar' => $request->has('address_ar'),
                'office_name_en' => $request->has('office_name_en'),
                'office_name_ar' => $request->has('office_name_ar')
            ]);

            $hasTranslations = $request->has('education_en') || $request->has('experience_en') ||
                              $request->has('education_ar') || $request->has('experience_ar') ||
                              $request->has('degree_en') || $request->has('degree_ar') ||
                              $request->has('hospital_name_en') || $request->has('hospital_name_ar') ||
                              $request->has('first_name_en') || $request->has('first_name_ar') ||
                              $request->has('services_en') || $request->has('services_ar') ||
                              $request->has('address_en') || $request->has('address_ar') ||
                              $request->has('office_name_en') || $request->has('office_name_ar');

            Log::info("Translation check result for update", [
                'hasTranslations' => $hasTranslations,
                'education_en_count' => $request->has('education_en') ? count($request->input('education_en')) : 0,
                'experience_en_count' => $request->has('experience_en') ? count($request->input('experience_en')) : 0,
                'education_ar_count' => $request->has('education_ar') ? count($request->input('education_ar')) : 0,
                'experience_ar_count' => $request->has('experience_ar') ? count($request->input('experience_ar')) : 0,
                'degree_en_count' => $request->has('degree_en') ? count($request->input('degree_en')) : 0,
                'degree_ar_count' => $request->has('degree_ar') ? count($request->input('degree_ar')) : 0,
                'hospital_name_en_count' => $request->has('hospital_name_en') ? count($request->input('hospital_name_en')) : 0,
                'hospital_name_ar_count' => $request->has('hospital_name_ar') ? count($request->input('hospital_name_ar')) : 0,
                'services_en' => $request->has('services_en'),
                'services_ar' => $request->has('services_ar'),
                'address_en' => $request->has('address_en'),
                'address_ar' => $request->has('address_ar'),
                'office_name_en' => $request->has('office_name_en'),
                'office_name_ar' => $request->has('office_name_ar')
            ]);

            // Store translations with better error handling
            try {
                Log::info("About to call storeTranslations method for update");
                
                // IMPORTANT: Ensure address relationship is loaded before calling storeTranslations
                if ($doctor) {
                    // Force refresh the doctor with address relationship
                    $doctor = $this->doctorService->get($doctor->id);
                    
                    // Explicitly load the address relationship
                    if (!$doctor->relationLoaded('address')) {
                        $doctor->load('address');
                    }
                    
                    // If address still not loaded, try to get it directly with retry mechanism
                    if (!$doctor->address) {
                        $maxRetries = 3;
                        $retry = 0;
                        
                        while ($retry < $maxRetries) {
                            try {
                                $address = \App\Models\Address::where('user_id', $doctor->user_id)
                                    ->orderBy('updated_at', 'desc')
                                    ->first();
                                    
                                if ($address) {
                                    $doctor->setRelation('address', $address);
                                    Log::info("Manually loaded address relationship for update", [
                                        'doctor_id' => $doctor->id,
                                        'address' => $address->address,
                                        'office_name' => $address->office_name,
                                        'retry' => $retry
                                    ]);
                                    break;
                                }
                            } catch (\Exception $e) {
                                Log::warning("Failed to manually load address relationship", [
                                    'error' => $e->getMessage(),
                                    'retry' => $retry
                                ]);
                            }
                            
                            $retry++;
                            if ($retry < $maxRetries) {
                                usleep(100000); // 100ms
                            }
                        }
                    }

                    Log::info("Final doctor state before translations (update)", [
                        'doctor_id' => $doctor->id,
                        'has_address_relation' => $doctor->relationLoaded('address'),
                        'address_object' => $doctor->address,
                        'address_value' => $doctor->address ? $doctor->address->address : 'null',
                        'office_name_value' => $doctor->address ? $doctor->address->office_name : 'null'
                    ]);
                }
                
                // Force call to storeTranslations regardless of $hasTranslations check
                $this->storeTranslations($request, $doctor);
                Log::info("Translations stored successfully for doctor update ID: {$doctor->id}");

                // Double-check if translation files exist
                $enFilePath = lang_path('en/education.php');
                $arFilePath = lang_path('ar/education.php');
                Log::info("Translation file check after updating", [
                    'enFileExists' => file_exists($enFilePath),
                    'arFileExists' => file_exists($arFilePath),
                    'enFileSize' => file_exists($enFilePath) ? filesize($enFilePath) : 0,
                    'arFileSize' => file_exists($arFilePath) ? filesize($arFilePath) : 0
                ]);
            } catch (\Exception $e) {
                Log::error("Error storing translations during update", [
                    'doctorId' => $doctor->id,
                    'exception' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                // Don't fail the whole request if just translations fail
            }

        } catch (\Throwable $th) {
            Log::error("Error updating doctor", [
                'doctorId' => $doctorId ?? 'unknown',
                'exception' => $th->getMessage(),
                'trace' => $th->getTraceAsString(),
                'line' => $th->getLine(),
                'file' => $th->getFile()
            ]);

            // Even if there's an error, try to save translations if they exist
            try {
                $hasTranslations = $request->has('education_en') || $request->has('experience_en') ||
                                  $request->has('education_ar') || $request->has('experience_ar') ||
                                  $request->has('first_name_en') || $request->has('first_name_ar') ||
                                  $request->has('services_en') || $request->has('services_ar');

                if ($hasTranslations) {
                    // Log::info("Update exception occurred but translations exist, attempting emergency save");

                    // Try to get the doctor first, or create a fake one
                    $doctor = null;
                    try {
                        $doctor = $this->doctorService->get($doctorId);
                    } catch (\Exception $e) {
                        // Create a minimal fake doctor object if we can't get the real one
                        $doctor = (object) [
                            'id' => $doctorId ?? 999999,
                            'user_id' => $doctorId ?? 999999,
                            'user' => (object) [
                                'id' => $doctorId ?? 999999,
                                'first_name' => $request->first_name ?? 'Unknown',
                                'last_name' => $request->last_name ?? 'Unknown',
                                'address' => $request->address ?? null,
                            ],
                            'office_name' => $request->office_name ?? null,
                            'services' => $request->services ?? null,
                        ];
                    }

                    if ($doctor) {
                        $this->storeTranslations($request, $doctor);
                        // Log::info("Emergency translation save completed for update");
                    }
                }
            } catch (\Exception $e) {
                Log::error("Emergency translation save failed for update", [
                    'exception' => $e->getMessage()
                ]);
            }

            // return redirect()->route('doctor.index')->with([
            //     'error' => trans("alerts.error.something_went_wrong")
            // ]);
        }

        return redirect()->route('doctor.index')->with([
            'success' => trans("alerts.success.doctor_updated_successfully")
        ]);
    }

    public function block(Request $request)
    {
        $this->doctorService->block($request->id);

        return response()->json(false);
    }

    public function unblock(Request $request)
    {
        $this->doctorService->unblock($request->id);

        return response()->json(true);
    }

    public function feature(Request $request)
    {
        $this->doctorService->feature($request->id);

        return response()->json(true);
    }

    public function unfeature(Request $request)
    {
        $this->doctorService->unfeature($request->id);

        return response()->json(false);
    }


    public function downloadDoctorPdf($id)
    {
        $doctor = $this->doctorService->get($id);
        $translatedSpeciality = __('specialities.' . $doctor->speciality->name);

        $pdf = PDF::loadView('emails.admin.bulletin-dadhesion', compact('doctor', 'translatedSpeciality'));
        return $pdf->download('bulletin-dadhesion-'.$doctor->last_name.'-.pdf');
    }




public function sendPdf(Request $request, $id)
{
    try {
        $doctor = $this->doctorService->get($id);
        $translatedSpeciality = __('specialities.' . $doctor->speciality->name);


        $pdf = PDF::loadView('emails.admin.bulletin-dadhesion', compact('doctor', 'translatedSpeciality'));


        Mail::to($doctor->email)->send(new \App\Mail\SendDoctorPdf($doctor));


        $pdfList = session('pdf_ready', []);
        $pdfList[$doctor->id] = [
            'id' => $doctor->id,
        ];
        session()->put('pdf_ready', $pdfList);

        return back()->with('success', 'PDF envoyé par email');
    } catch (\Exception $e) {
        return back()->with('error', 'Erreur lors de l\'envoi de l\'email : ' . $e->getMessage());
    }
}


public function deleteEducation(Request $request)
{
    try {
        $this->doctorService->deleteEducation($request->id);
        return response()->json(['success' => trans("alerts.success.education_deleted")]);
    } catch (\Exception $e) {
        Log::error('Error deleting education: ' . $e->getMessage());
        return response()->json(['error' => trans("alerts.error.something_went_wrong")], 500);
    }
}

public function deleteExperience(Request $request)
{
    try {
        $this->doctorService->deleteExperience($request->id);
        return response()->json(['success' => trans("alerts.success.experience_deleted")]);
    } catch (\Exception $e) {
        Log::error('Error deleting experience: ' . $e->getMessage());
        return response()->json(['error' => trans("alerts.error.something_went_wrong")], 500);
    }
}

    public function create()
    {
        $specialities = $this->specialityRepository->all();
        $subspecialities = $this->subSpecialityRepository->all();

        return view('pages.admin.doctors.create', compact('specialities', 'subspecialities'));
    }

    public function edit($id)
    {
        $doctor = $this->doctorService->get($id);
        if (!$doctor) {
            return redirect()->route('doctor.index')->with([
                'error' => trans("alerts.error.doctor_not_found")
            ]);
        }

        // Load relationships to ensure all data is available
        $doctor->load(['user', 'speciality', 'subspeciality', 'address', 'education', 'experiences']);

        $specialities = $this->specialityRepository->all();
        $subspecialities = $this->subSpecialityRepository->all();

        // Load existing translations
        $doctorFullName = strtolower($doctor->user->first_name . ' ' . $doctor->user->last_name);

        // Load English translations
        $doctorNamesEn = __('doctor-name', [], 'en');
        $addressesEn = __('address', [], 'en');
        $educationEn = __('education', [], 'en');
        $experienceEn = __('experience', [], 'en');
        $servicesEn = __('services', [], 'en');
        $officeNameEn = __('office-name', [], 'en');

        $translationsEn = [
            'first_name' => '',
            'last_name' => '',
            'address' => $doctor->address && isset($addressesEn[$doctor->address->address]) ? $addressesEn[$doctor->address->address] : '',
            'services' => $this->reconstructCommaSeparatedTranslations($doctor->services, $servicesEn),
            'office_name' => $doctor->address && $doctor->address->office_name && isset($officeNameEn[$doctor->address->office_name]) ? $officeNameEn[$doctor->address->office_name] : ''
        ];

        // Debug office name lookup
        if ($doctor->address && $doctor->address->office_name) {
            Log::info("Office name lookup debugging (EN)", [
                'doctor_office_name' => $doctor->address->office_name,
                'doctor_office_name_length' => strlen($doctor->address->office_name),
                'doctor_office_name_bytes' => bin2hex($doctor->address->office_name),
                'officeNameEn_keys' => array_keys($officeNameEn),
                'officeNameEn_count' => count($officeNameEn),
                'key_exists' => isset($officeNameEn[$doctor->address->office_name]),
                'translation_result' => $translationsEn['office_name']
            ]);
        }

        if (isset($doctorNamesEn[$doctorFullName])) {
            $nameParts = explode(' ', $doctorNamesEn[$doctorFullName], 2);
            $translationsEn['first_name'] = $nameParts[0] ?? '';
            $translationsEn['last_name'] = $nameParts[1] ?? '';
        }

        // Add education and experience translations
        if ($doctor->education) {
            $translationsEn['education'] = [];
            foreach ($doctor->education as $edu) {
                $translationsEn['education'][] = $educationEn[$edu->degree] ?? '';
            }
        }

        if ($doctor->experiences) {
            $translationsEn['experience'] = [];
            foreach ($doctor->experiences as $exp) {
                $translationsEn['experience'][] = $experienceEn[$exp->hospital_name] ?? '';
            }
        }

        // Load Arabic translations
        $doctorNamesAr = __('doctor-name', [], 'ar');
        $addressesAr = __('address', [], 'ar');
        $educationAr = __('education', [], 'ar');
        $experienceAr = __('experience', [], 'ar');
        $servicesAr = __('services', [], 'ar');
        $officeNameAr = __('office-name', [], 'ar');

        $translationsAr = [
            'first_name' => '',
            'last_name' => '',
            'address' => $doctor->address && isset($addressesAr[$doctor->address->address]) ? $addressesAr[$doctor->address->address] : '',
            'services' => $this->reconstructCommaSeparatedTranslations($doctor->services, $servicesAr),
            'office_name' => $doctor->address && $doctor->address->office_name && isset($officeNameAr[$doctor->address->office_name]) ? $officeNameAr[$doctor->address->office_name] : ''
        ];

        // Debug office name lookup
        if ($doctor->address && $doctor->address->office_name) {
            Log::info("Office name lookup debugging (AR)", [
                'doctor_office_name' => $doctor->address->office_name,
                'doctor_office_name_length' => strlen($doctor->address->office_name),
                'doctor_office_name_bytes' => bin2hex($doctor->address->office_name),
                'officeNameAr_keys' => array_keys($officeNameAr),
                'officeNameAr_count' => count($officeNameAr),
                'key_exists' => isset($officeNameAr[$doctor->address->office_name]),
                'translation_result' => $translationsAr['office_name']
            ]);
        }

        if (isset($doctorNamesAr[$doctorFullName])) {
            $nameParts = explode(' ', $doctorNamesAr[$doctorFullName], 2);
            $translationsAr['first_name'] = $nameParts[0] ?? '';
            $translationsAr['last_name'] = $nameParts[1] ?? '';
        }

        // Add education and experience translations
        if ($doctor->education) {
            $translationsAr['education'] = [];
            foreach ($doctor->education as $edu) {
                $translationsAr['education'][] = $educationAr[$edu->degree] ?? '';
            }
        }

        if ($doctor->experiences) {
            $translationsAr['experience'] = [];
            foreach ($doctor->experiences as $exp) {
                $translationsAr['experience'][] = $experienceAr[$exp->hospital_name] ?? '';
            }
        }

        Log::info("Edit form translations loaded", [
            'doctorId' => $doctor->id,
            'translationsEn' => $translationsEn,
            'translationsAr' => $translationsAr,
            'educationCount' => is_array($educationEn) ? count($educationEn) : 0,
            'experienceCount' => is_array($experienceEn) ? count($experienceEn) : 0,
            'doctorEducationCount' => $doctor->education ? $doctor->education->count() : 0,
            'doctorExperienceCount' => $doctor->experiences ? $doctor->experiences->count() : 0
        ]);

        // Log information about services reconstruction for debugging
        Log::info("Edit form - Services reconstruction debugging", [
            'doctorId' => $doctor->id,
            'originalServices' => $doctor->services,
            'servicesEnArray' => $servicesEn,
            'servicesArArray' => $servicesAr,
            'reconstructedEn' => $translationsEn['services'],
            'reconstructedAr' => $translationsAr['services']
        ]);

        return view('pages.admin.doctors.edit', compact('doctor', 'specialities', 'subspecialities', 'translationsEn', 'translationsAr'));
    }

    protected function storeTranslations($request, $doctor)
    {
        Log::info("=== STARTING STORE TRANSLATIONS ===");

        // Log ALL request data to debug field names and values
        Log::info("Full request data for translation debugging", [
            'allRequestData' => $request->all(),
            'hasAnyEducationField' => $request->has('degree_en') || $request->has('education_en'),
            'hasAnyExperienceField' => $request->has('hospital_name_en') || $request->has('experience_en'),
            'hasAddressFields' => $request->has('address_en') || $request->has('address_ar'),
            'hasOfficeNameFields' => $request->has('office_name_en') || $request->has('office_name_ar'),
            'addressEnValue' => $request->input('address_en'),
            'addressArValue' => $request->input('address_ar'),
            'officeNameEnValue' => $request->input('office_name_en'),
            'officeNameArValue' => $request->input('office_name_ar'),
            'addressValue' => $request->input('address'),
            'officeNameValue' => $request->input('office_name'),
            'degreeEnData' => $request->input('degree_en', []),
            'degreeArData' => $request->input('degree_ar', []),
            'hospitalNameEnData' => $request->input('hospital_name_en', []),
            'hospitalNameArData' => $request->input('hospital_name_ar', []),
            'educationEnData' => $request->input('education_en', []),
            'educationArData' => $request->input('education_ar', []),
            'experienceEnData' => $request->input('experience_en', []),
            'experienceArData' => $request->input('experience_ar', []),
            'educationKeysData' => $request->input('education_keys', []),
            'experienceKeysData' => $request->input('experience_keys', [])
        ]);

        // Log the doctor object and request data to debug
        Log::info("Storing translations for doctor", [
            'doctorId' => $doctor->id ?? null,
            'userId' => $doctor->user_id ?? null,
            'hasUser' => isset($doctor->user),
            'hasAddress' => isset($doctor->address),
            'firstName' => $doctor->user->first_name ?? 'N/A',
            'lastName' => $doctor->user->last_name ?? 'N/A',
            'doctorAddressObject' => $doctor->address ?? 'null',
            'addressFromDoctor' => isset($doctor->address) && $doctor->address ? $doctor->address->address : 'null',
            'officeNameFromDoctor' => isset($doctor->address) && $doctor->address ? $doctor->address->office_name : 'null',
            'requestHasEnEducation' => $request->has('education_en'),
            'requestHasArEducation' => $request->has('education_ar'),
            'requestHasEnExperience' => $request->has('experience_en'),
            'requestHasArExperience' => $request->has('experience_ar'),
            'education_keys' => $request->has('education_keys') ? count($request->education_keys) : 0,
            'experience_keys' => $request->has('experience_keys') ? count($request->experience_keys) : 0,
        ]);

        // Ensure language directories exist and are writable
        $this->ensureLanguageDirectoryExists('en');
        $this->ensureLanguageDirectoryExists('ar');
        $this->ensureLanguageDirectoryExists('fr');

        // Handle empty doctor object
        if (!isset($doctor->user)) {
            Log::error("Doctor user relation is not available, aborting translations");
            return;
        }

        $doctorFullName = strtolower($doctor->user->first_name . ' ' . $doctor->user->last_name);

        // Handle English translations
        if ($request->filled('first_name_en') || $request->filled('last_name_en')) {
            $fullNameEn = trim(($request->first_name_en ?? '') . ' ' . ($request->last_name_en ?? ''));
            if ($fullNameEn) {
                $this->updateLanguageFile('en', 'doctor-name', $doctorFullName, $fullNameEn);
            }
        }

        // Handle address translation (EN) - IMPROVED VERSION
        if ($request->filled('address_en')) {
            $address = null;
            
            // Try multiple methods to get the address with better error handling
            if (isset($doctor->address) && $doctor->address && !empty($doctor->address->address)) {
                $address = $doctor->address->address;
                Log::info("Got address from doctor->address relationship", ['address' => $address]);
            } elseif ($request->filled('address')) {
                $address = $request->input('address');
                Log::info("Got address from request", ['address' => $address]);
            } else {
                // Try to get address from database directly with retry mechanism
                $maxRetries = 3;
                $retry = 0;
                
                while ($retry < $maxRetries && !$address) {
                    try {
                        $doctorAddress = \App\Models\Address::where('user_id', $doctor->user_id)
                            ->orderBy('updated_at', 'desc')
                            ->first();
                            
                        if ($doctorAddress && !empty($doctorAddress->address)) {
                            $address = $doctorAddress->address;
                            Log::info("Got address from database query", [
                                'address' => $address,
                                'retry' => $retry
                            ]);
                            break;
                        }
                    } catch (\Exception $e) {
                        Log::warning("Failed to get address from database", [
                            'error' => $e->getMessage(),
                            'retry' => $retry
                        ]);
                    }
                    
                    $retry++;
                    if ($retry < $maxRetries) {
                        // Wait a bit before retrying (helps with race conditions)
                        usleep(100000); // 100ms
                    }
                }
            }
            
            if ($address) {
                Log::info("Saving address translation (EN)", [
                    'address_key' => $address,
                    'address_translation' => $request->address_en
                ]);
                $this->updateLanguageFile('en', 'address', $address, $request->address_en);
            } else {
                Log::warning("No address found for English translation after all attempts", [
                    'doctor_id' => $doctor->id ?? 'unknown',
                    'user_id' => $doctor->user_id ?? 'unknown'
                ]);
            }
        }

        // Handle office name translation (EN) - IMPROVED VERSION
        if ($request->filled('office_name_en')) {
            $officeName = null;
            
            // Try multiple methods to get the office name with better error handling
            if (isset($doctor->address) && $doctor->address && !empty($doctor->address->office_name)) {
                $officeName = $doctor->address->office_name;
                Log::info("Got office name from doctor->address relationship", ['office_name' => $officeName]);
            } elseif ($request->filled('office_name')) {
                $officeName = $request->input('office_name');
                Log::info("Got office name from request", ['office_name' => $officeName]);
            } else {
                // Try to get office name from database directly with retry mechanism
                $maxRetries = 3;
                $retry = 0;
                
                while ($retry < $maxRetries && !$officeName) {
                    try {
                        $doctorAddress = \App\Models\Address::where('user_id', $doctor->user_id)
                            ->orderBy('updated_at', 'desc')
                            ->first();
                            
                        if ($doctorAddress && !empty($doctorAddress->office_name)) {
                            $officeName = $doctorAddress->office_name;
                            Log::info("Got office name from database query", [
                                'office_name' => $officeName,
                                'retry' => $retry
                            ]);
                            break;
                        }
                    } catch (\Exception $e) {
                        Log::warning("Failed to get office name from database", [
                            'error' => $e->getMessage(),
                            'retry' => $retry
                        ]);
                    }
                    
                    $retry++;
                    if ($retry < $maxRetries) {
                        // Wait a bit before retrying (helps with race conditions)
                        usleep(100000); // 100ms
                    }
                }
            }

            if ($officeName) {
                Log::info("Saving office name translation (EN)", [
                    'office_name_key' => $officeName,
                    'office_name_translation' => $request->office_name_en
                ]);
                $this->updateLanguageFile('en', 'office-name', $officeName, $request->office_name_en);
            } else {
                Log::warning("No office name found for English translation after all attempts", [
                    'doctor_id' => $doctor->id ?? 'unknown',
                    'user_id' => $doctor->user_id ?? 'unknown'
                ]);
            }
        }

        // Handle services translation
        if ($request->has('services_en') && !empty($request->services_en)) {
            $services = $doctor->services ?? $request->input('services');

            Log::info("Services translation debugging (EN)", [
                'doctor_services' => $doctor->services ?? 'NULL',
                'request_services' => $request->input('services'),
                'final_services' => $services,
                'services_en_value' => $request->services_en
            ]);

            if ($services) {
                // Handle comma-separated services
                $originalServices = array_map('trim', explode(',', $services));
                $translatedServices = array_map('trim', explode(',', $request->services_en));

                Log::info("Processing comma-separated services (EN)", [
                    'originalServices' => $originalServices,
                    'translatedServices' => $translatedServices,
                    'originalCount' => count($originalServices),
                    'translatedCount' => count($translatedServices)
                ]);

                // Map each original service to its translation
                foreach ($originalServices as $index => $originalService) {
                    if (!empty($originalService)) {
                        // Get corresponding translation or use empty string if not provided
                        $translation = isset($translatedServices[$index]) ? $translatedServices[$index] : '';

                        if (!empty($translation)) {
                            Log::info("Saving service translation (EN)", [
                                'original' => $originalService,
                                'translation' => $translation
                            ]);
                            $this->updateLanguageFile('en', 'services', $originalService, $translation);
                        } else {
                            Log::warning("Empty translation for service (EN)", [
                                'original' => $originalService,
                                'index' => $index
                            ]);
                        }
                    }
                }
            } else {
                Log::warning("No services found for translation (EN)");
            }
        } else {
            Log::info("services_en not filled or not present in request");
        }

        // Handle education translations
        // Support both new field format (degree_en, degree_ar) and old format (education_en, education_ar)
        $educationTranslationsEn = [];
        $degreeEnInput = $request->input('degree_en', []);
        $educationEnInput = $request->input('education_en', []);

        // Filter out null and empty values from degree_en
        if (!empty($degreeEnInput)) {
            $educationTranslationsEn = array_filter($degreeEnInput, function($value) {
                return !is_null($value) && trim($value) !== '';
            });
        }

        // Fallback to education_en if degree_en is empty
        if (empty($educationTranslationsEn) && !empty($educationEnInput)) {
            $educationTranslationsEn = array_filter($educationEnInput, function($value) {
                return !is_null($value) && trim($value) !== '';
            });
        }

        Log::info("Education translation processing (EN)", [
            'degreeEnInput' => $degreeEnInput,
            'educationEnInput' => $educationEnInput,
            'filteredEducationTranslationsEn' => $educationTranslationsEn,
            'degreeEnInputCount' => count($degreeEnInput),
            'educationTranslationsEnCount' => count($educationTranslationsEn),
            'degreeEnRawValues' => json_encode($degreeEnInput),
            'emptyValues' => array_filter($degreeEnInput, function($v) { return is_null($v) || $v === ''; }),
            'nonEmptyValues' => array_filter($degreeEnInput, function($v) { return !is_null($v) && $v !== ''; })
        ]);

        if (!empty($educationTranslationsEn)) {
            $educationKeys = $request->has('education_keys') ? $request->education_keys : [];

            Log::info("Processing education translations (EN)", [
                'count_translations' => count($educationTranslationsEn),
                'count_keys' => count($educationKeys),
                'translations' => $educationTranslationsEn,
                'keys' => $educationKeys,
                'raw_degrees' => $request->input('degree', []),
                'all_translation_fields' => [
                    'education_en' => $request->input('education_en', []),
                    'education_ar' => $request->input('education_ar', []),
                    'degree_en' => $request->input('degree_en', []),
                    'degree_ar' => $request->input('degree_ar', []),
                    'experience_en' => $request->input('experience_en', []),
                    'experience_ar' => $request->input('experience_ar', []),
                    'hospital_name_en' => $request->input('hospital_name_en', []),
                    'hospital_name_ar' => $request->input('hospital_name_ar', [])
                ]
            ]);

            foreach ($educationTranslationsEn as $index => $translation) {
                if (!empty($translation)) {
                    // Get the corresponding key if available
                    $key = isset($educationKeys[$index]) && !empty($educationKeys[$index])
                        ? $educationKeys[$index]
                        : null;

                    // If no key found, try to find it from the degree array which is the primary required field
                    if ($key === null && isset($request->degree) && is_array($request->degree) && isset($request->degree[$index])) {
                        $key = $request->degree[$index];
                    }

                    // If still no key, use a fallback with index
                    if ($key === null) {
                        $key = "education-$index-" . substr(md5($translation), 0, 8);
                    }

                    // Log what we're trying to save
                    Log::info("Saving education (EN)", [
                        'key' => $key,
                        'value' => $translation,
                        'index' => $index
                    ]);

                    $this->updateLanguageFile('en', 'education', $key, $translation);
                }
            }
        }

        // Handle experience translations
        // Support both new field format (hospital_name_en, hospital_name_ar) and old format (experience_en, experience_ar)
        $experienceTranslationsEn = [];
        $hospitalNameEnInput = $request->input('hospital_name_en', []);
        $experienceEnInput = $request->input('experience_en', []);

        // Filter out null and empty values from hospital_name_en
        if (!empty($hospitalNameEnInput)) {
            $experienceTranslationsEn = array_filter($hospitalNameEnInput, function($value) {
                return !is_null($value) && trim($value) !== '';
            });
        }

        // Fallback to experience_en if hospital_name_en is empty
        if (empty($experienceTranslationsEn) && !empty($experienceEnInput)) {
            $experienceTranslationsEn = array_filter($experienceEnInput, function($value) {
                return !is_null($value) && trim($value) !== '';
            });
        }

        Log::info("Experience translation processing (EN)", [
            'hospitalNameEnInput' => $hospitalNameEnInput,
            'experienceEnInput' => $experienceEnInput,
            'filteredExperienceTranslationsEn' => $experienceTranslationsEn,
            'hospitalNameEnInputCount' => count($hospitalNameEnInput),
            'experienceTranslationsEnCount' => count($experienceTranslationsEn),
            'hospitalNameEnRawValues' => json_encode($hospitalNameEnInput),
            'emptyValues' => array_filter($hospitalNameEnInput, function($v) { return is_null($v) || $v === ''; }),
            'nonEmptyValues' => array_filter($hospitalNameEnInput, function($v) { return !is_null($v) && $v !== ''; })
        ]);

        if (!empty($experienceTranslationsEn)) {
            $experienceKeys = $request->has('experience_keys') ? $request->experience_keys : [];

            Log::info("Processing experience translations (EN)", [
                'count_translations' => count($experienceTranslationsEn),
                'count_keys' => count($experienceKeys),
                'translations' => $experienceTranslationsEn,
                'keys' => $experienceKeys
            ]);

            foreach ($experienceTranslationsEn as $index => $translation) {
                if (!empty($translation)) {
                    // Get the corresponding key if available
                    $key = isset($experienceKeys[$index]) && !empty($experienceKeys[$index])
                        ? $experienceKeys[$index]
                        : null;

                    // If no key found, try to find it from hospital_name which is the primary required field
                    if ($key === null && isset($request->hospital_name) && is_array($request->hospital_name) && isset($request->hospital_name[$index])) {
                        $key = $request->hospital_name[$index];
                    }

                    // If still no key, use a fallback with index
                    if ($key === null) {
                        $key = "experience-$index-" . substr(md5($translation), 0, 8);
                    }

                    // Log what we're trying to save
                    Log::info("Saving experience (EN)", [
                        'key' => $key,
                        'value' => $translation,
                        'index' => $index
                    ]);

                    $this->updateLanguageFile('en', 'experience', $key, $translation);
                }
            }
        }

        // Handle Arabic translations
        if ($request->filled('first_name_ar') || $request->filled('last_name_ar')) {
            $fullNameAr = trim(($request->first_name_ar ?? '') . ' ' . ($request->last_name_ar ?? ''));
            if ($fullNameAr) {
                $this->updateLanguageFile('ar', 'doctor-name', $doctorFullName, $fullNameAr);
            }
        }

        // Handle address translation (AR) - IMPROVED VERSION
        if ($request->filled('address_ar')) {
            $address = null;
            
            // Try multiple methods to get the address with better error handling
            if (isset($doctor->address) && $doctor->address && !empty($doctor->address->address)) {
                $address = $doctor->address->address;
                Log::info("Got address from doctor->address relationship (AR)", ['address' => $address]);
            } elseif ($request->filled('address')) {
                $address = $request->input('address');
                Log::info("Got address from request (AR)", ['address' => $address]);
            } else {
                // Try to get address from database directly with retry mechanism
                $maxRetries = 3;
                $retry = 0;
                
                while ($retry < $maxRetries && !$address) {
                    try {
                        $doctorAddress = \App\Models\Address::where('user_id', $doctor->user_id)
                            ->orderBy('updated_at', 'desc')
                            ->first();
                            
                        if ($doctorAddress && !empty($doctorAddress->address)) {
                            $address = $doctorAddress->address;
                            Log::info("Got address from database query (AR)", [
                                'address' => $address,
                                'retry' => $retry
                            ]);
                            break;
                        }
                    } catch (\Exception $e) {
                        Log::warning("Failed to get address from database (AR)", [
                            'error' => $e->getMessage(),
                            'retry' => $retry
                        ]);
                    }
                    
                    $retry++;
                    if ($retry < $maxRetries) {
                        // Wait a bit before retrying (helps with race conditions)
                        usleep(100000); // 100ms
                    }
                }
            }
            
            if ($address) {
                Log::info("Saving address translation (AR)", [
                    'address_key' => $address,
                    'address_translation' => $request->address_ar
                ]);
                $this->updateLanguageFile('ar', 'address', $address, $request->address_ar);
            } else {
                Log::warning("No address found for Arabic translation after all attempts", [
                    'doctor_id' => $doctor->id ?? 'unknown',
                    'user_id' => $doctor->user_id ?? 'unknown'
                ]);
            }
        }

        // Handle office name translation (AR) - IMPROVED VERSION
        if ($request->filled('office_name_ar')) {
            $officeName = null;
            
            // Try multiple methods to get the office name with better error handling
            if (isset($doctor->address) && $doctor->address && !empty($doctor->address->office_name)) {
                $officeName = $doctor->address->office_name;
                Log::info("Got office name from doctor->address relationship (AR)", ['office_name' => $officeName]);
            } elseif ($request->filled('office_name')) {
                $officeName = $request->input('office_name');
                Log::info("Got office name from request (AR)", ['office_name' => $officeName]);
            } else {
                // Try to get office name from database directly with retry mechanism
                $maxRetries = 3;
                $retry = 0;
                
                while ($retry < $maxRetries && !$officeName) {
                    try {
                        $doctorAddress = \App\Models\Address::where('user_id', $doctor->user_id)
                            ->orderBy('updated_at', 'desc')
                            ->first();
                            
                        if ($doctorAddress && !empty($doctorAddress->office_name)) {
                            $officeName = $doctorAddress->office_name;
                            Log::info("Got office name from database query (AR)", [
                                'office_name' => $officeName,
                                'retry' => $retry
                            ]);
                            break;
                        }
                    } catch (\Exception $e) {
                        Log::warning("Failed to get office name from database (AR)", [
                            'error' => $e->getMessage(),
                            'retry' => $retry
                        ]);
                    }
                    
                    $retry++;
                    if ($retry < $maxRetries) {
                        // Wait a bit before retrying (helps with race conditions)
                        usleep(100000); // 100ms
                    }
                }
            }

            if ($officeName) {
                Log::info("Saving office name translation (AR)", [
                    'office_name_key' => $officeName,
                    'office_name_translation' => $request->office_name_ar
                ]);
                $this->updateLanguageFile('ar', 'office-name', $officeName, $request->office_name_ar);
            } else {
                Log::warning("No office name found for Arabic translation after all attempts", [
                    'doctor_id' => $doctor->id ?? 'unknown',
                    'user_id' => $doctor->user_id ?? 'unknown'
                ]);
            }
        }

        // Handle services translation (Arabic)
        if ($request->has('services_ar') && !empty($request->services_ar)) {
            $services = $doctor->services ?? $request->input('services');

            Log::info("Services translation debugging (AR)", [
                'doctor_services' => $doctor->services ?? 'NULL',
                'request_services' => $request->input('services'),
                'final_services' => $services,
                'services_ar_value' => $request->services_ar
            ]);

            if ($services) {
                // Handle comma-separated services
                $originalServices = array_map('trim', explode(',', $services));
                $translatedServices = array_map('trim', explode(',', $request->services_ar));

                // If only one translation but it contains Arabic commas, split by Arabic comma
                if (count($translatedServices) === 1 && mb_strpos($translatedServices[0], '،') !== false) {
                    $translatedServices = array_map('trim', explode('،', $translatedServices[0]));
                }

                // If still only one translation and multiple originals, repeat translation for all
                if (count($translatedServices) === 1 && count($originalServices) > 1) {
                    $translatedServices = array_fill(0, count($originalServices), $translatedServices[0]);
                }

                Log::info("Processing comma-separated services (AR) - after fix", [
                    'originalServices' => $originalServices,
                    'translatedServices' => $translatedServices,
                    'originalCount' => count($originalServices),
                    'translatedCount' => count($translatedServices)
                ]);

                // Map each original service to its translation
                foreach ($originalServices as $index => $originalService) {
                    if (!empty($originalService)) {
                        // Get corresponding translation or use empty string if not provided
                        $translation = isset($translatedServices[$index]) ? $translatedServices[$index] : '';

                        if (!empty($translation)) {
                            Log::info("Saving service translation (AR)", [
                                'original' => $originalService,
                                'translation' => $translation,
                                'index' => $index,
                                'rawTranslatedServices' => $translatedServices,
                                'totalOriginalServices' => count($originalServices),
                                'totalTranslatedServices' => count($translatedServices)
                            ]);
                            $this->updateLanguageFile('ar', 'services', $originalService, $translation);
                        } else {
                            Log::warning("Empty translation for service (AR)", [
                                'original' => $originalService,
                                'index' => $index
                            ]);
                        }
                    }
                }
            } else {
                Log::warning("No services found for translation (AR)");
            }
        } else {
            Log::info("services_ar not filled or not present in request");
        }

        // Handle education translations (Arabic)
        // Support both new field format (degree_ar) and old format (education_ar)
        $educationTranslationsAr = [];
        $degreeArInput = $request->input('degree_ar', []);
        $educationArInput = $request->input('education_ar', []);

        // Filter out null and empty values from degree_ar
        if (!empty($degreeArInput)) {
            $educationTranslationsAr = array_filter($degreeArInput, function($value) {
                return !is_null($value) && trim($value) !== '';
            });
        }

        // Fallback to education_ar if degree_ar is empty
        if (empty($educationTranslationsAr) && !empty($educationArInput)) {
            $educationTranslationsAr = array_filter($educationArInput, function($value) {
                return !is_null($value) && trim($value) !== '';
            });
        }

        if (!empty($educationTranslationsAr)) {
            $educationKeys = $request->has('education_keys') ? $request->education_keys : [];

            Log::info("Processing education translations (AR)", [
                'count_translations' => count($educationTranslationsAr),
                'count_keys' => count($educationKeys),
                'translations' => $educationTranslationsAr,
                'keys' => $educationKeys
            ]);

            foreach ($educationTranslationsAr as $index => $translation) {
                if (!empty($translation)) {
                    // Get the corresponding key if available
                    $key = isset($educationKeys[$index]) && !empty($educationKeys[$index])
                        ? $educationKeys[$index]
                        : null;

                    // If no key found, try to find it from the degree array which is the primary required field
                    if ($key === null && isset($request->degree) && is_array($request->degree) && isset($request->degree[$index])) {
                        $key = $request->degree[$index];
                    }

                    // If still no key, use a fallback with index
                    if ($key === null) {
                        $key = "education-$index-" . substr(md5($translation), 0, 8);
                    }

                    // Log what we're trying to save
                    Log::info("Saving education (AR)", [
                        'key' => $key,
                        'value' => $translation,
                        'index' => $index
                    ]);

                    $this->updateLanguageFile('ar', 'education', $key, $translation);
                }
            }
        }

        // Handle experience translations (Arabic)
        // Support both new field format (hospital_name_ar) and old format (experience_ar)
        $experienceTranslationsAr = [];
        $hospitalNameArInput = $request->input('hospital_name_ar', []);
        $experienceArInput = $request->input('experience_ar', []);

        // Filter out null and empty values from hospital_name_ar
        if (!empty($hospitalNameArInput)) {
            $experienceTranslationsAr = array_filter($hospitalNameArInput, function($value) {
                return !is_null($value) && trim($value) !== '';
            });
        }

        // Fallback to experience_ar if hospital_name_ar is empty
        if (empty($experienceTranslationsAr) && !empty($experienceArInput)) {
            $experienceTranslationsAr = array_filter($experienceArInput, function($value) {
                return !is_null($value) && trim($value) !== '';
            });
        }

        if (!empty($experienceTranslationsAr)) {
            $experienceKeys = $request->has('experience_keys') ? $request->experience_keys : [];

            Log::info("Processing experience translations (AR)", [
                'count_translations' => count($experienceTranslationsAr),
                'count_keys' => count($experienceKeys),
                'translations' => $experienceTranslationsAr,
                'keys' => $experienceKeys
            ]);

            foreach ($experienceTranslationsAr as $index => $translation) {
                if (!empty($translation)) {
                    // Get the corresponding key if available
                    $key = isset($experienceKeys[$index]) && !empty($experienceKeys[$index])
                        ? $experienceKeys[$index]
                        : null;

                    // If no key found, try to find it from hospital_name which is the primary required field
                    if ($key === null && isset($request->hospital_name) && is_array($request->hospital_name) && isset($request->hospital_name[$index])) {
                        $key = $request->hospital_name[$index];
                    }

                    // If still no key, use a fallback with index
                    if ($key === null) {
                        $key = "experience-$index-" . substr(md5($translation), 0, 8);
                    }

                    // Log what we're trying to save
                    Log::info("Saving experience (AR)", [
                        'key' => $key,
                        'value' => $translation,
                        'index' => $index
                    ]);

                    $this->updateLanguageFile('ar', 'experience', $key, $translation);
                }
            }
        }

        Log::info("Completed storing translations for doctor", [
            'doctorId' => $doctor->id,
            'totalRequestFields' => count($request->all()),
            'translationFieldsProcessed' => [
                'name_en' => $request->filled('first_name_en') || $request->filled('last_name_en'),
                'name_ar' => $request->filled('first_name_ar') || $request->filled('last_name_ar'),
                'address_en' => $request->filled('address_en'),
                'address_ar' => $request->filled('address_ar'),
                'office_en' => $request->filled('office_name_en'),
                'office_ar' => $request->filled('office_name_ar'),
                'services_en' => $request->filled('services_en'),
                'services_ar' => $request->filled('services_ar'),
                'education_en_count' => count(array_filter($request->input('degree_en', []), function($v) { return !empty($v); })),
                'education_ar_count' => count(array_filter($request->input('degree_ar', []), function($v) { return !empty($v); })),
                'experience_en_count' => count(array_filter($request->input('hospital_name_en', []), function($v) { return !empty($v); })),
                'experience_ar_count' => count(array_filter($request->input('hospital_name_ar', []), function($v) { return !empty($v); }))
            ]
        ]);
    }

    protected function updateLanguageFile($locale, $file, $key, $value)
    {
        // Log what we're trying to save
        Log::info("Attempting to save translation", [
            'locale' => $locale,
            'file' => $file,
            'key' => $key,
            'value' => $value
        ]);

        if (empty($value)) {
            Log::warning("Skipping empty translation value", [
                'locale' => $locale,
                'file' => $file,
                'key' => $key
            ]);
            return;
        }

        // If key is empty, generate a fallback key from the value
        if (empty($key)) {
            // Generate a key based on first few words of value (max 5 words)
            $words = explode(' ', $value);
            $keyWords = array_slice($words, 0, 5);
            $key = implode('-', $keyWords);

            Log::info("Generated fallback key from value", [
                'locale' => $locale,
                'file' => $file,
                'originalKey' => null,
                'generatedKey' => $key,
                'value' => $value
            ]);
        }

        try {
            // Ensure the language directory exists and is writable
            if (!$this->ensureLanguageDirectoryExists($locale)) {
                Log::error("Language directory not writable, aborting translation save", [
                    'locale' => $locale,
                    'file' => $file
                ]);
                return false;
            }

            $filePath = lang_path($locale . '/' . $file . '.php');
            Log::info("Working with translation file", ['path' => $filePath]);

            $translations = [];

            // Get existing translations or create empty array
            if (file_exists($filePath)) {
                Log::info("File exists, getting contents", ['path' => $filePath]);
                $fileContents = file_get_contents($filePath);

                if (empty($fileContents)) {
                    Log::info("File exists but is empty, initializing it", ['path' => $filePath]);
                    $defaultContent = "<?php\n\nreturn [\n    // {$file} entries\n];\n";
                    $writeResult = file_put_contents($filePath, $defaultContent);

                    if ($writeResult === false) {
                        Log::error("Failed to write default content to file", ['path' => $filePath]);
                    } else {
                        Log::info("Successfully wrote default content to file", [
                            'path' => $filePath,
                            'bytes' => $writeResult
                        ]);
                    }
                }

                try {
                    $translations = include $filePath;
                    if (!is_array($translations)) {
                        Log::warning("File doesn't return an array, resetting", ['path' => $filePath]);
                        $translations = [];
                    }
                } catch (\Exception $e) {
                    Log::error("Error including language file", [
                        'path' => $filePath,
                        'error' => $e->getMessage()
                    ]);
                    $translations = [];
                }
            } else {
                Log::info("File doesn't exist, creating it", ['path' => $filePath]);
                $defaultContent = "<?php\n\nreturn [\n    // {$file} entries\n];\n";
                $writeResult = file_put_contents($filePath, $defaultContent);

                if ($writeResult === false) {
                    Log::error("Failed to create new file", ['path' => $filePath]);
                } else {
                    Log::info("Successfully created new file", [
                        'path' => $filePath,
                        'bytes' => $writeResult
                    ]);
                }
            }

            // Use the original key exactly as it is from the database
            $cleanKey = $key;

            // Log the original key for debugging
            Log::info("Using original key without any cleaning", [
                'original' => $key,
                'length' => strlen($key),
                'bytes' => bin2hex($key)
            ]);

            // Create a unique key if it's empty
            if (empty($cleanKey)) {
                $cleanKey = 'key_' . md5($value . microtime());
                Log::warning("Generated unique key for empty key", ['generated' => $cleanKey]);
            }

            // Check if translation exists and if it's different from the new value
            if (isset($translations[$cleanKey])) {
                if ($translations[$cleanKey] === $value) {
                    Log::info("Translation already exists with same value, skipping", [
                        'key' => $cleanKey,
                        'existingValue' => $translations[$cleanKey],
                        'newValue' => $value
                    ]);
                    return true; // Don't write to file if no changes needed
                } else {
                    Log::info("Translation exists but value is different, updating", [
                        'key' => $cleanKey,
                        'existingValue' => $translations[$cleanKey],
                        'newValue' => $value
                    ]);
                    $translations[$cleanKey] = $value; // Update existing translation
                }
            } else {
                Log::info("Adding new translation", [
                    'key' => $cleanKey,
                    'value' => $value
                ]);
                $translations[$cleanKey] = $value; // Add new translation
            }

            // Read the existing file content to preserve it exactly as-is
            $existingContent = '';
            if (file_exists($filePath)) {
                $existingContent = file_get_contents($filePath);
            }

            // Check if this key already exists in the original file
            $keyExistsInOriginal = false;
            $existingValueInFile = null;
            if (!empty($existingContent)) {
                // Check if the key exists in the original content (in any quote format)
                $escapedKeyForSearch = preg_quote($cleanKey, '/');
                if (preg_match('/[\'"]' . $escapedKeyForSearch . '[\'"](\s*=>\s*[\'"]([^\'\"]*)[\'"])/', $existingContent, $matches)) {
                    $keyExistsInOriginal = true;
                    $existingValueInFile = isset($matches[2]) ? $matches[2] : '';
                }
            }

            // If key exists in original file and value is the same, don't modify anything
            if ($keyExistsInOriginal && $existingValueInFile === $value) {
                Log::info("Key exists in original file with same value, not modifying", [
                    'key' => $cleanKey,
                    'existingValue' => $existingValueInFile,
                    'newValue' => $value
                ]);
                return true;
            }

            // If key exists but value is different, we need to update the file
            if ($keyExistsInOriginal && $existingValueInFile !== $value) {
                Log::info("Key exists in original file but value is different, updating", [
                    'key' => $cleanKey,
                    'existingValue' => $existingValueInFile,
                    'newValue' => $value
                ]);
                
                // Replace the existing value in the file content
                $pattern = '/([\'"])' . preg_quote($cleanKey, '/') . '([\'"])\s*=>\s*[\'"][^\'\"]*[\'"],?/';
                $replacement = '$1' . $cleanKey . '$2 => ' . var_export($value, true) . ',';
                $content = preg_replace($pattern, $replacement, $existingContent);
                
                if ($content !== $existingContent) {
                    Log::info("Successfully updated existing translation in file", [
                        'key' => $cleanKey,
                        'oldValue' => $existingValueInFile,
                        'newValue' => $value
                    ]);
                } else {
                    Log::warning("Failed to update existing translation in file, will append new entry", [
                        'key' => $cleanKey
                    ]);
                    // Fallback to appending new entry
                    $keyExistsInOriginal = false;
                }
            }

            // Only add new entry if it doesn't exist in original file
            if (!empty($existingContent)) {
                // Find the position to insert the new entry (before the closing '];')
                $closingPattern = '/(\s*\]\s*;\s*)$/';
                if (preg_match($closingPattern, $existingContent, $matches, PREG_OFFSET_CAPTURE)) {
                    $insertPosition = $matches[0][1];
                    
                    // Prepare the new entry with double quotes and proper formatting
                    $escapedKey = str_replace(['\\', '"'], ['\\\\', '\\"'], $cleanKey);
                    
                    // Ensure proper line ending - check if there's already a newline before the closing bracket
                    $contentBeforeClosing = substr($existingContent, 0, $insertPosition);
                    $needsNewline = !preg_match('/\n\s*$/', $contentBeforeClosing);
                    
                    $newEntry = ($needsNewline ? "\n" : "") . "    \"" . $escapedKey . "\" => " . var_export($value, true) . ",\n";
                    
                    // Insert the new entry before the closing '];'
                    $content = $contentBeforeClosing . $newEntry . substr($existingContent, $insertPosition);
                    
                    Log::info("Inserted new entry into existing file", [
                        'key' => $cleanKey,
                        'insertPosition' => $insertPosition,
                        'needsNewline' => $needsNewline
                    ]);
                } else {
                    Log::error("Could not find closing pattern in existing file", [
                        'filePath' => $filePath,
                        'content' => $existingContent
                    ]);
                    return false;
                }
            } else {
                // Create new file with the new entry
                $escapedKey = str_replace(['\\', '"'], ['\\\\', '\\"'], $cleanKey);
                $content = "<?php\n\nreturn [\n    \"" . $escapedKey . "\" => " . var_export($value, true) . ",\n];\n";
                
                Log::info("Created new file with new entry", [
                    'key' => $cleanKey
                ]);
            }

            // Write to file with explicit logging
            Log::info("Writing translations to file", [
                'path' => $filePath,
                'contentLength' => strlen($content),
                'translationCount' => count($translations),
                'adding' => $cleanKey
            ]);

            $writeResult = file_put_contents($filePath, $content, LOCK_EX);

            if ($writeResult === false) {
                Log::error("Failed to write translations to file", [
                    'path' => $filePath,
                    'error' => error_get_last()
                ]);

                // Try to debug file permissions
                if (file_exists($filePath)) {
                    $perms = fileperms($filePath);
                    $owner = fileowner($filePath);
                    $group = filegroup($filePath);
                    Log::error("File permission details", [
                        'path' => $filePath,
                        'permissions' => substr(sprintf('%o', $perms), -4),
                        'owner' => $owner,
                        'group' => $group,
                        'isWritable' => is_writable($filePath)
                    ]);
                }
            } else {
                Log::info("Successfully wrote translations to file", [
                    'path' => $filePath,
                    'bytesWritten' => $writeResult,
                    'keyAdded' => $cleanKey
                ]);
                
                // Clear any opcache for this file to ensure fresh data
                if (function_exists('opcache_invalidate')) {
                    opcache_invalidate($filePath, true);
                }

                // Clear Laravel's translation cache
                try {
                    app('cache')->forget('translation.' . $locale . '.' . $file);
                    
                    // If Laravel's translation cache tag exists, clear it
                    if (app('cache')->getStore() instanceof \Illuminate\Cache\TaggedCache) {
                        app('cache')->tags('translations')->flush();
                    }
                } catch (\Exception $e) {
                    Log::warning("Failed to clear translation cache", [
                        'locale' => $locale,
                        'file' => $file,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            // Log successful translation update
            Log::info("Updated translation for {$locale}/{$file}: {$key} => {$value}");

            // Verify the file is readable/valid PHP
            if (!is_readable($filePath)) {
                Log::error("Created file is not readable: {$filePath}");
            } else {
                // Try to include the file to validate it
                try {
                    $testInclude = include $filePath;
                    if (!is_array($testInclude)) {
                        Log::error("File does not return an array: {$filePath}");
                    } else {
                        Log::info("Translation file validated successfully", [
                            'path' => $filePath,
                            'entriesCount' => count($testInclude)
                        ]);
                    }
                } catch (\Exception $e) {
                    Log::error("Error validating language file: {$e->getMessage()}");
                }
            }

            return true;

        } catch (\Exception $e) {
            Log::error("Failed to update translation file", [
                'locale' => $locale,
                'file' => $file,
                'key' => $key,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return false;
        }
    }

    protected function ensureLanguageDirectoryExists($locale)
    {
        $languageDir = lang_path($locale);

        try {
            if (!is_dir($languageDir)) {
                Log::info("Creating language directory", ['path' => $languageDir]);
                if (!mkdir($languageDir, 0755, true)) {
                    Log::error("Failed to create language directory", ['path' => $languageDir]);
                    return false;
                }
            }

            if (!is_writable($languageDir)) {
                Log::error("Language directory is not writable", ['path' => $languageDir]);
                return false;
            }

            Log::info("Language directory exists and is writable", ['path' => $languageDir]);
            return true;

        } catch (\Exception $e) {
            Log::error("Error ensuring language directory exists", [
                'locale' => $locale,
                'path' => $languageDir,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Reconstruct comma-separated translations from individual language file entries
     */
    protected function reconstructCommaSeparatedTranslations($originalServices, $translationArray)
    {
        if (empty($originalServices)) {
            return '';
        }

        // Split the original services by comma
        $originalServicesList = array_map('trim', explode(',', $originalServices));
        $reconstructedTranslations = [];

        // For each original service, find its translation
        foreach ($originalServicesList as $service) {
            if (!empty($service)) {
                // Look for the translation in the language array
                $translation = isset($translationArray[$service]) ? $translationArray[$service] : '';
                $reconstructedTranslations[] = $translation;
            }
        }

        // Join the translations back with commas, but only include non-empty translations
        $nonEmptyTranslations = array_filter($reconstructedTranslations, function($translation) {
            return !empty(trim($translation));
        });

        return implode(',', $nonEmptyTranslations);
    }
}
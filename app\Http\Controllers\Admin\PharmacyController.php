<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Services\PharmacyService\IPharmacyService;
use App\Http\Requests\Pharmacy\CreatePharmacyRequest;
use App\Http\Requests\Pharmacy\UpdatePharmacyByAdminRequest;

class PharmacyController extends Controller
{

    protected $pharmacyService;

    public function __construct(IPharmacyService $pharmacyService)
    {
        $this->pharmacyService = $pharmacyService;
    }

    public function index()
    {
        $pharmacies = $this->pharmacyService->all();
        return view("pages/admin/pharmacy/index", compact("pharmacies"));
    }

    public function store(CreatePharmacyRequest $request)
    {


        $this->pharmacyService->create($request->validated());

        return redirect()->back()->with([
            'success' => trans("alerts.success.pharmacy_added_successfully")
        ]);
    }


    public function edit(Request $request)
    {
        $pharmacy = $this->pharmacyService->get($request->id);
        return response()->json($pharmacy);
    }
    public function update(UpdatePharmacyByAdminRequest $request)
    {

        $pharmacy= $this->pharmacyService->update($request->validated(), $request->id);

        return redirect()->back()->with([
            'success' => trans("alerts.success.pharmacy_updated_successfully")
        ]);
    }


    public function block(Request $request)
    {
        $this->pharmacyService->block($request->id);
        return response()->json(false);
    }

    public function unblock(Request $request)
    {
        $this->pharmacyService->unblock($request->id);
        return response()->json(true);
    }


}

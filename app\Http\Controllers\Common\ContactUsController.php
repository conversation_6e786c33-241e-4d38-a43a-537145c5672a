<?php

namespace App\Http\Controllers\Common;

use App\Http\Controllers\Controller;
use App\Http\Requests\ContactUs\ContactUsRequest;
use App\Http\Requests\ContactUs\RegistrationRequestRequest;
use App\Mail\ContactUs;
use App\Mail\RegistrationRequest;
use Illuminate\Support\Facades\Mail;
// i've added this
use SEOMeta;
use OpenGraph;
use JsonLd;
// the end 


class ContactUsController extends Controller
{
    public function index()
    {
        // Set meta tags for SEO
        SEOMeta::setTitle('Contactez-Nous | DocExpress');
        SEOMeta::setDescription('Contactez DocExpress pour des demandes ou un soutien personnalisé en matière de santé.');
        SEOMeta::setCanonical(url()->current());
        // Set OpenGraph tags for social media
        OpenGraph::setTitle('Contactez-Nous | DocExpress');
        OpenGraph::setDescription('Trouvez toutes les informations nécessaires pour nous contacter et découvrir nos services de santé au Maroc.');
        OpenGraph::setUrl(url()->current());
        OpenGraph::addImage(asset('images/contact-og-image.jpg'));
        // Set JSON-LD structured data
        JsonLd::setTitle('Contactez-Nous | DocExpress');
        JsonLd::setDescription('Contactez-nous pour toute question ou assistance en matière de santé au Maroc.');
        JsonLd::setUrl(url()->current());
        JsonLd::addImage(asset('images/contact-json-ld-image.jpg'));
        return view('pages.contact-us.contact.index');
    }

    public function sendEmail(ContactUsRequest $request)
    {
        $data = $request->validated();
        Mail::to('<EMAIL>')->send(new ContactUs($data));
        return redirect()->back()->with([
            'success' => trans("alerts.success.your_message_is_sent_successfully")
        ]);
    }

    public function index_()
    {
        return view('pages.contact-us.registration_request.index');
    }

    public function registrationRequest(RegistrationRequestRequest $request)
    {
        $data = $request->validated();
        $data['name'] = $data['first_name'].' '.$data['last_name'];
        Mail::to('<EMAIL>')->send(new RegistrationRequest($data));
        return redirect()->back()->with([
            'success' => trans("alerts.success.your_message_is_sent_successfully")
        ]);
    }
}

<?php

namespace App\Http\Controllers\Patient;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\Dependent\CreateDependentRequest;
use App\Http\Requests\Dependent\UpdateDependentRequest;
use App\Repositories\DependentRepositoryInterface;

class DependentController extends Controller
{
    protected $dependentRepository;
    public function __construct(DependentRepositoryInterface $dependentRepository)
    {
        $this->dependentRepository = $dependentRepository;
    }
    public function index()
    {
        $dependent = $this->dependentRepository->getByPatientId();

        return view('pages.user-accounts.patient.dependent', compact('dependent'));
    }
    public function store(CreateDependentRequest $request)
    {
        $this->dependentRepository->store($request->validated());

        return redirect()->route('dependent')->with([
            'success' => trans("alerts.success.dependent_added_successfully")
        ]);
    }
    public function edit(Request $request)
    {
        $dependent = $this->dependentRepository->edit($request->id);
        return response()->json($dependent);
    }
    public function update(UpdateDependentRequest $request)
    {
        $this->dependentRepository->update($request->validated(), $request->id);
        return redirect()->route('dependent')->with([
            'success' => trans("alerts.success.dependent_updated_successfully")
        ]);
    }
    public function destroy($id)
    {
        $this->dependentRepository->delete($id);
        return redirect()->back()->with([
            'success' => trans("alerts.success.dependent_deleted_successfully")
        ]);
    }
}

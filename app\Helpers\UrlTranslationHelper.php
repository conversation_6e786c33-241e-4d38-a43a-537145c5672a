<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Log;

class UrlTranslationHelper
{
    /**
     * Clean a string by removing accents and special characters (same logic as frontend)
     */
    public static function cleanString($str)
    {
        if (empty($str)) return '';

        $str = strtolower($str);
        $str = str_replace(['é', 'è', 'ê', 'ë'], 'e', $str);
        $str = str_replace(['à', 'â', 'ä', 'á'], 'a', $str);
        $str = str_replace(['ù', 'û', 'ü', 'ú'], 'u', $str);
        $str = str_replace(['î', 'ï', 'í'], 'i', $str);
        $str = str_replace(['ô', 'ö', 'ó'], 'o', $str);
        $str = str_replace('ç', 'c', $str);
        $str = str_replace('ñ', 'n', $str);
        $str = str_replace([' ', '-'], '', $str);

        return $str;
    }

    /**
     * Find the original translation key from a clean URL parameter
     */
    public static function findOriginalKey($cleanValue, $translationArray)
    {
        if (empty($cleanValue)) return '';

        // Clean the input value
        $cleanInput = self::cleanString($cleanValue);

        // Search through all translation keys
        foreach ($translationArray as $key => $value) {
            // Clean both the key and value and compare
            if (self::cleanString($key) === $cleanInput || self::cleanString($value) === $cleanInput) {
                return $key;
            }
        }

        return $cleanValue; // fallback to original if not found
    }

    /**
     * Get translated city name from clean URL parameter
     */
    public static function getTranslatedCity($cleanCityParam, $locale = null)
    {
        if (empty($cleanCityParam)) return '';

        $locale = $locale ?: app()->getLocale();
        $cities = __('villes', [], $locale);

        $originalKey = self::findOriginalKey($cleanCityParam, $cities);
        $translated = __('villes.' . $originalKey);

        // If translation not found, fallback to cleaned parameter
        if (str_starts_with($translated, 'villes.')) {
            return ucfirst(str_replace('-', ' ', $cleanCityParam));
        }

        return $translated;
    }

    /**
     * Get translated district name from clean URL parameter
     */
    public static function getTranslatedDistrict($cleanDistrictParam, $locale = null)
    {
        if (empty($cleanDistrictParam)) return '';

        $locale = $locale ?: app()->getLocale();
        $districts = __('arrondissement', [], $locale);

        $originalKey = self::findOriginalKey($cleanDistrictParam, $districts);
        $translated = __('arrondissement.' . $originalKey);

        // If translation not found in regular translation files, try cities-districtsAR.JSON
        if (str_starts_with($translated, 'arrondissement.')) {
            $fallbackTranslated = self::getDistrictFromCitiesJson($cleanDistrictParam, $locale);
            if ($fallbackTranslated) {
                return $fallbackTranslated;
            }
            // Final fallback to cleaned parameter
            return ucfirst(str_replace('-', ' ', $cleanDistrictParam));
        }

        return $translated;
    }

    /**
     * Get district translation from cities-districtsAR.JSON file
     */
    private static function getDistrictFromCitiesJson($cleanDistrictParam, $locale = null)
    {
        try {
            $locale = $locale ?: app()->getLocale();

            // Only use this fallback for Arabic locale
            if ($locale !== 'ar') {
                return null;
            }

            $jsonPath = public_path('assets/js/cities-districtsAR.JSON');
            if (!file_exists($jsonPath)) {
                return null;
            }

            $citiesDistricts = json_decode(file_get_contents($jsonPath), true);
            if (!$citiesDistricts) {
                return null;
            }

            // Clean the search parameter for comparison
            $cleanParam = self::cleanString($cleanDistrictParam);

            // Search through all cities and their districts
            foreach ($citiesDistricts as $districts) {
                foreach ($districts as $district) {
                    $cleanDistrict = self::cleanString($district);
                    // Also check without "حي" prefix
                    $districtWithoutPrefix = str_replace('حي ', '', $district);
                    $cleanDistrictWithoutPrefix = self::cleanString($districtWithoutPrefix);

                    if ($cleanDistrict === $cleanParam || $cleanDistrictWithoutPrefix === $cleanParam) {
                        return $district;
                    }
                }
            }

            return null;
        } catch (\Exception $e) {
            Log::warning('Error reading cities-districtsAR.JSON: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get all cleaned-to-original mappings for cities
     */
    public static function getCityMappings($locale = null)
    {
        $locale = $locale ?: app()->getLocale();
        $cities = __('villes', [], $locale);
        $mappings = [];

        foreach ($cities as $key => $value) {
            $cleanKey = self::cleanString($key);
            $cleanValue = self::cleanString($value);

            if (!empty($cleanKey)) $mappings[$cleanKey] = $key;
            if (!empty($cleanValue) && $cleanValue !== $cleanKey) $mappings[$cleanValue] = $key;
        }

        return $mappings;
    }

    /**
     * Get all cleaned-to-original mappings for districts
     */
    public static function getDistrictMappings($locale = null)
    {
        $locale = $locale ?: app()->getLocale();
        $districts = __('arrondissement', [], $locale);
        $mappings = [];

        foreach ($districts as $key => $value) {
            $cleanKey = self::cleanString($key);
            $cleanValue = self::cleanString($value);

            if (!empty($cleanKey)) $mappings[$cleanKey] = $key;
            if (!empty($cleanValue) && $cleanValue !== $cleanKey) $mappings[$cleanValue] = $key;
        }

        return $mappings;
    }
}

<?php

namespace App\Http\Controllers\Common;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;

class LanguageController extends Controller
{
    public function change(Request $request)
    {
        // validate the language code
        $request->validate([
            'lang' => 'required|in:en,fr,ar,tam',
        ]);
        App::setLocale($request->lang);
        session()->put('locale', $request->lang);
        // if a user is logged in, update the language in the database
        if (Auth::check()) {
            $user = User::find(Auth::id());
            $user->prefered_language = $request->lang;
            $user->save();
        }
        return redirect()->back();
    }
}

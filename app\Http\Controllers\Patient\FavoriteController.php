<?php

namespace App\Http\Controllers\Patient;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Services\PatientService\IPatientService;

class FavoriteController extends Controller
{

    protected $patientService;
    public function __construct(IPatientService $patientService)
    {
        $this->patientService = $patientService;
    }
    public function index()
    {
        $favoriteDoctors = $this->patientService->getFavoriteDoctors();

        return view('pages.user-accounts.patient.favorite', compact('favoriteDoctors'));
    }
    public function store(Request $req)
    {
        return $this->patientService->toggleFavoriteDoctor($req->doctor_id) ? response()->json(['data' => 1]) : response()->json(['data' => 0]);
    }
    public function destroy($id)
    {
        $this->patientService->removeDoctorFromFavorite($id);
        return redirect()->back()->with([
            'success' => trans("alerts.success.favorite_removed")
        ]);
    }
}

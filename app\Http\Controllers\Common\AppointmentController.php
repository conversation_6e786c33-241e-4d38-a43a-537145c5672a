<?php

namespace App\Http\Controllers\Common;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Repositories\DoctorRepositoryInterface;
use App\Repositories\AppointmentRepositoryInterface;
use App\Services\AppointmentService\IAppointmentService;
use App\Http\Requests\Appointment\CreateAppointmentRequest;
use App\Http\Requests\Appointment\UpdateAppointmentStatusRequest;
use App\Http\Requests\Appointment\UpdateAppointmentStatusRequestByPatient;
use App\Http\Requests\Appointment\UpdateAppointmentStatusRequestByPatientWithMotif;
use App\Services\DoctorService\IDoctorService;
use App\Services\FreeAppointmentService;
use App\Repositories\DocClinicTimeRepositoryInterface;
use App\Http\Requests\Appointment\CreateAppointmentsForclinic;
use App\Models\Doctor;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\App;
use Illuminate\Http\Exceptions\HttpResponseException;
use App\Models\CentreMedical;
use App\Models\Assistante_ad;
use App\Models\Appointment;
use App\Mail\DoctorAppointmentRequest;
use App\Mail\ClinicAppointmentRequest;
use App\Mail\AssistantAppointmentRequest;
use App\Mail\PatientAppointmentRequest;
use App\Mail\AppointmentCanceledByPatient;
use App\Mail\AppointmentCanceled;
use App\Mail\AppointmentCanceledByPatientWithMotif; // Ensure this class exists in the specified namespace
use App\Mail\AppointmentCanceledWithMotif;
use App\Mail\AppointmentUpdatedByPatient; // Ensure this class exists in the specified namespace
use App\Mail\AppointmentUpdated; // Ensure this class exists and extends Mailable
use App\Notifications\AppointmentCanceledSms; // Ensure this class exists in the specified namespace
use App\Jobs\SendAppointmentEmails;
class AppointmentController extends Controller
{
    protected $appointmentService;
    protected $doctorRepository;
    protected $appointmentRepository;
    protected $docclinictimerepository;
    protected $freeAppointmentService;
    protected $doctorService;
    
    public function __construct(
        IDoctorService $doctorService
    ) {
        $this->doctorService = $doctorService;
        
        // Resolve other dependencies from the service container
        $this->appointmentService = app(IAppointmentService::class);
        $this->doctorRepository = app(DoctorRepositoryInterface::class);
        $this->appointmentRepository = app(AppointmentRepositoryInterface::class);
        $this->docclinictimerepository = app(DocClinicTimeRepositoryInterface::class);
        $this->freeAppointmentService = app(FreeAppointmentService::class);
    }

    public function adminIndex()
    {
        $appointments = $this->appointmentService->getAppointmentsByStatus();
        return view(
            'pages.admin.appointments.index',
            $appointments
        );
    }

    public function doctorIndex()
    {
        $doctorId = Auth::user()->doctor ? Auth::user()->doctor->id : Auth::user()->assistante->doctor_id;
        $confirmedAppointments = $this->appointmentService->getDoctorConfirmedAppointments($doctorId);
        if(Auth::user()->role=='doctor'){
            return view('pages.user-accounts.doctor.appointments', $confirmedAppointments);
        }
        return view('pages.user-accounts.assistante-administrative.appointments', $confirmedAppointments);
    }

    public function create(Request $request , $fullname)
{
        $doctorId = $request->query('doctor_id');
        $date = $request->query('date');

        // If doctor_id is provided as query parameter, use it
        if ($doctorId) {
            $doctor = $this->doctorRepository->get($doctorId);
        } else {
            // Otherwise, find doctor by URL-friendly name
            $doctor = $this->doctorService->getByUrlName($fullname);
            if (!$doctor) {
                // Try to get the doctor using the existing getByFullName method as fallback
                $doctor = $this->doctorService->getByFullName($fullname);
                if (!$doctor) {
                    return redirect()->back()->with('error', 'Doctor not found.');
                }
            }
        }
        $holidaysdate = [];
        $holidays = [];
        $inclinic = false;
        $clinicid = null; // Initialize the variable

        $medicalCenter = $doctor->medicalCenters()->first();
        if ($medicalCenter && $medicalCenter->pivot) {
            $clinicdocpivot = $medicalCenter->pivot;
            $clinicdocid = $clinicdocpivot->id;
            $clinicid = $clinicdocpivot->centre_medical_id;
            $docclinictime = $this->docclinictimerepository->getByDoctorCenter($clinicdocid);
            foreach ($docclinictime as $docclinic) {
                if (strtolower($docclinic->day) == strtolower(substr(Carbon::now()->dayName, 0, 3))) {
                    $inclinic = true;
                    break;
                }
            }
        }
        foreach ($doctor->holidays as $holiday) {
            $holidaysdate[] = $holiday['date_holiday'];
            $holidays[] = $holiday;
        }
        $appt_taken = $this->appointmentService->getbyDoctor($doctor->id);



        return view('pages.user-accounts.doctor.booking.index')->with([
            'doctor' => $doctor,
            'holidaysdate' => $holidaysdate,
            'holidays' => $holidays,
            'appt_taken' => $appt_taken,
            'inclinic' => $inclinic,
            'date' => $date ,
            'clinicid' => $clinicid,
            'translatedDoctors'=>$doctor->translated_speciality = __('specialities.' . $doctor->speciality->name, [], 'fr')
        ]);
}       
    //  public function store(CreateAppointmentRequest $req)
    // {
    //     if (!Auth::check()) {
    //         return redirect('/login?redirect=' . urlencode(url()->current()));
    //     }
    //     $appointment = $this->appointmentService->create($req->validated());
    //     return view('pages.user-accounts.doctor.booking.success', [
    //         'appointment' => $appointment,
    //         'dashboard_link' => route('dashboard-patient')
    //     ]);
    // }

    public function store(CreateAppointmentRequest $req)
    {
        if (!Auth::check()) {
            return redirect('/login?redirect=' . urlencode(url()->current()));
        }
    
        $newAppointment = $this->appointmentService->create($req->validated());
        $doctor = $newAppointment->doctor;
        $patient = $newAppointment->patient;
    
        // If this is a modification flow, cancel the old appointment now
        if ($req->has('modification') && $req->input('modification') == 1 && $req->has('old_appt_id')) {
            $this->appointmentService->updateStatusByPatient(
                $req->input('old_appt_id'),
                'cancel',
                $req->input('modification_motif')
            );
            $motif = $req->input('modification_motif');
            $this->appointmentService->notifyModification($newAppointment, $doctor, $patient, $motif);
        }
    
        // Synchronisation Google Calendar si le docteur a connecté son agenda
        $calendarAccount = $doctor->user->calendarAccounts()->where('provider', 'google')->first();
        if ($calendarAccount) {
            try {
                $client = new \Google_Client();
                $client->setClientId(config('services.google.client_id'));
                $client->setClientSecret(config('services.google.client_secret'));
                $client->setRedirectUri(config('services.google.redirect'));
                $client->setAccessToken(decrypt($calendarAccount->access_token));
                if ($client->isAccessTokenExpired() && $calendarAccount->refresh_token) {
                    $client->fetchAccessTokenWithRefreshToken(decrypt($calendarAccount->refresh_token));
                }
                $service = new \Google_Service_Calendar($client);
                $event = new \Google_Service_Calendar_Event([
                    'summary' => 'Rendez-vous avec ' . $patient->full_name,
                    'description' => $newAppointment->motif,
                    'start' => [
                        'dateTime' => $newAppointment->date,
                        'timeZone' => 'Africa/Casablanca', // Morocco timezone
                    ],
                    'end' => [
                        'dateTime' => $newAppointment->date, // à adapter si end différent
                        'timeZone' => 'Africa/Casablanca', // Morocco timezone
                    ],
                ]);
                $createdEvent = $service->events->insert('primary', $event);
                \App\Models\CalendarEvent::create([
                    'user_id' => $doctor->user->id,
                    'appointment_id' => $newAppointment->id,
                    'provider' => 'google',
                    'external_event_id' => $createdEvent->id,
                    'sync_status' => 'synced',
                    'last_synced_at' => now(),
                ]);
            } catch (\Exception $e) {
                \Log::error('Erreur synchronisation Google Calendar : ' . $e->getMessage());
            }
        }
    
        return view('pages.user-accounts.doctor.booking.success', [
            'appointment' => $newAppointment,
            'dashboard_link' => route('dashboard-patient')
        ]);
    }


    public function storeClinique(CreateAppointmentsForclinic $req)
    {
        if (!Auth::check()) {
            return redirect('/login?redirect=' . urlencode(url()->current()));
        }

        Log::info('========== APPOINTMENT CREATION PROCESS STARTED ==========');
        Log::info('User authenticated: ' . Auth::user()->email);

        try {
            // Get validated data and ensure status is set
            $appointmentData = $req->validated();
            $appointmentData['status'] = $appointmentData['status'] ?? 'pending';

            // Create appointment
            $appointment = $this->appointmentService->createForClinique($appointmentData);

            if ($appointment) {
                Log::info('Appointment created successfully', [
                    'appointment_id' => $appointment->id,
                    'date' => $appointment->date,
                    'status' => $appointment->status,
                    'doctor_id' => $appointment->doctor_id,
                    'patient_id' => $appointment->patient_id,
                    'motif' => $appointment->motif,
                    'type' => $appointment->type
                ]);

                // Queue emails with the current locale
                $locale = App::getLocale();
                Log::info('Current locale for emails: ' . $locale);

                SendAppointmentEmails::dispatch($appointment, $locale);
                Log::info('Email notifications queued for processing');

                // Return success to the user
                return view('pages.user-accounts.doctor.booking.success', [
                    'appointment' => $appointment,
                    'dashboard_link' => route('dashboard-patient')
                ]);
            } else {
                Log::error('Failed to create appointment');
                return redirect()->back()->with('error', 'Failed to create appointment');
            }        } catch (\Exception $e) {
            Log::error('Error creating appointment: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'user_id' => Auth::user()->id,
                'doctor_id' => $appointmentData['doctor_id'] ?? 'unknown'
            ]);
            
            // Handle specific error messages for better user experience
            if (str_contains($e->getMessage(), 'already_taken')) {
                return redirect()->back()->with('error', trans("alerts.error.sorry_this_time_is_already_taken"));
            }
            
            return redirect()->back()->with('error', trans("alerts.error.something_went_wrong"));
        }
    }    public function updateStatus(UpdateAppointmentStatusRequest $req)
    {
        $doctorId = Auth::user()->doctor ? Auth::user()->doctor->id : Auth::user()->assistante->doctor_id;
        $this->appointmentService->updateStatus($req->appt_id, $req->action, $req->date, $req->time, $doctorId);

        // Synchronisation Google Calendar lors de la modification d'un rendez-vous
        $appointment = Appointment::find($req->appt_id);
        if ($appointment && $appointment->doctor && $appointment->doctor->user) {
            $calendarAccount = $appointment->doctor->user->calendarAccounts()->where('provider', 'google')->first();
            $calendarEvent = $appointment->calendarEvents()->where('provider', 'google')->first();
            if ($calendarAccount && $calendarEvent) {
                try {
                    $client = new \Google_Client();
                    $client->setClientId(config('services.google.client_id'));
                    $client->setClientSecret(config('services.google.client_secret'));
                    $client->setRedirectUri(config('services.google.redirect'));
                    $client->setAccessToken(decrypt($calendarAccount->access_token));
                    if ($client->isAccessTokenExpired() && $calendarAccount->refresh_token) {
                        $client->fetchAccessTokenWithRefreshToken(decrypt($calendarAccount->refresh_token));
                    }
                    $service = new \Google_Service_Calendar($client);
                    $event = $service->events->get('primary', $calendarEvent->external_event_id);
                    $event->setSummary('Rendez-vous avec ' . $appointment->patient->full_name);
                    $event->setDescription($appointment->motif);
                    $event->setStart([
                        'dateTime' => $appointment->date,
                        'timeZone' => 'Africa/Casablanca', // Morocco timezone
                    ]);
                    $event->setEnd([
                        'dateTime' => $appointment->date,
                        'timeZone' => 'Africa/Casablanca', // Morocco timezone
                    ]);
                    $service->events->update('primary', $calendarEvent->external_event_id, $event);
                    $calendarEvent->update([
                        'sync_status' => 'synced',
                        'last_synced_at' => now(),
                    ]);
                } catch (\Exception $e) {
                    \Log::error('Erreur update Google Calendar : ' . $e->getMessage());
                }
            }
        }

        switch ($req->action) {
            case 'accept':
                return redirect()->back()->with('success', trans("alerts.success.the_appointment_has_been_accepted"));
            case 'reject':
                return redirect()->back()->with('success', trans("alerts.success.the_appointment_has_been_rejected"));
            case 'complete':
                return redirect()->back()->with('success', trans("alerts.success.the_appointment_has_been_completed"));
            case 'cancel':
                return redirect()->back()->with('success', trans("alerts.success.the_appointment_has_been_cancelled"));
            case 'edit':
                return redirect()->back()->with('success', trans("alerts.success.the_appointment_has_been_updated"));
        }
    }

    public function updateStatusByPatient(UpdateAppointmentStatusRequestByPatient $req)
    {
        try {
            $this->appointmentService->updateStatusByPatient($req->appt_id, $req->action);
        } catch (\Throwable $th) {
            return redirect()->back()->with('error', trans("alerts.error.something_went_wrong"));
        }

        // Synchronisation Google Calendar lors de la modification/cancellation par le patient
        $appointment = Appointment::find($req->appt_id);
        if ($appointment && $appointment->doctor && $appointment->doctor->user) {
            $calendarAccount = $appointment->doctor->user->calendarAccounts()->where('provider', 'google')->first();
            $calendarEvent = $appointment->calendarEvents()->where('provider', 'google')->first();
            if ($calendarAccount && $calendarEvent) {
                try {
                    $client = new \Google_Client();
                    $client->setClientId(config('services.google.client_id'));
                    $client->setClientSecret(config('services.google.client_secret'));
                    $client->setRedirectUri(config('services.google.redirect'));
                    $client->setAccessToken(decrypt($calendarAccount->access_token));
                    if ($client->isAccessTokenExpired() && $calendarAccount->refresh_token) {
                        $client->fetchAccessTokenWithRefreshToken(decrypt($calendarAccount->refresh_token));
                    }
                    $service = new \Google_Service_Calendar($client);
                    if ($req->action === 'cancel') {
                        $service->events->delete('primary', $calendarEvent->external_event_id);
                        $calendarEvent->update([
                            'sync_status' => 'deleted',
                            'last_synced_at' => now(),
                        ]);
                    } else {
                        $event = $service->events->get('primary', $calendarEvent->external_event_id);
                        $event->setSummary('Rendez-vous avec ' . $appointment->patient->full_name);
                        $event->setDescription($appointment->motif);
                        $event->setStart([
                            'dateTime' => $appointment->date,
                            'timeZone' => 'Africa/Casablanca', // Morocco timezone
                        ]);
                        $event->setEnd([
                            'dateTime' => $appointment->date,
                            'timeZone' => 'Africa/Casablanca', // Morocco timezone
                        ]);
                        $service->events->update('primary', $calendarEvent->external_event_id, $event);
                        $calendarEvent->update([
                            'sync_status' => 'synced',
                            'last_synced_at' => now(),
                        ]);
                    }
                } catch (\Exception $e) {
                    \Log::error('Erreur update/cancel Google Calendar : ' . $e->getMessage());
                }
            }
        }

        switch ($req->action) {
            case 'confirm':
                return redirect()->back()->with('success', trans("alerts.success.the_appointment_has_been_confirmed_after_update"));
            case 'cancel':
                return redirect()->back()->with('success', trans("alerts.success.the_appointment_has_been_cancelled_after_update"));
        }
    }

public function updateStatusByPatientWithMotif(UpdateAppointmentStatusRequestByPatientWithMotif $req)
{
    try {
        $appointment = $this->appointmentService->updateStatusByPatient($req->appt_id, $req->action, $req->motif);

        Log::info('Appointment cancellation with motif requested by patient', [
            'appointment_id' => $appointment->id,
            'patient_id' => $appointment->patient_id,
            'doctor_id' => $appointment->doctor_id,
            'motif' => $req->motif,
            'action' => $req->action
        ]);

        // Send emails with motif
        $doctor = $appointment->doctor;
        $patient = $appointment->patient;

        // Send SMS via notification
        (new \App\Notifications\AppointmentCanceledSms($appointment, $req->motif))->send($patient);

        // Or, if you want to keep the direct SMS as well, you can have both

        Mail::to($doctor->user->email)->send(
            new AppointmentCanceledByPatientWithMotif($appointment, $doctor, $patient, $req->motif)
        );
        Mail::to($patient->user->email)->send(
            new AppointmentCanceledWithMotif($appointment, $doctor, $patient, $req->motif)
        );

        return redirect()->back()->with('success', trans("alerts.success.the_appointment_has_been_cancelled_after_update"));
    } catch (\Throwable $th) {
        Log::error('Error cancelling appointment with motif: ' . $th->getMessage());
        return redirect()->back()->with('error', trans("alerts.error.something_went_wrong"));
    }
}
public function modifyAppointmentWithMotif(Request $request)
{
    $request->validate([
        'appt_id' => 'required|exists:appointments,id',
        'motif' => 'required|string',
    ]);

    try {
        // 1. Get the doctor from the appointment
        $appointment = \App\Models\Appointment::find($request->appt_id);
        $doctor = $appointment->doctor;

        // 2. Redirect to booking page for the same doctor, passing motif and old appointment ID as query params
        return redirect('/book/' . $doctor->url_name . '?modification=1&modification_motif=' . urlencode($request->motif) . '&old_appt_id=' . $appointment->id)
            ->with('success', __('alerts.success.ready_to_book_new_appointment'));

    } catch (\Throwable $th) {
        Log::error('Appointment modification failed: ' . $th->getMessage());
        return redirect()->back()->with('error', __('alerts.error.something_went_wrong'));
    }
}

    public function destroy($id)
    {
        $this->appointmentService->delete($id);
        return redirect()->back()->with([
            'success' => trans("alerts.success.appointment_delete_successfully")
        ]);
    }    public function showFirstMeetingFree($doc_id)
    {
        try {
            // Check if user is authenticated and has a patient profile
            if (!Auth::check() || !Auth::user()->patient) {
                // For guests or users without patient profile, show free appointments
                $result = $this->freeAppointmentService->getNextFreeSlot($doc_id);
                
                if (!$result) {
                    return response()->json(['error' => __('search.no_available_slots')], 404);
                }
                
                return response()->json($result);
            }            // Check if the authenticated patient already has an appointment today
            $patientId = Auth::user()->patient->id;
            $hasAppointmentToday = $this->appointmentRepository->hasPatientAppointmentToday($patientId);

            if ($hasAppointmentToday) {
                // Get the next available booking day
                $nextAvailableDay = $this->appointmentRepository->getNextAvailableBookingDay($patientId);
                
                $response = [
                    'has_appointment_today' => true,
                    'message' => __('search.already_booked_today'),
                    'can_book' => false
                ];

                if ($nextAvailableDay) {
                    $response['next_available_day'] = [
                        'date' => $nextAvailableDay->toDateString(),
                        'formatted_date' => $nextAvailableDay->format('d/m/Y'),
                        'day_name' => $nextAvailableDay->locale(app()->getLocale())->isoFormat('dddd'),
                        'is_tomorrow' => $nextAvailableDay->isToday() ? false : $nextAvailableDay->isTomorrow()
                    ];
                    $response['message'] = __('search.already_booked_today_next_available', [
                        'date' => $nextAvailableDay->locale(app()->getLocale())->isoFormat('dddd D MMMM')
                    ]);
                }

                return response()->json($response);
            }

            // Use the new FreeAppointmentService for cleaner logic
            $result = $this->freeAppointmentService->getNextFreeSlot($doc_id);
            
            if (!$result) {
                return response()->json(['error' => __('search.no_available_slots')], 404);
            }
            
            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('Error in showFirstMeetingFree: ' . $e->getMessage());
            return response()->json(['error' => __('alerts.error.something_went_wrong')], 500);
        }
    }

    /**
     * Find the next available appointment slot for a doctor
     */
    private function findNextAvailableSlot($doctor, $holidays, $bookedSlots)
    {
        $doctorStartTime = Carbon::parse($doctor->start_time);
        $doctorEndTime = Carbon::parse($doctor->end_time);
        $latestDateTime = $latestDate ? Carbon::parse($latestDate) : null;
        $latestUpcomingDateTime = $latestUpcomingDate ? Carbon::parse($latestUpcomingDate) : null;

        $nextAvailableDateTime = Carbon::now()->addHour()->addMinutes(30 - (Carbon::now()->minute % 30)); // Add one hour and round to next half hour
        $availableDates = [];

        while (count($availableDates) < 3) {
            if ($latestDateTime && $latestDateTime->greaterThan($nextAvailableDateTime)) {
                $nextAvailableDateTime = $latestDateTime->addMinutes(30);
            } elseif ($latestUpcomingDateTime && $latestUpcomingDateTime->greaterThan($nextAvailableDateTime)) {
                $nextAvailableDateTime = $latestUpcomingDateTime->addMinutes(30);
            }

            if ($nextAvailableDateTime->greaterThan($doctorEndTime)) {
                $nextAvailableDateTime = $doctorStartTime->addDay();
            }

            if ($nextAvailableDateTime->greaterThan(Carbon::now())) {
                $availableDates[] = $nextAvailableDateTime->format('Y-m-d H:i:s');
            }
            $nextAvailableDateTime = $nextAvailableDateTime->addMinutes(30); // Increment for the next slot
        }

        return response()->json(['latest_dates' => $availableDates]);
    }

    public function isWeekday()
    {
        $dayOfWeek = Carbon::now()->dayOfWeek;
        return $dayOfWeek != Carbon::SATURDAY && $dayOfWeek != Carbon::SUNDAY;
    }    /**
     * Invalidate cache for a doctor and trigger immediate cache update
     */
    private function invalidateDoctorCache($doctorId)
    {
        // Use the FreeAppointmentService for immediate and aggressive cache invalidation
        $this->freeAppointmentService->invalidateCache($doctorId);
        
        // Also clear any Laravel cache that might be related
        \Illuminate\Support\Facades\Cache::forget("free_appointment_doctor_{$doctorId}");
        \Illuminate\Support\Facades\Cache::forget("doctor_appointments_{$doctorId}");
        
        Log::info("All caches cleared immediately for doctor {$doctorId}");
    }
}

<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\FreeAppointmentService;
use App\Repositories\AppointmentRepositoryInterface;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class FreeAppointmentController extends Controller
{
    protected $freeAppointmentService;
    protected $appointmentRepository;

    public function __construct(
        FreeAppointmentService $freeAppointmentService,
        AppointmentRepositoryInterface $appointmentRepository
    ) {
        $this->freeAppointmentService = $freeAppointmentService;
        $this->appointmentRepository = $appointmentRepository;
    }    /**
     * Get single doctor's free appointment slot (API route method)
     */
    public function getSingleDoctorSlots($doctorId): JsonResponse
    {
        try {
            // Check if user is authenticated and has a patient profile
            if (!Auth::check() || !Auth::user()->patient) {
                // For guests or users without patient profile, show free appointments
                $slotData = $this->freeAppointmentService->getNextFreeSlot($doctorId);
                
                if (!$slotData) {
                    return response()->json([
                        'next_slot' => null,
                        'can_book' => false,
                        'message' => __('search.no_available_slots'),
                        'updated_at' => now()->toISOString()
                    ]);
                }

                return response()->json($slotData);
            }            // Check if the authenticated patient already has an appointment today
            $patientId = Auth::user()->patient->id;
            $hasAppointmentToday = $this->appointmentRepository->hasPatientAppointmentToday($patientId);            if ($hasAppointmentToday) {
                // Get the next available booking day
                $nextAvailableDay = $this->appointmentRepository->getNextAvailableBookingDay($patientId);
                
                $message = __('search.already_booked_today');
                
                if ($nextAvailableDay) {
                    if ($nextAvailableDay->isTomorrow()) {
                        $message = __('search.already_booked_today') . '. ' . 
                                  __('search.next_available_booking') . ': ' . 
                                  __('search.available_tomorrow');
                    } else {
                        $dayName = $nextAvailableDay->locale(app()->getLocale())->isoFormat('dddd D MMMM');
                        $message = __('search.already_booked_today') . '. ' . 
                                  __('search.next_available_booking') . ': ' . 
                                  $dayName;
                    }
                }

                return response()->json([
                    'has_appointment_today' => true,
                    'message' => $message,
                    'can_book' => false,
                    'next_available_day' => $nextAvailableDay ? [
                        'date' => $nextAvailableDay->toDateString(),
                        'formatted_date' => $nextAvailableDay->format('d/m/Y'),
                        'day_name' => $nextAvailableDay->locale(app()->getLocale())->isoFormat('dddd'),
                        'is_tomorrow' => $nextAvailableDay->isTomorrow()
                    ] : null
                ]);
            }

            $slotData = $this->freeAppointmentService->getNextFreeSlot($doctorId);
            
            if (!$slotData) {
                return response()->json([
                    'next_slot' => null,
                    'can_book' => false,
                    'message' => __('search.no_available_slots'),
                    'updated_at' => now()->toISOString()
                ]);
            }

            return response()->json($slotData);

        } catch (\Exception $e) {
            return response()->json([
                'error' => __('alerts.error.something_went_wrong'),
                'message' => 'Failed to load appointment slot'
            ], 500);
        }
    }

    /**
     * Get next free appointment slot for a doctor (legacy method)
     */
    public function getNextFreeSlot($doctorId): JsonResponse
    {
        return $this->getSingleDoctorSlots($doctorId);
    }

    /**
     * Refresh cache and get updated slot (legacy method)
     */
    public function refreshSlot($doctorId): JsonResponse
    {
        try {
            $slotData = $this->freeAppointmentService->refreshCache($doctorId);
            
            return response()->json($slotData ?: [
                'next_slot' => null,
                'can_book' => false,
                'message' => __('search.no_available_slots'),
                'updated_at' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => __('alerts.error.something_went_wrong'),
                'message' => 'Failed to refresh appointment slot'
            ], 500);
        }
    }    /**
     * Get multiple doctors' free slots (API route method)
     */
    public function getMultipleDoctorSlots(Request $request): JsonResponse
    {
        try {
            $doctorIds = $request->input('doctor_ids', []);
            
            if (empty($doctorIds) || !is_array($doctorIds)) {
                return response()->json(['error' => 'Invalid doctor IDs'], 400);
            }

            $results = [];
            foreach ($doctorIds as $doctorId) {
                $results[$doctorId] = $this->freeAppointmentService->getNextFreeSlot($doctorId);
            }

            return response()->json($results);

        } catch (\Exception $e) {
            return response()->json([
                'error' => __('alerts.error.something_went_wrong'),
                'message' => 'Failed to load appointment slots'
            ], 500);
        }
    }

    /**
     * Refresh cache for doctors
     */
    public function refreshCache(Request $request): JsonResponse
    {
        try {
            $doctorIds = $request->input('doctor_ids', []);
            
            if (empty($doctorIds)) {
                return response()->json(['error' => 'No doctor IDs provided'], 400);
            }

            $results = [];
            foreach ($doctorIds as $doctorId) {
                $results[$doctorId] = $this->freeAppointmentService->refreshCache($doctorId);
            }

            return response()->json([
                'success' => true,
                'message' => 'Cache refreshed successfully',
                'data' => $results
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => __('alerts.error.something_went_wrong'),
                'message' => 'Failed to refresh cache'
            ], 500);
        }
    }

    /**
     * Legacy method - get multiple doctors' free slots
     */
    public function getMultipleFreeSlots(Request $request): JsonResponse
    {
        return $this->getMultipleDoctorSlots($request);
    }
}

<?php

namespace App\Http\Controllers\CenterMedical;

use App\Http\Controllers\Controller;
use App\Services\CentreMedicalService\CentreMedicalService;
use App\Services\DoctorToCentreMedical\DoctorToCentreMedical;
use Illuminate\Support\Facades\Auth;

class CentreMedicalDoctors extends Controller
{
    protected $centreMedicalService;
    protected $doctorToCentreMedical;
    public function __construct(
        CentreMedicalService $centreMedicalService,
        DoctorToCentreMedical $doctorToCentreMedical,
    ) {
        $this->centreMedicalService = $centreMedicalService;
        $this->doctorToCentreMedical = $doctorToCentreMedical;
    }
    public function index()
    {
        if (Auth::check()) {
            $user = Auth::user();
            if ($user->centreMedical) {
                $medicalCenter = $user->centreMedical;
                $centreId = $medicalCenter->id;
                $doctors = $this->doctorToCentreMedical->getDoctorsByCentre($centreId);
                $translatedDoctors = $doctors->map(function ($doctor) {
                    $doctor->translated_speciality = __('specialities.' . $doctor->name, [], 'fr');
                    return $doctor;
                });
                return view('pages.user-accounts.centreMedical.doctors-centre', compact('doctors', 'medicalCenter',"translatedDoctors"));
            }
        }
        return redirect()->route('dashboard')->with('error', 'You are not associated with a medical center.');
    }
}

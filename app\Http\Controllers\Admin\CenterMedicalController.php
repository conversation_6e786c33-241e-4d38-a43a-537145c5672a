<?php
namespace App\Http\Controllers\Admin;
use Illuminate\Support\Str;
use App\Http\Controllers\Controller;
use App\Http\Requests\CentreMedical\CreateCentreMedicalRequest;
use App\Http\Requests\CentreMedical\UpdateCentreMedicalRequest;
use App\Services\CentreMedicalService\CentreMedicalService;
use App\Services\DoctorToCentreMedical\DoctorToCentreMedical;
use App\Repositories\CentreMedicalRepository;
use App\Repositories\AddressRepository;
use App\Repositories\UserRepository;
use App\Http\Requests\DoctorToCentreMedical\CreateDoctorToCentreMedical;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\CentreMedical;
use App\Models\Address;
use App\Notifications\ClinicAccountCreatedNotification;
use App\Mail\ClinicAccountCreated;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\File;

class CenterMedicalController extends Controller
{
    protected $centreMedicalService;
    protected $doctorToCentreMedical;
    protected $addressRepository;
    protected $userRepository;

    public function __construct(
        CentreMedicalService $centreMedicalService,
        DoctorToCentreMedical $doctorToCentreMedical,
        AddressRepository $addressRepository,
        UserRepository $userRepository,
    ) {
        $this->centreMedicalService = $centreMedicalService;
        $this->doctorToCentreMedical = $doctorToCentreMedical;
        $this->addressRepository = $addressRepository;
        $this->userRepository = $userRepository;
    }

    public function index()
    {
        $centres = $this->centreMedicalService->all()->load('address' ,'user');
        return view('pages.admin.medical-center.index', compact('centres'));
    }

    // public function create()
    // {
    //     $addresses = $this->addressRepository->all();
    //     return view('pages.admin.medical-center.create', compact('addresses'));
    // }
    public function create()
    {

        $translations = [
            'fr' => [
                'name' => '',
                'presentation' => '',
                'address' => '',
            ],
            'en' => [
                'name' => '',
                'presentation' => '',
                'address' => '',
            ],
            'ar' => [
                'name' => '',
                'presentation' => '',
                'address' => '',
            ],
        ];



        return view('pages.admin.medical-center.create', compact('translations'));
    }

 public function store(CreateCentreMedicalRequest $request)
{
    $validated = $request->validated();

    $logoPath = $request->hasFile('logo')
        ? $request->file('logo')->store('logos', 'public')
        : null;

    // ✅ Utilise les champs français comme clé directe (même avec espaces)
    $nameKey = $validated['name'];       // Exemple : "walid anif"
    $addressKey = $validated['address']; // Exemple : "12 rue el fida"

    // ✅ Sauvegarde des traductions - seulement si les valeurs ne sont pas vides et si les fichiers existent

    // Nom du centre
    if (!empty($validated['name'])) {
        $this->addTranslationEntry('fr/clinique.php', $nameKey, $validated['name']);
    }
    if (!empty($request->input('name_en'))) {
        $this->addTranslationEntry('en/clinique.php', $nameKey, $request->input('name_en'));
    }
    if (!empty($request->input('name_ar'))) {
        $this->addTranslationEntry('ar/clinique.php', $nameKey, $request->input('name_ar'));
    }

    // Présentation
    if (!empty($request->input('presentation_fr'))) {
        $this->addTranslationEntry('fr/presentations.php', $nameKey, $request->input('presentation_fr'));
    }
    if (!empty($request->input('presentation_en'))) {
        $this->addTranslationEntry('en/presentations.php', $nameKey, $request->input('presentation_en'));
    }
    if (!empty($request->input('presentation_ar'))) {
        $this->addTranslationEntry('ar/presentations.php', $nameKey, $this->formatArabicText($request->input('presentation_ar')));
    }

    // Adresse
    if (!empty($validated['address'])) {
        $this->addTranslationEntry('fr/address.php', $addressKey, $validated['address']);
    }
    if (!empty($request->input('address_en'))) {
        $this->addTranslationEntry('en/address.php', $addressKey, $request->input('address_en'));
    }
    if (!empty($request->input('address_ar'))) {
        $this->addTranslationEntry('ar/address.php', $addressKey, $request->input('address_ar'));
    }

    // ✅ Transaction
    DB::beginTransaction();

    try {
        $user = $this->userRepository->create([
            'email' => $validated['email'],
            'password' => bcrypt($validated['password']),
            'role' => 'centre_medical',
            'phone' => $validated['phone']
        ]);
        $plainPassword = $validated['password'];

        $centerM = $this->centreMedicalService->create([
            'name' => $validated['name'],
            'clinic_type' => $validated['clinic_type'],
            'presentation' => $validated['presentation'],
            'logo' => $logoPath,
            'user_id' => $user->id,
        ]);

        $address = $this->addressRepository->create([
            'address' => $validated['address'],
            'postal_code' => $validated['postal_code'],
            'district' => $validated['district'],
            'city' => $validated['city'],
            'country' => $validated['country'],
            'user_id' => $user->id,
            'centre_medical_id' => $centerM->id,
        ]);

        Mail::to($user->email)->send(new ClinicAccountCreated($user, $plainPassword, $centerM));
        DB::commit();

    } catch (\Throwable $th) {
        DB::rollBack();
        if (isset($user)) User::destroy($user->id);
        if (isset($centerM)) CentreMedical::destroy($centerM->id);
        if (isset($address)) Address::destroy($address->id);

        return redirect()->route('medical-center')->with('error', 'Medical center not created successfully!');
    }

    return redirect()->route('medical-center')->with('success', 'Medical center created successfully!');
}

public function show($id)
{
    $centre = $this->centreMedicalService->get($id);

    if (! $centre) {
        dd("ID reçu dans show() = ", $id); // Debug
        return redirect()
            ->route('medical-center')
            ->with('error', 'Centre médical introuvable.');
    }

    $centre->load('address', 'user');
    return view('pages.admin.medicalcenter-doctor.index', compact('centre'));
}
/**
 * Charge toutes les traductions d'un fichier langue et retourne un tableau clé => valeur.
 */
protected function getTranslations(string $lang): array
{
    $path = resource_path("lang/{$lang}/clinique.php");

    return file_exists($path) ? include $path : [];
    if (!file_exists($path)) {
        return [];
    }

    // Le fichier doit retourner un tableau associatif
    return include $path;
}

 public function edit($id)
    {
        $medicalCenter = $this->centreMedicalService->get($id);

        if (!$medicalCenter) {
            return redirect()->route('medical-center.index')->with('error', __('alerts.error.medicalCenter_not_found'));
        }

        $medicalCenter->load('address', 'user');

        Log::debug('Medical Center Data:', $medicalCenter->toArray());
        Log::debug('Address Data:', $medicalCenter->address ? $medicalCenter->address->toArray() : ['address' => 'null']);
        Log::debug('User Data:', $medicalCenter->user ? $medicalCenter->user->toArray() : ['user' => 'null']);

        $city = $medicalCenter->address->city ?? null;
        $district = $medicalCenter->address->district ?? null;
        $addressValue = $medicalCenter->address->address ?? '';

        $translations = [
            'fr' => [
                'name' => trans('clinique', [], 'fr')[$medicalCenter->name] ?? $medicalCenter->name,
               'presentation' => trans('presentations', [], 'fr')[$medicalCenter->name] ?? $medicalCenter->presentation,
                'address' => trans('address', [], 'fr')[$addressValue] ?? $addressValue,
            ],
            'en' => [
                'name' => trans('clinique', [], 'en')[$medicalCenter->name] ?? $medicalCenter->name,
                'presentation' => trans('presentations', [], 'en')[$medicalCenter->name] ?? '',
                'address' => trans('address', [], 'en')[$addressValue] ?? $addressValue,
            ],
            'ar' => [
                'name' => trans('clinique', [], 'ar')[$medicalCenter->name] ?? $medicalCenter->name,
                'presentation' => trans('presentations', [], 'ar')[$medicalCenter->name] ?? '',
                'address' => trans('address', [], 'ar')[$addressValue] ?? $addressValue,
            ],
        ];
$citiesJson = file_get_contents(public_path('assets/js/cities-districts.json'));
$citiesArray = json_decode($citiesJson, true);

$cities = array_keys($citiesArray);

// Récupérer la ville actuelle du centre médical
$currentCity = $medicalCenter->address->city ?? null;

// Initialiser districts en fonction de la ville actuelle
$districts = $currentCity && isset($citiesArray[$currentCity]) ? $citiesArray[$currentCity] : [];

return view('pages.admin.medical-center.edit', [
    'medicalCenter' => $medicalCenter,
    'translations' => $translations,
    'clinicTypes' => $this->loadClinicTypes(),
    'cities' => $cities,
    'districts' => $districts,
]);
}




protected function getAddressTranslations(string $lang): array
{
    $path = lang_path("{$lang}/address.php");
    return file_exists($path) ? include $path : [];
}
/**
 * Charge les traductions depuis un fichier PHP selon la langue
 */

protected function loadClinicTypes(): array
{
    $path = public_path('assets/js/clinics-type.json');

    if (!file_exists($path)) {
        Log::error("Le fichier clinics-type.json est introuvable à l'emplacement : $path");
        return [];
    }

    $jsonContent = file_get_contents($path);
    $clinicTypes = json_decode($jsonContent, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        Log::error("Erreur de décodage JSON dans clinics-type.json : " . json_last_error_msg());
        return [];
    }

    return $clinicTypes;
}







  public function update(UpdateCentreMedicalRequest $request, $id)
    {
        DB::beginTransaction();

        try {
            $medicalCenter = $this->centreMedicalService->get($id);
            if (!$medicalCenter) {
                return redirect()->route('medical-center.index')
                    ->with('error', __('alerts.error.medicalCenter_not_found'));
            }

            $validated = $request->validated();

            // Gestion du logo
            $logoPath = $medicalCenter->logo;
            if ($request->hasFile('logo')) {
                if ($logoPath && Storage::disk('public')->exists($logoPath)) {
                    Storage::disk('public')->delete($logoPath);
                }
                $logoPath = $request->file('logo')->store('logos', 'public');
            }

            $oldNameKey = $medicalCenter->name;
            $oldAddressKey = $medicalCenter->address->address ?? null;

            // Mise à jour centre médical
            $medicalCenter->update([
                'name' => $validated['name'],
                'clinic_type' => $validated['clinic_type'],
                'logo' => $logoPath,
            ]);

            // Mise à jour adresse
            if ($medicalCenter->address) {
                $medicalCenter->address->update([
                    'address' => $validated['address'],
                    'postal_code' => $validated['postal_code'],
                    'district' => $validated['district'],
                    'city' => $validated['city'],
                    'country' => $validated['country'],
                ]);
            }

            // Logs pour debug des présentations reçues
            Log::debug('Présentation FR:', [$validated['presentation']]);
            Log::debug('Présentation EN:', [$request->input('presentation_en')]);
            Log::debug('Présentation AR:', [$request->input('presentation_ar')]);

            // Mise à jour traductions (évite d’écraser par vide)
            $this->updateTranslations($validated, $request, $oldNameKey, $oldAddressKey);

            DB::commit();

            return redirect()->route('medical-center')->with('success', 'Centre médical mis à jour avec succès.');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Erreur mise à jour centre médical : '.$e->getMessage());
            return redirect()->route('medical-center')->with('error', 'Erreur lors de la mise à jour: '.$e->getMessage());
        }
    }

    protected function updateTranslations($validated, $request, $oldNameKey, $oldAddressKey)
    {
        $newNameKey = $validated['name'];
        $newAddressKey = $validated['address'];

        $translationFiles = [
            'clinique' => [
                'fr' => $validated['name'],
                'en' => $request->input('name_en'),
                'ar' => $request->input('name_ar'),
            ],
            'presentations' => [
                'fr' => $validated['presentation'],
                'en' => $request->input('presentation_en'),
                'ar' => $this->formatArabicText($request->input('presentation_ar')),
            ],
            'address' => [
                'fr' => $validated['address'],
                'en' => $request->input('address_en'),
                'ar' => $request->input('address_ar'),
            ],
        ];

        foreach ($translationFiles as $file => $translations) {
            foreach ($translations as $lang => $value) {
                // Skip if value is null or empty
                if ($value === null || trim($value) === '') {
                    Log::debug("Skipping empty translation for $lang/$file.php");
                    continue;
                }

                $relativePath = "$lang/$file.php";
                $oldKey = ($file === 'address') ? $oldAddressKey : $oldNameKey;
                $newKey = ($file === 'address') ? $newAddressKey : $newNameKey;

                Log::debug("Update translation $relativePath: oldKey=$oldKey, newKey=$newKey, value=" . substr($value, 0, 30));

                $this->addTranslationEntry($relativePath, $newKey, $value, $oldKey);
            }
        }
    }

    /**
     * Format Arabic text to handle line breaks properly for Linux production
     * Converts paragraph breaks to explicit \n characters
     */
    private function formatArabicText($text)
    {
        if (empty($text)) {
            return $text;
        }

        // Clean up any extra whitespace at the beginning or end
        $text = trim($text);
        
        // Replace multiple consecutive spaces/newlines with single line break
        $text = preg_replace('/\s*[\r\n]+\s*/', "\n", $text);
        
        // Clean up any remaining line breaks and normalize
        $text = str_replace(["\r\n", "\r"], "\n", $text);
        
        // Replace multiple newlines with single newline
        $text = preg_replace('/\n+/', "\n", $text);
        
        // Trim again after processing
        $text = trim($text);
        
        return $text;
    }

    /**
     * Safely adds a translation entry to an existing file without modifying existing entries
     * Only appends new entries while preserving the exact existing file structure
     */
    private function addTranslationEntry($relativePath, $newKey, $value, $oldKey = null)
    {
        $fullPath = lang_path($relativePath);

        // Only proceed if translation file already exists
        if (!File::exists($fullPath)) {
            Log::warning("Translation file does not exist: $fullPath. Skipping translation entry.");
            return false;
        }

        try {
            // Read the original file content as text
            $originalContent = File::get($fullPath);
            
            // Load existing translations to check if key exists
            $translations = include $fullPath;
            
            // Ensure we have an array
            if (!is_array($translations)) {
                Log::error("Invalid translation file format: $fullPath");
                return false;
            }

            // Check if we need to remove old key (only for updates)
            $needsOldKeyRemoval = $oldKey !== null && $oldKey !== $newKey && isset($translations[$oldKey]);
            
            // Check if new key already exists
            $keyExists = isset($translations[$newKey]);

            // If key exists and we're not updating, skip
            if ($keyExists && $oldKey === null) {
                Log::debug("Translation key already exists, skipping: $newKey");
                return true;
            }

            // If we need to remove old key or add new key, we need to rewrite the file
            if ($needsOldKeyRemoval || !$keyExists || $oldKey !== null) {
                
                // Remove old key if needed
                if ($needsOldKeyRemoval) {
                    unset($translations[$oldKey]);
                    Log::debug("Removed old translation key: $oldKey");
                }

                // Add new key
                $translations[$newKey] = $value;
                Log::debug("Added/updated translation key: $newKey");

                // Append to file while preserving structure
                $this->appendToTranslationFile($fullPath, $originalContent, $translations);
            }
            
            return true;

        } catch (\Exception $e) {
            Log::error("Error updating translation file $fullPath: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Appends new entries to translation file while preserving exact existing structure
     * Updates existing entries by replacing just the value, not the entire line
     */
    private function appendToTranslationFile($fullPath, $originalContent, $newTranslations)
    {
        // Load current translations
        $currentTranslations = include $fullPath;
        
        // Check if we only need to update specific existing keys
        $needsUpdate = false;
        foreach ($newTranslations as $key => $value) {
            if (!isset($currentTranslations[$key]) || $currentTranslations[$key] !== $value) {
                $needsUpdate = true;
                break;
            }
        }
        
        if (!$needsUpdate) {
            Log::debug("No translation updates needed");
            return;
        }
        
        // For files with [] syntax, try to preserve structure by only updating specific lines
        if (strpos($originalContent, 'return [') !== false) {
            $this->updateModernArraySyntax($fullPath, $originalContent, $newTranslations, $currentTranslations);
        } else {
            // For array() syntax files, merge and rewrite (existing working method)
            foreach ($newTranslations as $key => $value) {
                $currentTranslations[$key] = $value;
                Log::debug("Updated/added translation key: $key");
            }
            
            $this->writeTranslationFile($fullPath, $currentTranslations);
        }
    }

    /**
     * Update files with modern [] syntax while preserving structure
     */
    private function updateModernArraySyntax($fullPath, $originalContent, $newTranslations, $currentTranslations)
    {
        $lines = explode("\n", $originalContent);
        $newContent = '';
        $updated = [];
        
        foreach ($lines as $line) {
            $updatedLine = $line;
            
            // Check if this line contains a key we need to update
            foreach ($newTranslations as $key => $newValue) {
                if (in_array($key, $updated)) continue;
                
                $escapedKey = var_export($key, true);
                // Match the key at the start of the line (after whitespace)
                if (preg_match('/^\s*' . preg_quote($escapedKey, '/') . '\s*=>\s*/', $line)) {
                    // Replace the entire line with new value
                    if (is_string($newValue) && strpos($newValue, "\n") !== false) {
                        // For multiline strings, escape newlines properly and use double quotes
                        $escapedValue = str_replace(["\r\n", "\r", "\n"], '\n', $newValue);
                        $escapedValue = str_replace('"', '\"', $escapedValue);
                        $escapedValue = '"' . $escapedValue . '"';
                    } else {
                        $escapedValue = var_export($newValue, true);
                    }
                    $indentation = '';
                    if (preg_match('/^(\s*)/', $line, $matches)) {
                        $indentation = $matches[1];
                    }
                    $updatedLine = $indentation . $escapedKey . ' => ' . $escapedValue . ',';
                    $updated[] = $key;
                    Log::debug("Updated existing translation key: $key");
                    break;
                }
            }
            
            $newContent .= $updatedLine . "\n";
        }
        
        // Remove the last newline to avoid double newlines
        $newContent = rtrim($newContent, "\n");
        
        // Add any new keys that weren't found in the file
        $newKeys = [];
        foreach ($newTranslations as $key => $value) {
            if (!in_array($key, $updated) && !isset($currentTranslations[$key])) {
                $newKeys[$key] = $value;
            }
        }
        
        if (!empty($newKeys)) {
            // Find the position to insert new keys (before the closing ])
            $lastBracketPos = strrpos($newContent, '];');
            if ($lastBracketPos !== false) {
                $beforeClosing = substr($newContent, 0, $lastBracketPos);
                $afterClosing = substr($newContent, $lastBracketPos);
                
                // Add new keys with proper indentation
                foreach ($newKeys as $key => $value) {
                    $escapedKey = var_export($key, true);
                    if (is_string($value) && strpos($value, "\n") !== false) {
                        // For multiline strings, escape newlines properly and use double quotes
                        $escapedValue = str_replace(["\r\n", "\r", "\n"], '\n', $value);
                        $escapedValue = str_replace('"', '\"', $escapedValue);
                        $escapedValue = '"' . $escapedValue . '"';
                    } else {
                        $escapedValue = var_export($value, true);
                    }
                    $beforeClosing .= "\n    " . $escapedKey . ' => ' . $escapedValue . ',';
                    Log::debug("Added new translation key: $key");
                }
                
                $newContent = $beforeClosing . "\n" . $afterClosing;
            }
        }
        
        File::put($fullPath, $newContent);
    }

    /**
     * Writes translation array to file while preserving the original format
     * (Keeping this as fallback method)
     */
    private function writeTranslationFile($fullPath, $translations)
    {
        // Read the original file to preserve formatting style
        $originalContent = File::get($fullPath);
        
        // Check if original uses 'array(' or '[]' syntax
        $useArraySyntax = strpos($originalContent, 'array (') !== false || strpos($originalContent, 'array(') !== false;
        
        if ($useArraySyntax) {
            // Use var_export to maintain array() syntax
            $content = "<?php\n\nreturn " . var_export($translations, true) . ";\n";
        } else {
            // Use modern array syntax []
            $content = "<?php\n\nreturn " . $this->arrayToString($translations) . ";\n";
        }

        File::put($fullPath, $content);
    }

    /**
     * Convert array to string with modern [] syntax while preserving formatting
     * Properly escapes newlines in Arabic text
     */
    private function arrayToString($array, $indent = 0)
    {
        $indentStr = str_repeat('    ', $indent);
        $result = "[\n";
        
        foreach ($array as $key => $value) {
            $result .= $indentStr . '    ' . var_export($key, true) . ' => ';
            
            if (is_array($value)) {
                $result .= $this->arrayToString($value, $indent + 1);
            } else {
                // For string values, properly escape newlines
                if (is_string($value) && strpos($value, "\n") !== false) {
                    // Replace actual newlines with \n and escape quotes properly
                    $escapedValue = str_replace(["\r\n", "\r", "\n"], '\n', $value);
                    $escapedValue = str_replace('"', '\"', $escapedValue);
                    $result .= '"' . $escapedValue . '"';
                } else {
                    $result .= var_export($value, true);
                }
            }
            
            $result .= ",\n";
        }
        
        $result .= $indentStr . ']';
        return $result;
    }


    public function destroy($id)
    {
        try {
            $this->centreMedicalService->delete($id);
            return redirect()->route('medical-center')->with('success', 'Medical centre deleted successfully');
        } catch (\Exception $e) {
            return back()->withErrors('Failed to delete medical centre');
        }
    }

    public function getDoctors($id)
    {
        $medicalCenter = $this->centreMedicalService->get($id);
        $doctors = $this->doctorToCentreMedical->getDoctorsByCentre($id);

        return view('pages.admin.medicalcenter-doctor.index', compact('medicalCenter', 'doctors'));
    }

    public function allCentersWithDoctors()
    {
        $centres = $this->centreMedicalService->allWithDoctors();
        return view('pages.admin.medicalcenter-doctor.index', compact('centres'));
    }

    public function addDoctorToClinic(CreateDoctorToCentreMedical $request, $clinicId)
    {
        $validatedData = $request->validated();
        $doctorId = $validatedData['doctor_id'];
        $result = $this->doctorToCentreMedical->addDoctorToClinic($clinicId, $doctorId);
        if (isset($result['error'])) {
            return redirect()->back()->withErrors($result['error']);
        }
        return redirect()->route('medical-center.show', $clinicId)
            ->with('success', 'Doctor added to the clinic successfully!');
    }

    public function createDoctorToClinic(CreateDoctorToCentreMedical $request)
    {
        $validatedData = $request->validated();
        $centreId = $validatedData['centre_medical_id'];
        $doctorIds = $validatedData['doctor_id'];

        foreach ($doctorIds as $doctorId) {
            $this->doctorToCentreMedical->createDoctorToClinic($centreId, $doctorId);
        }

        return redirect()->route('centre-medical.doctors', $centreId)
            ->with('success', 'Doctors added to the clinic successfully!');
    }

    public function deleteDoctorFromClinic($centreMedicalId, $doctorId)
    {
        $this->doctorToCentreMedical->deleteDoctorFromClinic($centreMedicalId, $doctorId);
        return redirect()->route('centre-medical.doctors', $centreMedicalId)
            ->with('success', 'Doctor removed from the clinic successfully!');
    }

    public function fetchedDoctor()
    {
        $doctors = $this->doctorToCentreMedical->getDoctors();
        return response()->json(['doctors' => $doctors]);
    }

    public function feature(Request $request)
    {
        $this->centreMedicalService->feature($request->id);

        return response()->json(true);
    }

    public function unfeature(Request $request)
    {
        $this->centreMedicalService->unfeature($request->id);

        return response()->json(false);
}
}

<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class RedirectByRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if (Auth::check()) {
            switch (Auth::user()->role) {
                case 'doctor':
                    if (Auth::user()->doctor->offre == 'basic')
                        return redirect()->intended('/doctor/settings');
                    else
                        return redirect()->intended('/doctor/dashboard');

                case 'pharmacy':
                    return redirect()->intended('/pharmacy/dashboard');
                case 'admin':
                    return redirect()->intended('/admin');
                case 'assistante':
                    if (!Auth::user()->assistante->type)
                        return redirect()->intended('/assistante/doctors');
                    else
                    return redirect()->intended('/assistante/clinics');
            }
        }

        return $next($request);
    }
}

<?php

namespace App\Http\Controllers\Doctor;

use App\Http\Controllers\Controller;
use App\Services\PatientService\IPatientService;
use App\Services\MedicalRecord\IMedicalRecordService;
// use App\Http\Requests\Medicalrecord\Medicalrecord;
use App\Http\Requests\Medicalrecord\CreateMedicalRecordRequest;
use Illuminate\Support\Facades\Auth;
use App\Repositories\PrescriptionRepository;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Str;
use App\Models\MedicalForm;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;
use App\Models\Prescription;
use App\Models\Patient;
use Illuminate\Support\Facades\DB;
use App\Models\MedicalRecord;
use Illuminate\Support\Facades\Storage;


class MedicalRecordController extends Controller
{


    protected $patientService;
    protected $PrescriptionRepository;
    protected $medicalRecordService;

    public function __construct(IPatientService $patientService,PrescriptionRepository $PrescriptionRepository,IMedicalRecordService $medicalRecordService)
    {

        $this->patientService = $patientService;
        $this->PrescriptionRepository = $PrescriptionRepository;
        $this->medicalRecordService = $medicalRecordService;
    }


    public function storeMedicalForm(Request $request, $id)
    {
        Log::info('Form submission received', $request->all());

        try {
            $validatedData = $request->all();
            $validatedData['created_by'] = Auth::id();
            $validatedData['patient_id'] = $id;
            $validatedData['examination_date'] = now();
            $medicalForm = $this->medicalRecordService->storeMedicalForm($validatedData,$id);
            $medicalForm = $medicalForm->fresh();
            // Instead of downloading or saving, just redirect
            return redirect()->to("/doctor/patient/{$id}")
                ->with('success', 'Medical form saved successfully');

        } catch (\Exception $e) {
            Log::error('Error saving medical form', ['error' => $e->getMessage()]);
            return response()->json([
                'success' => false,
                'message' => 'Error saving form: ' . $e->getMessage()
            ], 500);
        }
    }

    // In MedicalRecordController.php
    public function index($id)
{
    $patient = $this->patientService->get($id);
    $loggedDoctorSpecialty = Auth::user()->doctor->Speciality->name;

    // Check if patient granted access to this doctor (any granted record unlocks dossiers UI)
    $hasPatientGrant = DB::table('medical_record_accesses')
        ->where('granted_by_patient_id', $patient->id)
        ->where('doctor_id', Auth::user()->doctor->id)
        ->exists();

    if (!$hasPatientGrant) {
        return response()->json([
            'authorized' => false,
            'message' => 'Le patient ne vous a pas accordé l\'accès aux dossiers.'
        ]);
    }

    // Check if doctor has authorized specialty
    $authorizedSpecialties = ['pulmonology', 'cardiologist'];
    if (!in_array($loggedDoctorSpecialty, $authorizedSpecialties)) {
        return response()->json([
            'authorized' => false,
            'message' => 'Les dossiers médicaux ne sont pas accessibles'
        ]);
    }

    $prescriptions = $this->PrescriptionRepository->getallpatientDoctorprescription($id, Auth::user()->doctor->id);
    $specialtyData = $loggedDoctorSpecialty;

    $medicalForms = MedicalForm::with(['prescription.doctor.user', 'prescription.doctor.speciality'])
        ->whereHas('prescription', function ($query) use ($prescriptions) {
            $query->whereIn('id', $prescriptions->pluck('id'));
        })
        ->orderBy('created_at', 'desc')
        ->paginate(10);

    // Attach a default document_type to support client-side filtering UI
    $medicalForms->setCollection(
        $medicalForms->getCollection()->map(function ($form) {
            $form->document_type = 'examen'; // Default categorization for medical forms
            return $form;
        })
    );

    // Fetch medical_records granted to this doctor for this patient
    $recordAccessRows = DB::table('medical_record_accesses as mra')
        ->join('medical_records as mr', 'mr.id', '=', 'mra.medical_record_id')
        ->where('mra.doctor_id', Auth::user()->doctor->id)
        ->where('mr.patient_id', $patient->id)
        ->orderByDesc('mr.created_at')
        ->select('mr.id','mr.title','mr.document_type','mr.created_at','mr.name')
        ->get();

    return response()->json([
        'authorized' => true,
        'patient' => $patient,
        'medicalForms' => $medicalForms->items(),
        'records' => $recordAccessRows,
        'pagination' => [
            'total' => $medicalForms->total(),
            'perPage' => $medicalForms->perPage(),
            'currentPage' => $medicalForms->currentPage(),
            'lastPage' => $medicalForms->lastPage()
        ],
        'loggedDoctorSpecialty' => $loggedDoctorSpecialty,
        'specialtyData' => $specialtyData
    ]);
}

    // View a file from medical_records if access granted
    public function viewRecordFile($recordId)
    {
        $doctorId = Auth::user()->doctor->id;
        $hasAccess = DB::table('medical_record_accesses')
            ->where('medical_record_id', $recordId)
            ->where('doctor_id', $doctorId)
            ->exists();
        if (!$hasAccess) {
            abort(403, 'Accès refusé.');
        }
        $record = MedicalRecord::findOrFail($recordId);
        $fullPath = storage_path('app/' . $record->file);
        if (!file_exists($fullPath)) {
            abort(404, 'Fichier introuvable');
        }
        $mime = \Illuminate\Support\Facades\File::mimeType($fullPath);
        return response()->file($fullPath, [
            'Content-Type' => $mime,
            'Content-Disposition' => 'inline; filename="' . basename($fullPath) . '"'
        ]);
    }

    public function downloadRecordFile($recordId)
    {
        $doctorId = Auth::user()->doctor->id;
        $hasAccess = DB::table('medical_record_accesses')
            ->where('medical_record_id', $recordId)
            ->where('doctor_id', $doctorId)
            ->exists();
        if (!$hasAccess) {
            abort(403, 'Accès refusé.');
        }
        $record = MedicalRecord::findOrFail($recordId);
        $fullPath = storage_path('app/' . $record->file);
        if (!file_exists($fullPath)) {
            abort(404, 'Fichier introuvable');
        }
        return response()->download($fullPath, basename($fullPath));
    }

    public function create ($id){
        $patient = $this->patientService->get($id);

        $loggedDoctorSpecialty = Auth::user()->doctor->Speciality->name;
        $specialtyData = $loggedDoctorSpecialty;

        return view( 'pages.user-accounts.doctor.patient-profile.tabs.dossier-medical.form', compact('patient','loggedDoctorSpecialty',
            'specialtyData'));
    }



    public function view($id)
    {
        try {
            // Get medical form with relations
            $medicalForm = $this->medicalRecordService->findWithRelations($id);
            // Get patient data
            $patient = $this->patientService->get($medicalForm->prescription->patient_id);
            // Get specialties
            $specialty = $medicalForm->prescription->doctor->speciality->name;
            $loggedDoctorSpecialty = Auth::user()->doctor->Speciality->name;
            // Return view with required data
            return view('pages.user-accounts.doctor.patient-profile.tabs.dossier-medical.form', [
                'patient' => $patient,
                'formData' => $medicalForm,  // Changed from $medicalForm->toArray()
                'loggedDoctorSpecialty' => $loggedDoctorSpecialty,
                'specialtyData' => $specialty,
                'isViewMode' => true  // Add this to indicate view mode
            ]);

        } catch (\Exception $e) {
            Log::error('Error viewing medical form', ['error' => $e->getMessage()]);
            return redirect()->back()->with('error', 'Error viewing medical form');
        }
    }


    public function download($id)
    {
        $medicalForm = $this->medicalRecordService->findWithRelations($id);

        $patient = $this->patientService->get($medicalForm->prescription->patient_id);
        $specialty = $medicalForm->prescription->doctor->speciality->name;
        $patientName = Str::slug($medicalForm->nom . '-' . $medicalForm->prenom);
        $date = date('Y-m-d-H-i-s');
        $pdf = PDF::loadView('pages.user-accounts.doctor.patient-profile.tabs.dossier-medical.dossier-pdf', [
            'patient' => $patient,
            'formData' => $medicalForm->toArray(),
            'loggedDoctorSpecialty' => $specialty
        ]);

        return $pdf->download("dossier-medical-{$patientName}-{$date}.pdf");
    }


    public function downloadPatientDossier($id)
    {
        $patient = Patient::findOrFail($id);
        $records = MedicalRecord::where('patient_id', $id)->get();

        // Generate PDF
        $pdf = PDF::loadView('doctor.patient-dossier-pdf', compact('patient', 'records'));
        $filename = 'dossier_' . Str::slug($patient->full_name) . '.pdf';
        return $pdf->download($filename);
    }
}

<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Doctor;
use App\Services\SMSService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class SmsController extends Controller
{
    protected $smsService;

    public function __construct(SMSService $smsService)
    {
        $this->smsService = $smsService;
    }

    public function index()
    {
        $targetDoctors = Doctor::whereHas('user', function ($query) {
            $query->where('account_status', 1)
                  ->where(function ($q) {
                      $q->where('phone', 'like', '06%')
                        ->orWhere('phone', 'like', '07%');
                  });
        })->with(['user' => function ($query) {
            $query->select('id', 'first_name', 'last_name', 'phone');
        }])->get(['id', 'user_id']);

        return view('pages.admin.Sms.index', [
            'targetDoctors' => $targetDoctors,
            'defaultMessage' => __('sms.automatic_message')
        ]);
    }

    public function sendBulkSms(Request $request)
    {
        $request->validate([
            'doctors' => 'required|array|min:1',
            'doctors.*' => 'exists:doctors,id',
        ]);

        $doctors = Doctor::whereIn('id', $request->input('doctors'))
            ->with(['user' => function ($query) {
                $query->select('id', 'first_name', 'last_name', 'phone');
            }])->get(['id', 'user_id']);

        $success = 0;
        $failed = 0;

        foreach ($doctors as $doctor) {
            $phone = $doctor->user->phone ?? null;
            $message = $this->prepareMessage($request->input('message'), $doctor->user->last_name ?? '');

            if ($phone && $this->smsService->isValidMoroccanPhone($phone)) {
                try {
                    $result = $this->smsService->send($phone, $message);
                    if ($result && isset($result['success']) && $result['success']) {
                        $success++;
                    } else {
                        $failed++;
                    }
                } catch (\Exception $e) {
                    Log::error("SMS sending failed for doctor {$doctor->id}: " . $e->getMessage());
                    $failed++;
                }
            } else {
                $failed++;
            }
        }

        return response()->json([
            'success' => true,
            'message' => __('sms.send_completed'),
            'success_count' => $success,
            'failed_count' => $failed,
        ]);
    }

    protected function prepareMessage(?string $userMessage, string $name): string
    {
        return $userMessage ?: str_replace(':name', $name, __('sms.automatic_message'));
    }

    public function sendSingleSms(Request $request)
    {
        $request->validate([
            'doctor_id' => 'required|exists:doctors,id',
            'message' => 'required|string|max:500',
        ]);

        $doctor = Doctor::with(['user' => function ($query) {
            $query->select('id', 'first_name', 'last_name', 'phone');
        }])->find($request->input('doctor_id'));

        if (!$doctor || !$doctor->user) {
            return response()->json([
                'success' => false,
                'message' => 'Doctor not found',
            ], 404);
        }

        $phone = $doctor->user->phone;
        $message = $request->input('message');

        if (!$phone || !$this->smsService->isValidMoroccanPhone($phone)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid phone number',
            ], 400);
        }

        try {
            $result = $this->smsService->send($phone, $message);
            
            if ($result && isset($result['success']) && $result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => __('sms.send_success'),
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => __('sms.send_error'),
                ], 500);
            }
        } catch (\Exception $e) {
            Log::error("SMS sending failed for doctor {$doctor->id}: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => __('sms.send_error'),
            ], 500);
        }
    }

    public function sendAllSms(Request $request)
    {
        $request->validate([
            'message' => 'required|string|max:500',
        ]);

        $doctors = Doctor::whereHas('user', function ($query) {
            $query->where('account_status', 1)
                  ->where(function ($q) {
                      $q->where('phone', 'like', '06%')
                        ->orWhere('phone', 'like', '07%');
                  });
        })->with(['user' => function ($query) {
            $query->select('id', 'first_name', 'last_name', 'phone');
        }])->get(['id', 'user_id']);

        $success = 0;
        $failed = 0;
        $message = $request->input('message');

        foreach ($doctors as $doctor) {
            $phone = $doctor->user->phone ?? null;
            
            if ($phone && $this->smsService->isValidMoroccanPhone($phone)) {
                try {
                    $result = $this->smsService->send($phone, $message);
                    if ($result && isset($result['success']) && $result['success']) {
                        $success++;
                    } else {
                        $failed++;
                    }
                } catch (\Exception $e) {
                    Log::error("SMS sending failed for doctor {$doctor->id}: " . $e->getMessage());
                    $failed++;
                }
            } else {
                $failed++;
            }
        }

        return response()->json([
            'success' => true,
            'message' => __('sms.send_completed'),
            'success_count' => $success,
            'failed_count' => $failed,
            'total_doctors' => $doctors->count(),
        ]);
    }
}

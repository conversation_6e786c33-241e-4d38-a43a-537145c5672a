<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\Speciality\CreateSpecialityRequest;
use App\Http\Requests\Speciality\UpdateSpecialityRequest;
use App\Repositories\SpecialityRepositoryInterface;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;

class SpecialityController extends Controller
{
    protected $specialityRepository;

    public function __construct(SpecialityRepositoryInterface $specialityRepository)
    {
        $this->specialityRepository = $specialityRepository;
    }

    public function index()
    {
        return view('pages.admin.specialities.index');
    }

    public function store(CreateSpecialityRequest $request)
    {
        $this->specialityRepository->store($request->validated());

        return redirect()->back()->with([
            'success' => trans("alerts.success.speciality_added_successfully")
        ]);
    }

    public function edit(Request $request)
    {
        $speciality = $this->specialityRepository->get($request->id);
        $speciality->name_fr = __('specialities.' . $speciality->name);
        return response()->json($speciality);
    }


    public function update(UpdateSpecialityRequest $request)
    {
        $this->specialityRepository->update(
            $request->validated(),
            $request->id
        );

        return redirect()->back()->with([
            'success' => trans("alerts.success.speciality_update_successfully")
        ]);
    }
}

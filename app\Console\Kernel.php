<?php

namespace App\Console;

use App\Http\Controllers\Admin\PaymentController;
use Carbon\Carbon;
use App\Repositories\DoctorRepository;
use App\Repositories\PaymentRepository;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->call(function () {
            $doctors = app(DoctorRepository::class)->all();

            foreach ($doctors as $doctor) {

                $lastPayment = $doctor->payments()->latest('month')->first();
                $nextMonth = Carbon::parse($lastPayment->month)->addMonth()->format('Y-m-d');

                app(PaymentRepository::class)->create([
                    'doctor_id' => $doctor->id,
                    'month' => $nextMonth,
                    'status' => 'no paid'
                ]);

            }
        })->monthlyOn(1, '00:00'); // Runs on the first day of each month at midnight        // Run the appointment reminder check every 5 minutes
        $schedule->command('appointments:send-reminders')
                ->everyFiveMinutes()
                ->withoutOverlapping()
                ->appendOutputTo(storage_path('logs/appointment-reminders.log'));
                
        // Update free appointment cache every minute for real-time availability
        $schedule->command('appointments:update-free-cache')
                ->everyMinute()
                ->withoutOverlapping()
                ->appendOutputTo(storage_path('logs/free-appointments-cache.log'));
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}

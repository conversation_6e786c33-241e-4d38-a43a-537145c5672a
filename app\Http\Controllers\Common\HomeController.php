<?php

namespace App\Http\Controllers\Common;

use Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Repositories\UserRepositoryInterface;
use App\Repositories\DoctorRepositoryInterface;
use App\Repositories\CentreMedicalRepositoryInterface;

class HomeController extends Controller
{

    protected $doctorRepository;
    protected $userRepository;
    protected $centreMedicalRepository;

    public function __construct(DoctorRepositoryInterface $doctorRepository,UserRepositoryInterface $userRepository,centreMedicalRepositoryInterface $centreMedicalRepository)
    {
        $this->doctorRepository = $doctorRepository;
        $this->userRepository = $userRepository;
        $this->centreMedicalRepository = $centreMedicalRepository;
    }

public function index()
    {
        $doctors = $this->doctorRepository->getFeatured();
        $centres = $this->centreMedicalRepository->getFeatured();

        // Translate doctor specialities
        $translatedDoctors = $doctors->map(function ($doctor) {
            if ($doctor->speciality) {
                $doctor->translated_speciality = __('specialities.' . $doctor->speciality->name, [], 'fr');
            }
            return $doctor;
        });
        // Fetch subspecialties with their parent specialties
        $sub_specialities = \App\Models\SubSpeciality::with('speciality')->get();

        return view('pages.home.index', compact(
            'doctors',
            'translatedDoctors',
            'centres',
            'sub_specialities',

        ));
    }

    
    public function DeletePicture()
    {
        try {
            $this->userRepository->deletePicture(Auth::user()->id);
            return response()->json(['message' => 'Picture deleted successfully']);
        } catch (\Exception $e) {

            return response()->json(['error' => 'Failed to delete picture'], 500);
        }
    }

}

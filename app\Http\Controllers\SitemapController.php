<?php

namespace App\Http\Controllers;

use App\Models\Doctor;
use App\Models\SubSpeciality;
use App\Models\CentreMedical;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Spatie\Sitemap\Sitemap;
use <PERSON><PERSON>\Sitemap\Tags\Url;

class SitemapController extends Controller
{
    /**
     * Generate XML sitemap
     */
    public function index()
    {
        return Cache::remember('sitemap.xml', now()->addHours(12), function () {
            $sitemap = Sitemap::create();

            // Add static pages
            $this->addStaticPages($sitemap);

            // Add dynamic content
            $this->addDoctorPages($sitemap);
            $this->addSubspecialityPages($sitemap);
            $this->addMedicalCenterPages($sitemap);
            $this->addSearchPages($sitemap);

            return response($sitemap->render(), 200)->header('Content-Type', 'application/xml');
        });
    }

    /**
     * Add static pages to sitemap
     */
    private function addStaticPages(Sitemap $sitemap)
    {
        $staticPages = [
            ['url' => '/', 'priority' => 1.0, 'changefreq' => Url::CHANGE_FREQUENCY_DAILY],
            ['url' => '/medecin', 'priority' => 0.9, 'changefreq' => Url::CHANGE_FREQUENCY_DAILY],
            ['url' => '/blog', 'priority' => 0.8, 'changefreq' => Url::CHANGE_FREQUENCY_WEEKLY],
            ['url' => '/specialities', 'priority' => 0.8, 'changefreq' => Url::CHANGE_FREQUENCY_WEEKLY],
            ['url' => '/clinique', 'priority' => 0.7, 'changefreq' => Url::CHANGE_FREQUENCY_WEEKLY],
            ['url' => '/contact', 'priority' => 0.7, 'changefreq' => Url::CHANGE_FREQUENCY_MONTHLY],
            ['url' => '/a-propos-de-Docexpress', 'priority' => 0.6, 'changefreq' => Url::CHANGE_FREQUENCY_MONTHLY],
        ];

        foreach ($staticPages as $page) {
            // Force HTTPS for static pages
            $httpsUrl = 'https://www.docexpress.ma' . $page['url'];

            $sitemap->add(
                Url::create($httpsUrl)
                    ->setLastModificationDate(now())
                    ->setChangeFrequency($page['changefreq'])
                    ->setPriority($page['priority'])
            );
        }
    }

    /**
     * Add doctor-related pages to sitemap
     * Only includes doctors with account_status = 1 (active accounts)
     */
    private function addDoctorPages(Sitemap $sitemap)
    {
        $addedUrls = [];

        // Only get ACTIVE doctors (account_status = 1) with complete, valid data
        $doctors = Doctor::with(['subspeciality', 'user', 'address', 'medicalCenters'])
            ->whereHas('user', function($query) {
                $query->where('account_status', 1)  // ONLY ACTIVE DOCTORS
                      ->whereNotNull('first_name')
                      ->whereNotNull('last_name')
                      ->where('first_name', '!=', '')
                      ->where('last_name', '!=', '')
                      ->where('first_name', 'not like', '%test%')
                      ->where('last_name', 'not like', '%test%');
            })
            ->whereHas('address', function($query) {
                $query->whereNotNull('city')
                      ->whereNotNull('district')
                      ->where('city', '!=', '')
                      ->where('district', '!=', '');
            })
            ->whereNotNull('subspeciality_id')
            ->whereHas('subspeciality')
            ->get();

        // Log the count of active doctors found
        Log::info('Sitemap: Found ' . $doctors->count() . ' active doctors (account_status = 1)');

        foreach ($doctors as $doctor) {
            try {
                if (!$this->isValidDoctor($doctor)) {
                    continue;
                }

                $fullName = trim($doctor->user->first_name . ' ' . $doctor->user->last_name);
                $fullNameSlug = Str::slug($fullName);

                $subspeciality = __('subspecialities.' . $doctor->subspeciality->name, [], 'fr');
                $subspecialitySlug = Str::slug($subspeciality);

                $citySlug = Str::slug($doctor->address->city);
                $districtSlug = Str::slug($doctor->address->district);

                // Skip if any slug is empty to prevent broken URLs
                if (empty($fullNameSlug) || empty($subspecialitySlug) || empty($citySlug) || empty($districtSlug)) {
                    Log::warning('Skipping doctor with empty slug', [
                        'doctor_id' => $doctor->id,
                        'slugs' => [$fullNameSlug, $subspecialitySlug, $citySlug, $districtSlug]
                    ]);
                    continue;
                }

                // Main doctor profile URL with city (highest priority)
                $profileUrl = "/profil-medecin/{$subspecialitySlug}/{$citySlug}/{$fullNameSlug}";
                $this->addUniqueUrl($sitemap, $profileUrl, $addedUrls, $doctor->updated_at, 0.8);

                // Add medical center variations only if doctor has multiple centers
                if ($doctor->medicalCenters && $doctor->medicalCenters->count() > 1) {
                    foreach ($doctor->medicalCenters as $clinic) {
                        $profileUrlWithClinic = "/profil-medecin/{$subspecialitySlug}/{$citySlug}/{$fullNameSlug}?lieu1-id={$clinic->id}";
                        $this->addUniqueUrl($sitemap, $profileUrlWithClinic, $addedUrls, $doctor->updated_at, 0.7);
                    }
                }

                // Add essential location-based URLs (reduce duplicates)
                $this->addLocationUrls($sitemap, $addedUrls, $citySlug, $subspecialitySlug, $doctor->updated_at);

            } catch (\Exception $e) {
                Log::error('Error processing doctor for sitemap', [
                    'doctor_id' => $doctor->id,
                    'error' => $e->getMessage()
                ]);
                continue;
            }
        }
    }

    /**
     * Add subspeciality pages to sitemap
     */
    private function addSubspecialityPages(Sitemap $sitemap)
    {
        $addedUrls = [];
        $languages = ['fr'];

        foreach (SubSpeciality::all() as $subspecialityItem) {
            foreach ($languages as $lang) {
                $translated = __('subspecialities.' . $subspecialityItem->name, [], $lang);
                $slug = Str::slug($translated);
                $searchUrl = "/medecin/{$slug}";

                $this->addUniqueUrl($sitemap, $searchUrl, $addedUrls, now(), 0.6);
            }
        }
    }

    /**
     * Add medical center pages to sitemap
     */
    private function addMedicalCenterPages(Sitemap $sitemap)
    {
        $medicalCenters = CentreMedical::with('address')->get();

        foreach ($medicalCenters as $center) {
            if (!empty($center->id)) {
                $centerUrl = "/centre/{$center->id}";
                // Force HTTPS for medical center URLs
                $httpsUrl = 'https://www.docexpress.ma' . $centerUrl;

                $sitemap->add(
                    Url::create($httpsUrl)
                        ->setLastModificationDate($center->updated_at)
                        ->setChangeFrequency(Url::CHANGE_FREQUENCY_WEEKLY)
                        ->setPriority(0.6)
                );
            }
        }
    }

    /**
     * Add search pages to sitemap
     * Generates URLs for city-only, specialty-city, and specialty-city-district combinations
     */
    private function addSearchPages(Sitemap $sitemap)
    {
        $addedUrls = [];

        // Get unique cities and districts from active doctors
        $activeDoctorsData = Doctor::with(['subspeciality', 'user', 'address'])
            ->whereHas('user', function($query) {
                $query->where('account_status', 1);
            })
            ->whereHas('address', function($query) {
                $query->whereNotNull('city')
                      ->whereNotNull('district')
                      ->where('city', '!=', '')
                      ->where('district', '!=', '');
            })
            ->whereNotNull('subspeciality_id')
            ->whereHas('subspeciality')
            ->get();

        // Extract unique combinations
        $cities = $activeDoctorsData->pluck('address.city')->unique()->filter();
        $subspecialities = SubSpeciality::all();

        // Group districts by city
        $cityDistricts = [];
        foreach ($activeDoctorsData as $doctor) {
            $city = $doctor->address->city;
            $district = $doctor->address->district;
            if (!isset($cityDistricts[$city])) {
                $cityDistricts[$city] = [];
            }
            if (!in_array($district, $cityDistricts[$city])) {
                $cityDistricts[$city][] = $district;
            }
        }

        // 1. Add city-only URLs: /medecin/casablanca
        foreach ($cities as $city) {
            $citySlug = Str::slug($city);
            if (!empty($citySlug)) {
                $cityUrl = "/medecin/{$citySlug}";
                $this->addUniqueUrl($sitemap, $cityUrl, $addedUrls, now(), 0.7);
            }
        }

        // 2. Add specialty-city URLs: /medecin/dentiste/casablanca
        foreach ($subspecialities as $subspeciality) {
            $subspecialityTranslated = __('subspecialities.' . $subspeciality->name, [], 'fr');
            $subspecialitySlug = Str::slug($subspecialityTranslated);

            if (!empty($subspecialitySlug)) {
                foreach ($cities as $city) {
                    $citySlug = Str::slug($city);
                    if (!empty($citySlug)) {
                        $specialtyCityUrl = "/medecin/{$subspecialitySlug}/{$citySlug}";
                        $this->addUniqueUrl($sitemap, $specialtyCityUrl, $addedUrls, now(), 0.8);
                    }
                }
            }
        }

        // 3. Add specialty-city-district URLs: /medecin/dentiste/casablanca/ain-borja
        foreach ($subspecialities as $subspeciality) {
            $subspecialityTranslated = __('subspecialities.' . $subspeciality->name, [], 'fr');
            $subspecialitySlug = Str::slug($subspecialityTranslated);

            if (!empty($subspecialitySlug)) {
                foreach ($cityDistricts as $city => $districts) {
                    $citySlug = Str::slug($city);
                    if (!empty($citySlug)) {
                        foreach ($districts as $district) {
                            $districtSlug = Str::slug($district);
                            if (!empty($districtSlug)) {
                                $fullUrl = "/medecin/{$subspecialitySlug}/{$citySlug}/{$districtSlug}";
                                $this->addUniqueUrl($sitemap, $fullUrl, $addedUrls, now(), 0.6);
                            }
                        }
                    }
                }
            }
        }

        // 4. Add city-district URLs: /medecin/casablanca/ain-borja
        foreach ($cityDistricts as $city => $districts) {
            $citySlug = Str::slug($city);
            if (!empty($citySlug)) {
                foreach ($districts as $district) {
                    $districtSlug = Str::slug($district);
                    if (!empty($districtSlug)) {
                        $cityDistrictUrl = "/medecin/{$citySlug}/{$districtSlug}";
                        $this->addUniqueUrl($sitemap, $cityDistrictUrl, $addedUrls, now(), 0.5);
                    }
                }
            }
        }

        Log::info('Sitemap: Added ' . count($addedUrls) . ' search page URLs');
    }

    /**
     * Check if doctor has all required data
     */
    private function isValidDoctor($doctor)
    {
        return !empty($doctor->subspeciality) &&
               !empty($doctor->subspeciality->name) &&
               !empty($doctor->user) &&
               !empty($doctor->user->first_name) &&
               !empty($doctor->user->last_name) &&
               !empty($doctor->address) &&
               !empty($doctor->address->city) &&
               !empty($doctor->address->district);
    }

    /**
     * Add URL to sitemap if not already added
     */
    private function addUniqueUrl(Sitemap $sitemap, string $url, array &$addedUrls, $lastModified, float $priority)
    {
        if (!in_array($url, $addedUrls)) {
            // Force HTTPS for all URLs
            $httpsUrl = 'https://www.docexpress.ma' . $url;

            $sitemap->add(
                Url::create($httpsUrl)
                    ->setLastModificationDate($lastModified)
                    ->setChangeFrequency(Url::CHANGE_FREQUENCY_WEEKLY)
                    ->setPriority($priority)
            );
            $addedUrls[] = $url;
        }
    }



    /**
     * Add location-based URLs
     */
    private function addLocationUrls(Sitemap $sitemap, array &$addedUrls, string $citySlug, string $subspecialitySlug, $lastModified)
    {
        $locationUrls = [
            "/medecin/{$citySlug}" => 0.5,
            "/medecin/{$subspecialitySlug}/{$citySlug}" => 0.6,
        ];

        foreach ($locationUrls as $url => $priority) {
            $this->addUniqueUrl($sitemap, $url, $addedUrls, $lastModified, $priority);
        }
    }

    /**
     * Debug method to show active doctors count
     */
    public function debugActiveDoctors()
    {
        $activeDoctors = Doctor::with(['user', 'address', 'subspeciality'])
            ->whereHas('user', function($query) {
                $query->where('account_status', 1);
            })
            ->get();

        $inactiveDoctors = Doctor::with(['user', 'address', 'subspeciality'])
            ->whereHas('user', function($query) {
                $query->where('account_status', 0);
            })
            ->get();

        $validActiveDoctors = $activeDoctors->filter(function($doctor) {
            return $this->isValidDoctor($doctor);
        });

        return response()->json([
            'total_doctors' => Doctor::count(),
            'active_doctors' => $activeDoctors->count(),
            'inactive_doctors' => $inactiveDoctors->count(),
            'valid_active_doctors' => $validActiveDoctors->count(),
            'active_doctors_sample' => $activeDoctors->take(5)->map(function($doctor) {
                return [
                    'id' => $doctor->id,
                    'name' => $doctor->user->first_name . ' ' . $doctor->user->last_name,
                    'account_status' => $doctor->user->account_status,
                    'city' => $doctor->address->city ?? 'N/A',
                    'subspeciality' => $doctor->subspeciality->name ?? 'N/A'
                ];
            })
        ]);
    }

    /**
     * Debug method to show sample search URLs that will be added to sitemap
     */
    public function debugSearchUrls()
    {
        $activeDoctorsData = Doctor::with(['subspeciality', 'user', 'address'])
            ->whereHas('user', function($query) {
                $query->where('account_status', 1);
            })
            ->whereHas('address', function($query) {
                $query->whereNotNull('city')
                      ->whereNotNull('district')
                      ->where('city', '!=', '')
                      ->where('district', '!=', '');
            })
            ->whereNotNull('subspeciality_id')
            ->whereHas('subspeciality')
            ->take(10)
            ->get();

        $cities = $activeDoctorsData->pluck('address.city')->unique()->take(3);
        $subspecialities = SubSpeciality::take(3)->get();

        $sampleUrls = [];

        // Sample city-only URLs
        foreach ($cities as $city) {
            $citySlug = Str::slug($city);
            $sampleUrls[] = "/medecin/{$citySlug}";
        }

        // Sample specialty-city URLs
        foreach ($subspecialities as $subspeciality) {
            $subspecialityTranslated = __('subspecialities.' . $subspeciality->name, [], 'fr');
            $subspecialitySlug = Str::slug($subspecialityTranslated);
            foreach ($cities->take(2) as $city) {
                $citySlug = Str::slug($city);
                $sampleUrls[] = "/medecin/{$subspecialitySlug}/{$citySlug}";
            }
        }

        // Sample specialty-city-district URLs
        foreach ($activeDoctorsData->take(5) as $doctor) {
            $subspecialityTranslated = __('subspecialities.' . $doctor->subspeciality->name, [], 'fr');
            $subspecialitySlug = Str::slug($subspecialityTranslated);
            $citySlug = Str::slug($doctor->address->city);
            $districtSlug = Str::slug($doctor->address->district);
            $sampleUrls[] = "/medecin/{$subspecialitySlug}/{$citySlug}/{$districtSlug}";
        }

        return response()->json([
            'message' => 'Sample search URLs that will be added to sitemap',
            'total_active_doctors' => $activeDoctorsData->count(),
            'unique_cities' => $cities->count(),
            'total_subspecialities' => SubSpeciality::count(),
            'sample_urls' => array_unique($sampleUrls),
            'full_urls' => array_map(function($url) {
                return 'https://www.docexpress.ma' . $url;
            }, array_unique($sampleUrls))
        ]);
    }
}

<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Services\OTP\OTPService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class OTPVerificationController extends Controller
{
    protected $otpService;

    public function __construct(OTPService $otpService)
    {
        $this->otpService = $otpService;
    }

    /**
     * Show the OTP verification form.
     *
     * @return \Illuminate\View\View
     */
    public function show()
    {
        Log::info('OTP verification page accessed', [
            'url' => request()->fullUrl(),
            'session_has_user_for_otp' => session()->has('user_for_otp'),
            'user_id' => session('user_for_otp'),
            'ip' => request()->ip()
        ]);
        
        return view('pages.auth.verify-otp');
    }

    /**
     * Verify the OTP entered by user
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function verify(Request $request)
    {
        $request->validate([
            'otp' => 'required|numeric|digits:6',
            'user_id' => 'required|exists:users,id'
        ]);

        Log::info('OTP verification attempt', [
            'user_id' => $request->user_id,
            'otp_length' => strlen($request->otp),
            'url' => request()->fullUrl(),
            'ip' => request()->ip()
        ]);

        $user = User::find($request->user_id);

        if (!$user) {
            Log::warning('OTP verification failed - user not found', [
                'user_id' => $request->user_id
            ]);
            
            return back()->withErrors(['otp' => 'User not found']);
        }

        if ($user->otp != $request->otp) {
            Log::warning('OTP verification failed - invalid OTP', [
                'user_id' => $request->user_id,
                'entered_otp' => $request->otp,
                'expected_otp' => $user->otp
            ]);
            
            return back()->withErrors(['otp' => 'Invalid OTP']);
        }

        if (Carbon::now()->isAfter(Carbon::parse($user->otp_expires_at))) {
            Log::warning('OTP verification failed - OTP expired', [
                'user_id' => $request->user_id,
                'otp_expires_at' => $user->otp_expires_at,
                'current_time' => Carbon::now()
            ]);
            
            return back()->withErrors(['otp' => 'OTP expired']);
        }

        // Mark user as verified
        $user->phone_verified_at = Carbon::now();
        $user->otp = null;
        $user->otp_expires_at = null;
        $user->save();

        Log::info('OTP verification successful', [
            'user_id' => $user->id,
            'phone' => $user->phone,
            'phone_verified_at' => $user->phone_verified_at
        ]);

        // Log in the user
        Auth::login($user);

        Log::info('User authenticated after OTP verification', [
            'user_id' => $user->id,
            'redirect_to' => session()->has('url.intended') ? session('url.intended') : '/dashboard'
        ]);

        return redirect()->intended('/dashboard');
    }

    /**
     * Resend OTP to user's phone
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function resend(Request $request)
    {
        Log::info('OTP resend request', [
            'user_id' => $request->user_id,
            'url' => request()->fullUrl(),
            'ip' => request()->ip()
        ]);
        
        $user = User::find($request->user_id);

        if (!$user) {
            Log::warning('OTP resend failed - user not found', [
                'user_id' => $request->user_id
            ]);
            
            return back()->withErrors(['otp' => 'User not found']);
        }

        // Throttling to prevent abuse
        if ($user->otp_expires_at && Carbon::parse($user->otp_expires_at)->diffInMinutes(Carbon::now()) < 1) {
            Log::warning('OTP resend throttled', [
                'user_id' => $user->id,
                'last_request' => $user->otp_expires_at,
                'time_elapsed' => Carbon::parse($user->otp_expires_at)->diffInMinutes(Carbon::now())
            ]);
            
            return back()->withErrors(['otp' => 'Please wait before requesting another OTP']);
        }

        $otp = random_int(100000, 999999);
        $user->otp = $otp;
        $user->otp_expires_at = Carbon::now()->addMinutes(5);
        $user->save();

        Log::info('New OTP generated', [
            'user_id' => $user->id,
            'expires_at' => $user->otp_expires_at
        ]);

        // Send OTP via SMS API
        $result = $this->otpService->sendOTP($user->phone, $otp);
        
        Log::info('OTP resend result', [
            'user_id' => $user->id,
            'success' => $result
        ]);

        return back()->with('status', 'A new OTP has been sent to your phone');
    }
}

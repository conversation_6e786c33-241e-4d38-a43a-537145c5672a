<?php

namespace App\Http\Controllers\Pharmacy;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Repositories\MedicationRepository;
use App\Services\PharmacyService\IPharmacyService;
use App\Http\Requests\Pharmacy\UpdateAvailableTimeRequest;

class PharmacyActionsController extends Controller
{

    protected $medicationRepository;
    protected $PharmacyService;

    public function __construct(MedicationRepository $medicationRepository,IPharmacyService $PharmacyService)
    {
        $this->medicationRepository = $medicationRepository;
        $this->PharmacyService = $PharmacyService;
    }
    public function DeleveredOrCancelDelevredMedication($medication_id)
    {

        $medication = $this->medicationRepository->get($medication_id);
        if($medication->status != "taken")
        {
            $medication->status='taken';
            $medication->pharmacy_id=Auth::user()->pharmacy->id;
            $this->medicationRepository->update($medication,$medication_id);
            return redirect()->back()->with('success',trans('pharmacy.medication_has_delivered'));
        }
        else
        {
            $medication = $this->medicationRepository->get($medication_id);
            $medication->status='Not taken';
            $medication->pharmacy_id=null;
            $this->medicationRepository->update($medication,$medication_id);
            return redirect()->back()->with('success',trans('pharmacy.medication_has_canceled'));
        }

    }
    public function EditAvailableTimes()
    {
        $pharmacy = $this->PharmacyService->get(Auth::user()->pharmacy->id);
        return view('pages.user-accounts.pharmacy.available-times',compact('pharmacy'));
    }
    public function UpdateAvailableTimes(UpdateAvailableTimeRequest $request)
    {
        $data = $request->validated();
        $pharmacyUpdated = $this->PharmacyService->updateAvailableTimes($data, Auth::id());
        return redirect()->back()->with([
            'success' => trans("alerts.success.your_information_have_been_saved_successfully"),
            'pharmacy'=> $pharmacyUpdated
        ]);
    }
}


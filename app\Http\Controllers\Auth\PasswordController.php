<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;

class PasswordController extends Controller
{

    public function index()
    {
        if (auth()->user()->role == 'assistante') {
            return view('pages.user-accounts.assistante-administrative.change-password');
        }
        if (auth()->user()->role == 'centre_medical') {
            return view('pages.user-accounts.centreMedical.change-password');
        }
        return view('pages.user-accounts.doctor.change-password');
    }
    /**
     * Update the user's password.
     */
    public function update(Request $request): RedirectResponse
    {

        $validated = $request->validateWithBag('updatePassword', [
            'current_password' => ['required', 'current_password'],
            'password' => ['required', Password::defaults(), 'confirmed'],
        ]);

        $request->user()->update([
            'password' => Hash::make($validated['password']),
        ]);

        return back()->with(
            'success',
            trans("alerts.success.password_updated")
        );
    }
}

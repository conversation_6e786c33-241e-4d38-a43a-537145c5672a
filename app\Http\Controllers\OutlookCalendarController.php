<?php

namespace App\Http\Controllers;

use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\CalendarAccount;
use Carbon\Carbon;

class OutlookCalendarController extends Controller
{
    public function index()
    {
        $events = []; // Ici tu peux injecter les événements de ta DB si besoin
        return view('pages.user-accounts.doctor.calendar.calendar', compact('events'));
    }

    // Redirection OAuth2 vers Microsoft
    public function redirectToOutlook()
    {
        $user = Auth::user();
        if (!$user) {
            return redirect()->route('login')->with('error', 'Vous devez être connecté pour accéder au calendrier.');
        }

        $query = http_build_query([
            'client_id' => env('AZURE_CLIENT_ID'),
            'response_type' => 'code',
            'redirect_uri' => env('AZURE_REDIRECT_URI'),
            'response_mode' => 'query',
            'scope' => 'openid offline_access User.Read Calendars.ReadWrite',
            'state' => encrypt($user->id),
        ]);

        return redirect("https://login.microsoftonline.com/common/oauth2/v2.0/authorize?{$query}");
    }

    // Callback OAuth2 → récupération et sauvegarde du token
    public function handleOutlookCallback(Request $request)
    {
        $user = Auth::user();
        if (!$user) {
            return redirect()->route('login')->with('error', 'Vous devez être connecté.');
        }

        if ($request->has('error')) {
            $errorDescription = $request->get('error_description');
            Log::error('OAuth Error', ['description' => $errorDescription]);
            return redirect()->route('doctor.calendar')
                ->with('error', "Erreur d'authentification: {$errorDescription}");
        }

        if (!$request->has('code')) {
            Log::error('OAuth Callback missing code');
            return redirect()->route('doctor.calendar')
                ->with('error', 'Erreur: Code d\'autorisation manquant');
        }

        try {
            $code = $request->get('code');
            $client = new Client(['timeout' => 30, 'verify' => false]);

            $response = $client->post('https://login.microsoftonline.com/common/oauth2/v2.0/token', [
                'form_params' => [
                    'client_id' => env('AZURE_CLIENT_ID'),
                    'client_secret' => env('AZURE_CLIENT_SECRET'),
                    'code' => $code,
                    'redirect_uri' => env('AZURE_REDIRECT_URI'),
                    'grant_type' => 'authorization_code',
                ],
            ]);

            $data = json_decode($response->getBody(), true);

            if (!isset($data['access_token'])) {
                Log::error('Token response missing access_token', ['response' => $data]);
                return redirect()->route('doctor.calendar')
                    ->with('error', 'Erreur: Token d\'accès manquant dans la réponse');
            }

            // Check if DocExpress calendar already exists, if not create it
            $calendarId = null;
            
            try {
                $client = new Client(['timeout' => 30, 'verify' => false]);
                
                // List all calendars to find existing DocExpress calendar
                $calendarResponse = $client->get('https://graph.microsoft.com/v1.0/me/calendars', [
                    'headers' => [
                        'Authorization' => 'Bearer ' . $data['access_token'],
                        'Accept' => 'application/json',
                    ],
                ]);
                
                $calendarsData = json_decode($calendarResponse->getBody(), true);
                $existingCalendar = null;
                
                foreach ($calendarsData['value'] as $calendar) {
                    if ($calendar['name'] === 'DocExpress') {
                        $existingCalendar = $calendar;
                        break;
                    }
                }
                
                if ($existingCalendar) {
                    // Use existing DocExpress calendar
                    $calendarId = $existingCalendar['id'];
                    Log::info('Found existing DocExpress calendar for user', [
                        'user_id' => $user->id,
                        'calendar_id' => $calendarId
                    ]);
                } else {
                    // Create new DocExpress calendar
                    $createCalendarResponse = $client->post('https://graph.microsoft.com/v1.0/me/calendars', [
                        'headers' => [
                            'Authorization' => 'Bearer ' . $data['access_token'],
                            'Accept' => 'application/json',
                            'Content-Type' => 'application/json',
                        ],
                        'json' => [
                            'name' => 'DocExpress',
                            'color' => 'lightBlue'
                        ],
                    ]);
                    
                    $createdCalendar = json_decode($createCalendarResponse->getBody(), true);
                    $calendarId = $createdCalendar['id'];
                    Log::info('Created new DocExpress calendar for user', [
                        'user_id' => $user->id,
                        'calendar_id' => $calendarId
                    ]);
                }
                
            } catch (\Exception $e) {
                Log::error('Error managing DocExpress calendar for Outlook', [
                    'user_id' => $user->id,
                    'message' => $e->getMessage()
                ]);
                $calendarId = null; // Use default calendar
            }

            // Sauvegarde DB
            $this->saveOutlookAccount(
                $user,
                $data['access_token'],
                $data['refresh_token'] ?? null,
                $data['expires_in'] ?? 3600,
                $calendarId
            );

            // Sauvegarde session
            session([
                'outlook_access_token' => $data['access_token'],
                'outlook_refresh_token' => $data['refresh_token'] ?? null,
                'outlook_token_expires' => now()->addSeconds($data['expires_in'] ?? 3600),
            ]);

            Log::info('OAuth authentication successful');
            return redirect()->route('doctor.calendar')->with('success', 'Connexion à Outlook réussie !');

        } catch (\Exception $e) {
            Log::error('OAuth General Error', ['message' => $e->getMessage()]);
            return redirect()->route('doctor.calendar')
                ->with('error', 'Erreur inattendue: ' . $e->getMessage());
        }
    }

    private function saveOutlookAccount($user, $accessToken, $refreshToken, $expiresIn, $calendarId = null)
    {
        return CalendarAccount::updateOrCreate(
            [
                'user_id' => $user->id,
                'provider' => 'outlook',
            ],
            [
                'email' => $user->email,
                'access_token' => $accessToken,
                'refresh_token' => $refreshToken,
                'token_expiry' => now()->addSeconds($expiresIn),
                'external_calendar_id' => $calendarId,
            ]
        );
    }

 private function refreshAccessToken($userId)
{
    Log::info('Attempting to refresh access token', ['user_id' => $userId]);
    
    // Récupérer les infos en DB
    $account = DB::table('calendar_accounts')
        ->where('user_id', $userId)
        ->where('provider', 'outlook')
        ->first();

    if (!$account || !$account->refresh_token) {
        Log::error('No refresh token found for user', [
            'user_id' => $userId,
            'account_exists' => !!$account,
            'has_refresh_token' => $account ? !empty($account->refresh_token) : false
        ]);
        return false;
    }

    try {
        $client = new \GuzzleHttp\Client(['timeout' => 30, 'verify' => false]);
        
        $requestData = [
            'client_id'     => env('AZURE_CLIENT_ID'),
            'client_secret' => env('AZURE_CLIENT_SECRET'),
            'grant_type'    => 'refresh_token',
            'refresh_token' => $account->refresh_token,
            'scope'         => 'https://graph.microsoft.com/.default offline_access',
        ];
        
        Log::info('Making refresh token request', [
            'user_id' => $userId,
            'client_id' => $requestData['client_id'],
            'has_refresh_token' => !empty($requestData['refresh_token'])
        ]);
        
        $response = $client->post('https://login.microsoftonline.com/common/oauth2/v2.0/token', [
            'form_params' => $requestData,
        ]);

        $data = json_decode($response->getBody(), true);
        
        Log::info('Refresh token response received', [
            'user_id' => $userId,
            'has_access_token' => !empty($data['access_token']),
            'has_refresh_token' => !empty($data['refresh_token']),
            'expires_in' => $data['expires_in'] ?? 'not set'
        ]);

        if (!empty($data['access_token'])) {
            // Mise à jour en DB
            $updateData = [
                'access_token' => $data['access_token'],
                'refresh_token' => $data['refresh_token'] ?? $account->refresh_token,
                'token_expiry' => now()->addSeconds($data['expires_in'] ?? 3600),
                'updated_at'   => now(),
            ];
            
            DB::table('calendar_accounts')
                ->where('id', $account->id)
                ->update($updateData);

            Log::info('Successfully refreshed and updated access token', [
                'user_id' => $userId,
                'new_expiry' => $updateData['token_expiry']->toDateTimeString()
            ]);

            return $data['access_token'];
        }

        Log::error('Refresh token failed: no access token in response', [
            'user_id' => $userId,
            'response_keys' => array_keys($data)
        ]);
        return false;

    } catch (\GuzzleHttp\Exception\ClientException $e) {
        $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 'unknown';
        $responseBody = $e->getResponse() ? $e->getResponse()->getBody()->getContents() : 'no response';
        
        Log::error('Token refresh client error', [
            'user_id' => $userId,
            'status_code' => $statusCode,
            'response_body' => $responseBody,
            'message' => $e->getMessage()
        ]);
        
        // If refresh token is invalid, clear it
        if ($statusCode == 400) {
            $this->clearTokens($userId);
        }
        
        return false;
        
    } catch (\Exception $e) {
        Log::error('Token refresh general error', [
            'user_id' => $userId,
            'message' => $e->getMessage(),
            'code' => $e->getCode()
        ]);
        return false;
    }
}


    public function getValidToken($userId)
{
    Log::info('Getting valid token for user', ['user_id' => $userId]);
    
    $account = DB::table('calendar_accounts')
        ->where('user_id', $userId)
        ->where('provider', 'outlook')
        ->first();

    if (!$account) {
        Log::error('No calendar account found for user', ['user_id' => $userId]);
        return null;
    }
    
    Log::info('Found calendar account', [
        'user_id' => $userId,
        'account_id' => $account->id,
        'has_access_token' => !empty($account->access_token),
        'has_refresh_token' => !empty($account->refresh_token),
        'token_expiry' => $account->token_expiry,
        'expires_at' => $account->token_expiry ? Carbon::parse($account->token_expiry)->toDateTimeString() : 'null',
        'is_expired' => $account->token_expiry ? now()->greaterThan($account->token_expiry) : 'unknown'
    ]);

    // Check if current token is valid
    if ($account->token_expiry && now()->lessThan($account->token_expiry) && !empty($account->access_token)) {
        Log::info('Using existing valid token', ['user_id' => $userId]);
        return $account->access_token;
    }

    // Token is expired or missing, try to refresh
    Log::info('Token expired or missing, attempting refresh', ['user_id' => $userId]);
    $newToken = $this->refreshAccessToken($userId);
    
    if ($newToken) {
        Log::info('Successfully refreshed token', ['user_id' => $userId]);
        return $newToken;
    }
    
    Log::error('Failed to refresh token', ['user_id' => $userId]);
    return null;
}

    private function clearTokens($userId)
    {
        Log::info('Clearing tokens for user', ['user_id' => $userId]);
        
        // Delete the calendar account record instead of updating tokens to null
        DB::table('calendar_accounts')
            ->where('user_id', $userId)
            ->where('provider', 'outlook')
            ->delete();
            
        session()->forget(['outlook_access_token','outlook_refresh_token','outlook_token_expires']);
    }

    /**
     * Format appointment date for external calendar with proper timezone handling
     * This method ensures that appointment times are never converted but are properly
     * formatted with the application timezone
     */
    private function formatAppointmentDateForCalendar($appointmentDate)
    {
        // Parse the appointment date assuming it's already in our app timezone
        $parsed = Carbon::parse($appointmentDate, config('app.timezone'));
        
        // Ensure the timezone is explicitly set to our app timezone
        // This prevents any automatic timezone conversion
        $parsed->setTimezone(config('app.timezone'));
        
        return $parsed;
    }

    /**
     * Get the application timezone for calendar operations
     */
    private function getAppTimezone()
    {
        return config('app.timezone');
    }

    // Récupération des événements Outlook
    public function getOutlookEvents($userId = null)
    {
        $userId = $userId ?? auth()->id();
        Log::info('Getting Outlook events for user', ['user_id' => $userId]);
        
        $token = $this->getValidToken($userId);
        
        if (!$token) {
            Log::error('No valid token found for user', ['user_id' => $userId]);
            return response()->json(['error' => 'Authentication required'], 401);
        }
        
        // Debug: Log token info (only log first and last few characters for security)
        $tokenStart = substr($token, 0, 10);
        $tokenEnd = substr($token, -10);
        Log::info('Using token for API call', [
            'user_id' => $userId,
            'token_start' => $tokenStart,
            'token_end' => $tokenEnd,
            'token_length' => strlen($token)
        ]);

        try {
            $client = new Client(['timeout' => 30, 'verify' => false]);
            $response = $client->get('https://graph.microsoft.com/v1.0/me/events', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $token,
                    'Accept' => 'application/json',
                    'Prefer' => 'outlook.timezone="' . config('app.timezone') . '"',
                ],
                'query' => [
                    '$select' => 'subject,start,end,bodyPreview,isAllDay',
                    '$orderby' => 'start/dateTime',
                    '$top' => 50,
                ],
            ]);

            $events = json_decode($response->getBody(), true);
            $formatted = [];

            foreach ($events['value'] as $event) {
                $formatted[] = [
                    'id' => $event['id'],
                    'title' => $event['subject'] ?? 'Sans titre',
                    'start' => $event['start']['dateTime'] ?? $event['start']['date'],
                    'end' => $event['end']['dateTime'] ?? $event['end']['date'],
                    'allDay' => $event['isAllDay'] ?? false,
                    'provider' => 'outlook',
                    'status' => 'from_outlook',
                    'description' => $event['bodyPreview'] ?? '',
                ];
            }

            Log::info('Successfully retrieved Outlook events', ['count' => count($formatted)]);
            return response()->json($formatted);

        } catch (\GuzzleHttp\Exception\ClientException $e) {
            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 'unknown';
            $responseBody = $e->getResponse() ? $e->getResponse()->getBody()->getContents() : 'no response';
            
            Log::error('Outlook Calendar API Error', [
                'user_id' => $userId,
                'status_code' => $statusCode,
                'response_body' => $responseBody,
                'message' => $e->getMessage(),
                'token_start' => substr($token, 0, 10),
                'token_end' => substr($token, -10),
                'token_length' => strlen($token),
                'note' => 'Microsoft Graph tokens are opaque tokens, not JWTs - the "JWT not well formed" error means the token is invalid/expired'
            ]);
            
            // If it's an authentication error, try to refresh the token once more
            if ($statusCode == 401) {
                Log::info('Received 401 error, attempting token refresh', ['user_id' => $userId]);
                
                $refreshedToken = $this->refreshAccessToken($userId);
                if ($refreshedToken) {
                    Log::info('Token refreshed successfully, retrying API call', ['user_id' => $userId]);
                    // Retry the API call with the new token
                    try {
                        $retryResponse = $client->get('https://graph.microsoft.com/v1.0/me/events', [
                            'headers' => [
                                'Authorization' => 'Bearer ' . $refreshedToken,
                                'Accept' => 'application/json',
                                'Prefer' => 'outlook.timezone="' . config('app.timezone') . '"',
                            ],
                            'query' => [
                                '$select' => 'subject,start,end,bodyPreview,isAllDay',
                                '$orderby' => 'start/dateTime',
                                '$top' => 50,
                            ],
                        ]);
                        
                        $events = json_decode($retryResponse->getBody(), true);
                        $formatted = [];

                        foreach ($events['value'] as $event) {
                            $formatted[] = [
                                'id' => $event['id'],
                                'title' => $event['subject'] ?? 'Sans titre',
                                'start' => $event['start']['dateTime'] ?? $event['start']['date'],
                                'end' => $event['end']['dateTime'] ?? $event['end']['date'],
                                'allDay' => $event['isAllDay'] ?? false,
                                'provider' => 'outlook',
                                'status' => 'from_outlook',
                                'description' => $event['bodyPreview'] ?? '',
                            ];
                        }

                        Log::info('Retry successful after token refresh', ['count' => count($formatted)]);
                        return response()->json($formatted);
                        
                    } catch (\Exception $retryError) {
                        Log::error('Retry failed even after token refresh', [
                            'user_id' => $userId,
                            'retry_error' => $retryError->getMessage()
                        ]);
                    }
                }
                
                // If refresh failed or retry failed, then clear tokens
                Log::warning('Token refresh failed or retry failed, clearing tokens', ['user_id' => $userId]);
                $this->clearTokens($userId);
                session()->forget(['outlook_access_token','outlook_refresh_token','outlook_token_expires']);
            }
            
            return response()->json(['error' => 'Authentication expired', 'details' => $responseBody], 401);
            
        } catch (\Exception $e) {
            Log::error('Outlook API General Error', [
                'user_id' => $userId,
                'message' => $e->getMessage(),
                'token_start' => substr($token, 0, 10),
                'token_end' => substr($token, -10)
            ]);
            return response()->json(['error' => 'API request failed'], 500);
        }
    }

    // Ajouter un événement Outlook
    public function addOutlookEvent(Request $request)
    {
        $userId = auth()->id();
        $token = $this->getValidToken($userId);
        if (!$token) return response()->json(['error' => 'Authentication required'], 401);

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'start' => 'required|date',
            'end' => 'required|date|after:start',
        ]);

        try {
            $client = new Client();
            
            // Format dates with proper timezone
            $startDate = $this->formatAppointmentDateForCalendar($validated['start']);
            $endDate = $this->formatAppointmentDateForCalendar($validated['end']);
            
            $eventData = [
                "subject" => $validated['title'],
                "start" => [
                    "dateTime" => $startDate->format('Y-m-d\TH:i:s.000'), 
                    "timeZone" => $this->getAppTimezone()
                ],
                "end" => [
                    "dateTime" => $endDate->format('Y-m-d\TH:i:s.000'), 
                    "timeZone" => $this->getAppTimezone()
                ]
            ];

            // Get user's calendar account to use the selected calendar
            $calendarAccount = auth()->user()->calendarAccounts()->where('provider', 'outlook')->first();
            $calendarId = $calendarAccount ? $calendarAccount->external_calendar_id : null;
            
            $eventsEndpoint = $calendarId 
                ? "https://graph.microsoft.com/v1.0/me/calendars/{$calendarId}/events"
                : 'https://graph.microsoft.com/v1.0/me/events';

            $response = $client->post($eventsEndpoint, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $token, 
                    'Content-Type' => 'application/json',
                    'Prefer' => 'outlook.timezone="' . config('app.timezone') . '"',
                ],
                'json' => $eventData,
            ]);

            $createdEvent = json_decode($response->getBody(), true);
            return response()->json(['success' => true, 'event' => $createdEvent]);

        } catch (\Exception $e) {
            Log::error('Add Outlook Event Failed', ['message' => $e->getMessage()]);
            return response()->json(['error' => 'Failed to create event'], 500);
        }
    }

    // Synchroniser tes rendez-vous locaux → Outlook
    public function syncOutlookEvents(Request $request)
    {
        Log::info('Starting Outlook sync for user', ['user_id' => auth()->id()]);
        
        $userId = auth()->id();
        $token = $this->getValidToken($userId);
        
        if (!$token) {
            Log::error('No valid token for sync', ['user_id' => $userId]);
            return response()->json(['success' => false, 'error' => 'Authentication required'], 401);
        }

        // Debug: Check what type of user this is and what appointments exist
        $user = auth()->user();
        Log::info('User sync debug', [
            'user_id' => $userId,
            'user_type' => get_class($user),
            'user_email' => $user->email ?? 'no email'
        ]);

        // Check if this user has a doctor profile
        $doctor = \App\Models\Doctor::where('user_id', auth()->id())->first();
        if ($doctor) {
            Log::info('Found doctor profile for user', [
                'doctor_id' => $doctor->id,
                'user_id' => $userId
            ]);
            
            // Use the doctor's ID to find appointments
            $appointments = \App\Models\Appointment::where('doctor_id', $doctor->id)->get();
            Log::info('Appointments found for doctor', ['count' => $appointments->count()]);
        } else {
            Log::info('No doctor profile found for user', ['user_id' => $userId]);
            
            // Fallback: Check appointments by user_id directly (though this might not exist)
            $appointments = \App\Models\Appointment::where('doctor_id', auth()->id())->get();
            Log::info('Appointments by direct user_id', ['count' => $appointments->count()]);
        }
        Log::info('Found appointments for sync', ['count' => $appointments->count(), 'user_id' => $userId]);
        
        if ($appointments->isEmpty()) {
            Log::info('No appointments to sync');
            return response()->json(['success' => true, 'count' => 0, 'message' => 'No appointments to sync']);
        }
        
        $client = new Client();
        $synced = 0;
        $failed = 0;

        foreach ($appointments as $appointment) {
            try {
                // Use helper method for consistent timezone handling
                $startDateTime = $this->formatAppointmentDateForCalendar($appointment->date);
                $endDateTime = $startDateTime->copy()->addHour();
                
                $eventData = [
                    "subject" => $appointment->motif ?? 'Rendez-vous',
                    "start" => [
                        "dateTime" => $startDateTime->format('Y-m-d\TH:i:s.000'), 
                        "timeZone" => $this->getAppTimezone()
                    ],
                    "end" => [
                        "dateTime" => $endDateTime->format('Y-m-d\TH:i:s.000'), 
                        "timeZone" => $this->getAppTimezone()
                    ]
                ];

                Log::info('Timezone conversion debugging - NEW METHOD', [
                    'appointment_id' => $appointment->id,
                    'original_date_from_db' => $appointment->date,
                    'parsed_date_components' => [
                        'year' => $startDateTime->year,
                        'month' => $startDateTime->month,
                        'day' => $startDateTime->day,
                        'hour' => $startDateTime->hour,
                        'minute' => $startDateTime->minute,
                    ],
                    'final_timezone' => $startDateTime->getTimezone()->getName(),
                    'iso8601_output' => $startDateTime->toIso8601String(),
                    'human_readable_time' => $startDateTime->format('Y-m-d H:i:s T'),
                    'app_timezone_config' => config('app.timezone')
                ]);

                Log::info('Syncing appointment with timezone', [
                    'appointment_id' => $appointment->id, 
                    'subject' => $eventData['subject'],
                    'original_date' => $appointment->date,
                    'app_timezone' => config('app.timezone'),
                    'start_time_iso' => $eventData['start']['dateTime'],
                    'end_time_iso' => $eventData['end']['dateTime'],
                    'timezone' => $eventData['start']['timeZone']
                ]);

                // Get user's calendar account to use the selected calendar
                $calendarAccount = auth()->user()->calendarAccounts()->where('provider', 'outlook')->first();
                $calendarId = $calendarAccount ? $calendarAccount->external_calendar_id : null;
                
                $eventsEndpoint = $calendarId 
                    ? "https://graph.microsoft.com/v1.0/me/calendars/{$calendarId}/events"
                    : 'https://graph.microsoft.com/v1.0/me/events';

                $response = $client->post($eventsEndpoint, [
                    'headers' => [
                        'Authorization' => 'Bearer ' . $token,
                        'Content-Type' => 'application/json',
                        'Prefer' => 'outlook.timezone="' . config('app.timezone') . '"',
                    ],
                    'json' => $eventData,
                ]);

                $synced++;
                Log::info('Successfully synced appointment', ['appointment_id' => $appointment->id]);
                
            } catch (\GuzzleHttp\Exception\ClientException $e) {
                $failed++;
                $response = $e->getResponse();
                $statusCode = $response ? $response->getStatusCode() : 'unknown';
                $responseBody = $response ? $response->getBody()->getContents() : 'no response';
                
                Log::error('Outlook API Client Error', [
                    'appointment_id' => $appointment->id ?? 'unknown',
                    'status_code' => $statusCode,
                    'response_body' => $responseBody,
                    'message' => $e->getMessage(),
                    'event_data' => $eventData ?? null
                ]);
            } catch (\GuzzleHttp\Exception\ServerException $e) {
                $failed++;
                $response = $e->getResponse();
                $statusCode = $response ? $response->getStatusCode() : 'unknown';
                $responseBody = $response ? $response->getBody()->getContents() : 'no response';
                
                Log::error('Outlook API Server Error', [
                    'appointment_id' => $appointment->id ?? 'unknown',
                    'status_code' => $statusCode,
                    'response_body' => $responseBody,
                    'message' => $e->getMessage(),
                    'event_data' => $eventData ?? null
                ]);
            } catch (\Exception $e) {
                $failed++;
                Log::error('Sync Outlook Event Failed', [
                    'appointment_id' => $appointment->id ?? 'unknown',
                    'message' => $e->getMessage(),
                    'code' => $e->getCode(),
                    'event_data' => $eventData ?? null
                ]);
            }
        }

        Log::info('Sync completed', ['synced' => $synced, 'failed' => $failed, 'total' => $appointments->count()]);
        
        return response()->json([
            'success' => $synced > 0, 
            'count' => $synced, 
            'failed' => $failed,
            'total' => $appointments->count(),
            'message' => "Synced {$synced} out of {$appointments->count()} appointments"
        ]);
    }

    // Déconnexion Outlook
    public function logoutOutlook()
    {
        try {
            $userId = auth()->id();
            $this->clearTokens($userId);
            
            // Check if this is an AJAX request
            if (request()->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Outlook disconnected successfully'
                ]);
            }
            
            return redirect()->route('doctor.calendar')->with('success', 'Déconnexion réussie');
        } catch (\Exception $e) {
            Log::error('Error disconnecting Outlook', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);
            
            if (request()->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'error' => 'Failed to disconnect Outlook account'
                ], 500);
            }
            
            return redirect()->route('doctor.calendar')->with('error', 'Erreur lors de la déconnexion');
        }
    }

    /**
     * Get list of Outlook calendars for the user
     */
    public function getOutlookCalendars()
    {
        $userId = auth()->id();
        $token = $this->getValidToken($userId);
        
        if (!$token) {
            Log::error('Outlook Calendar token invalid for calendar list');
            return response()->json([]);
        }
        
        try {
            $client = new Client(['timeout' => 30, 'verify' => false]);
            $response = $client->get('https://graph.microsoft.com/v1.0/me/calendars', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $token,
                    'Accept' => 'application/json',
                ],
            ]);
            
            $calendarsData = json_decode($response->getBody(), true);
            $calendars = [];
            
            foreach ($calendarsData['value'] as $calendar) {
                $calendars[] = [
                    'id' => $calendar['id'],
                    'name' => $calendar['name'],
                    'color' => $calendar['color'] ?? 'auto',
                    'isDefaultCalendar' => $calendar['isDefaultCalendar'] ?? false,
                ];
            }
            
            Log::info('Successfully retrieved Outlook calendars', ['count' => count($calendars)]);
            return response()->json($calendars);
            
        } catch (\Exception $e) {
            Log::error('Error retrieving Outlook calendars', [
                'user_id' => $userId,
                'message' => $e->getMessage()
            ]);
            return response()->json([]);
        }
    }

}
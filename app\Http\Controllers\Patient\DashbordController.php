<?php

namespace App\Http\Controllers\Patient;

use App\Http\Controllers\Controller;
use App\Services\AppointmentService\IAppointmentService;
use App\Services\PrescriptionService\IPrescriptionService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Artesaos\SEOTools\Facades\SEOMeta;
use Artesaos\SEOTools\Facades\OpenGraph;
use Artesaos\SEOTools\Facades\JsonLd;
use Illuminate\Http\Request;
use App\Models\Doctor;
use App\Models\CentreMedical;
use App\Services\MedicalCertificateService\IMedicalCertificateService;
use App\Models\PreConsultationForm;



class DashbordController extends Controller
{
    protected $appointmentService;
    protected $prescriptionService;
    protected $medicalCertificateService;

    public function __construct(IAppointmentService $appointmentService, IPrescriptionService $prescriptionService, IMedicalCertificateService $medicalCertificateService)
    {
        $this->appointmentService = $appointmentService;
        $this->prescriptionService = $prescriptionService;
        $this->medicalCertificateService = $medicalCertificateService;

    }

    public function index(Request $request)
    {
        // $appointments = $this->appointmentService->getPatientAppointments(Auth::user()->patient->id);
        $appointments = $this->appointmentService->getPatientAppointments(Auth::user()->patient->id);

    $appointmentsWithUrls = $appointments->map(function ($appointment) {
        $doctor = $appointment->doctor;

        // Create URL-safe strings
        $appointment->doctor_url = sprintf(
            '/doctors/%s/%s/%s-%s',
            strtolower($doctor->speciality->name),
            strtolower($doctor->address->district),
            strtolower($doctor->user->first_name),
            strtolower($doctor->user->last_name)
        );

        return $appointment;
    });    // Check if we're showing the pre-consultation tab
    $showPreConsultation = $request->query('show') === 'pre-consultation';

    // Get pre-consultation forms for this patient if requested
    $preConsultationForms = collect();
    if ($showPreConsultation) {
        $appointmentIds = $appointments->pluck('id')->toArray();
        $preConsultationForms = PreConsultationForm::whereIn('appointment_id', $appointmentIds)
            ->with('appointment.doctor.user', 'appointment.doctor.speciality', 'appointment.doctor.address')
            ->orderBy('created_at', 'desc')->get();

        // Add doctor URLs to each pre-consultation form's appointment
        $preConsultationForms->each(function ($form) {
            if ($form->appointment && $form->appointment->doctor) {
                $doctor = $form->appointment->doctor;

                // Create URL-safe strings for the doctor
                $form->appointment->doctor_url = sprintf(
                    '/doctors/%s/%s/%s-%s',
                    strtolower($doctor->speciality->name ?? 'speciality'),
                    strtolower($doctor->address->district ?? 'district'),
                    strtolower($doctor->user->first_name ?? 'first'),
                    strtolower($doctor->user->last_name ?? 'last')
                );
            }
        });
    }
        // Set SEO meta for the "Mes Rendez-Vous" page
    SEOMeta::setTitle('Mes Rendez-Vous | DocExpress');
    SEOMeta::setDescription('Accédez à vos rendez-vous médicaux et gérez vos consultations en ligne avec DocExpress.');
    SEOMeta::addKeyword([
        'mes rendez-vous',
        'gérer mes rendez-vous',
        'consultations en ligne',
        'rendez-vous médicaux',
        'DocExpress rendez-vous'
    ]);
    SEOMeta::setCanonical(url()->current());
    // Set OpenGraph metadata
    OpenGraph::setTitle('Mes Rendez-Vous | DocExpress');
    OpenGraph::setDescription('Gérez vos rendez-vous médicaux en toute simplicité grâce à DocExpress.');
    OpenGraph::setUrl(url()->current());    // Set JSON-LD metadata
    JsonLd::setTitle('Mes Rendez-Vous | DocExpress');
    JsonLd::setDescription('Consultez et gérez vos rendez-vous médicaux directement sur DocExpress.');
    JsonLd::setUrl(url()->current());

        return view('pages.user-accounts.patient.dashboard', compact('appointments', 'showPreConsultation', 'preConsultationForms'));
    }
    public function getPatientPrescriptions()
    {
        $patient = auth()->user()->patient;
        return view('pages.user-accounts.patient.prescriptions', compact('patient'));
    }
    public function getPatientMaedical_certificates()
    {
        $patient = auth()->user()->patient;
        return view('pages.user-accounts.patient.medical_certificates', compact('patient'));
    }
    public function getPrescrption($presc_id)
    {
        $patient = Auth::user()->patient;
        $prescription = $this->prescriptionService->get($presc_id);
        return view('pages.user-accounts.patient.view-prescriotion',[
            'prescription'=> $prescription,
            'patient'=>$patient
        ]);
    }

     public function getMedical_certificate($certi_id)
    {
        $patient = Auth::user()->patient;
        $medicalCertificate = $this->medicalCertificateService->get($certi_id);
        return view('pages.user-accounts.patient.view-medical-certificate',[
            'medicalCertificate'=> $medicalCertificate,
            'patient'=> $patient
        ]);


    }
    public function rendezvous()
    {
        $patient = auth()->user()->patient;
        return view('pages.user-accounts.patient.rendezvous');
    }
function getrdvByfullnameAndSpecialityAndClinicname(Request $request) {
    try {
        $query = $request->input('query');
        $page = $request->input('page', 1);
        $perPage = 3;

        $doctors = Doctor::with('user', 'speciality', 'address')
            ->whereHas('user', function ($q) use ($query) {
                $q->where('account_status', 1) // Filter by account_status in the user table
                  ->whereRaw("LOWER(CONCAT(first_name, ' ', last_name)) LIKE ?", ['%' . strtolower($query) . '%']);
            })
            ->orWhereHas('speciality', function ($q) use ($query) {
                $q->whereRaw("LOWER(name) LIKE ?", ['%' . strtolower($query) . '%']);
            })
            ->orWhereHas('address', function ($q) use ($query) {
                $q->whereRaw("LOWER(office_name) LIKE ?", ['%' . strtolower($query) . '%']);
            })
            ->paginate($perPage, ['*'], 'page', $page);

        // Transform the paginated data by working with items
        $transformedData = collect($doctors->items())->map(function ($doctor) {
            // Ensure user relationship is loaded
            if (!$doctor->user) {
                return null;
            }

            $doctor->avatar_html = view('components.avatars.doctor-avatar', [
                'image_url' => $doctor->user->image_url,
                'speciality' => $doctor->speciality->image_url ?? null,
                'gender' => $doctor->user->gender,
            ])->render();

            // Convert to array and add appended attributes
            $doctorArray = $doctor->toArray();
            $doctorArray['url_name'] = $doctor->url_name;
            $doctorArray['full_name'] = $doctor->full_name;

            return $doctorArray;
        })->filter()->values();

        $medicalCenters = CentreMedical::whereRaw("LOWER(name) LIKE ?", ['%' . strtolower($query) . '%'])
            ->paginate($perPage, ['*'], 'page', $page);

        return response()->json([
            'doctors' => $transformedData->toArray(),
            'medicalCenters' => $medicalCenters->items()
        ]);

    } catch (\Exception $e) {
        Log::error('Error in patient search: ' . $e->getMessage());
        return response()->json([
            'error' => 'An error occurred while searching',
            'doctors' => [],
            'medicalCenters' => []
        ], 500);
    }
}

}
<?php

namespace App\Http\Controllers\assistanteadministrative;

use App\Http\Controllers\Controller;
use App\Repositories\DocADRepository;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use App\Models\Doctor;

class SelectDoctorController extends Controller
{
    protected $docADRepository;

    public function __construct(DocADRepository $docADRepository)
    {
        $this->docADRepository = $docADRepository;
    }

    public function getDoctorsByAssistant()
    {
        $doctors = $this->docADRepository->get(Auth::user()->assistante->id);
        return view('pages.assistante-administrative.index', compact('doctors'));
    }

    public function selectDoctor(Request $request, $doctorId)
    {
        $doctor = Doctor::find($doctorId);
        if (!$doctor) {
            abort(404, 'Doctor not found');
        }

        $request->session()->put('selected_doctor_id', $doctorId);
        return redirect()->route('assistante.dashboard.doctor', ['doctorId' => $doctorId]);
    }
}
